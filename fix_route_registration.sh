#!/bin/bash

# Quick fix for route registration issue
# This script fixes the duplicate route definitions that prevent automation routes from being registered

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$SCRIPT_DIR"

log "🔧 Applying Route Registration Fix"
log "=" * 50

# Verify we're in the right directory
if [ ! -d "$PROJECT_DIR/saas_infrastructure" ]; then
    error "saas_infrastructure directory not found. Please run this script from the project root."
    exit 1
fi

# Check if the file exists
SAAS_APP_FILE="$PROJECT_DIR/saas_infrastructure/app/saas_app.py"
if [ ! -f "$SAAS_APP_FILE" ]; then
    error "SaaS app file not found: $SAAS_APP_FILE"
    exit 1
fi

# Create backup
BACKUP_FILE="$PROJECT_DIR/saas_app_backup_$(date +%Y%m%d_%H%M%S).py"
cp "$SAAS_APP_FILE" "$BACKUP_FILE"
log "Created backup: $BACKUP_FILE"

# Check if the duplicate routes exist
if grep -q "# Device Bridge API endpoints for standalone services" "$SAAS_APP_FILE"; then
    log "Found duplicate route definitions, removing them..."
    
    # Create a temporary file with the fix
    python3 << 'EOF'
import sys

# Read the file
with open('saas_infrastructure/app/saas_app.py', 'r') as f:
    content = f.read()

# Find the start of the duplicate section
start_marker = "        # Device Bridge API endpoints for standalone services"
end_marker = "    def setup_websockets(self):"

start_pos = content.find(start_marker)
end_pos = content.find(end_marker)

if start_pos != -1 and end_pos != -1:
    # Remove the duplicate section
    new_content = content[:start_pos] + content[end_pos:]
    
    # Write the fixed content
    with open('saas_infrastructure/app/saas_app.py', 'w') as f:
        f.write(new_content)
    
    print("✅ Duplicate routes removed successfully")
else:
    print("❌ Could not find duplicate section markers")
    sys.exit(1)
EOF

    if [ $? -eq 0 ]; then
        log "✅ Route registration fix applied successfully"
    else
        error "❌ Failed to apply route registration fix"
        # Restore backup
        cp "$BACKUP_FILE" "$SAAS_APP_FILE"
        exit 1
    fi
else
    log "ℹ️  No duplicate routes found, fix may already be applied"
fi

# Stop existing services
log "Stopping existing services..."

# Function to stop a service by PID file
stop_service() {
    local pid_file="$1"
    local service_name="$2"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 $pid 2>/dev/null; then
            kill $pid
            log "Stopped $service_name (PID: $pid)"
            sleep 2
        fi
        rm -f "$pid_file"
    fi
}

stop_service "$PROJECT_DIR/saas_service.pid" "SaaS service"
stop_service "$PROJECT_DIR/ios_service.pid" "iOS service"
stop_service "$PROJECT_DIR/android_service.pid" "Android service"

# Kill any remaining processes
pkill -f "start_saas_service.py" || true
pkill -f "start_ios_service.py" || true
pkill -f "start_android_service.py" || true

log "All services stopped"

# Wait a moment
sleep 3

# Start services
log "Starting services with fixed routes..."

# Create log directory if it doesn't exist
mkdir -p "$PROJECT_DIR/logs"

# Start main SaaS service
log "Starting main SaaS service..."
cd "$PROJECT_DIR"
nohup python3 start_saas_service.py > "$PROJECT_DIR/logs/saas_service.log" 2>&1 &
SAAS_PID=$!
echo $SAAS_PID > "$PROJECT_DIR/saas_service.pid"

# Wait for SaaS service to start
sleep 8

# Check if SaaS service is running
if curl -s -f http://localhost:5000/health > /dev/null; then
    log "✅ Main SaaS service started successfully (PID: $SAAS_PID)"
else
    error "❌ Main SaaS service failed to start"
    cat "$PROJECT_DIR/logs/saas_service.log" | tail -20
    exit 1
fi

# Start iOS automation service
log "Starting iOS automation service..."
nohup python3 start_ios_service.py > "$PROJECT_DIR/logs/ios_service.log" 2>&1 &
IOS_PID=$!
echo $IOS_PID > "$PROJECT_DIR/ios_service.pid"

# Start Android automation service
log "Starting Android automation service..."
nohup python3 start_android_service.py > "$PROJECT_DIR/logs/android_service.log" 2>&1 &
ANDROID_PID=$!
echo $ANDROID_PID > "$PROJECT_DIR/android_service.pid"

# Wait for services to start
sleep 5

log "All services started"

# Test route accessibility
log "Testing route accessibility..."
sleep 3

# Test automation routes (should return 401 without auth, not 404)
ios_response=$(curl -s -o /dev/null -w "%{http_code}" -X POST http://localhost:5000/api/automation/ios/access)
android_response=$(curl -s -o /dev/null -w "%{http_code}" -X POST http://localhost:5000/api/automation/android/access)

if [ "$ios_response" = "404" ]; then
    error "❌ iOS automation route still not found (404)"
    log "Checking SaaS service logs:"
    cat "$PROJECT_DIR/logs/saas_service.log" | tail -20
    exit 1
elif [ "$ios_response" = "401" ] || [ "$ios_response" = "403" ]; then
    log "✅ iOS automation route accessible (returns $ios_response - auth required)"
else
    log "ℹ️  iOS automation route returns: $ios_response"
fi

if [ "$android_response" = "404" ]; then
    error "❌ Android automation route still not found (404)"
    exit 1
elif [ "$android_response" = "401" ] || [ "$android_response" = "403" ]; then
    log "✅ Android automation route accessible (returns $android_response - auth required)"
else
    log "ℹ️  Android automation route returns: $android_response"
fi

log "=" * 50
log "🎉 Route Registration Fix Applied Successfully!"
log "=" * 50
log "✅ Duplicate routes removed"
log "✅ Services restarted"
log "✅ Automation routes are now accessible"
log ""
log "Next steps:"
log "1. Run: python3 verify_authentication_fix.py"
log "2. Test the complete authentication flow"
log ""
log "If you need to restore the original file:"
log "cp $BACKUP_FILE $SAAS_APP_FILE"

exit 0
