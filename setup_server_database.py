#!/usr/bin/env python3
"""
Server Database Setup Script
Sets up the database with all required tables and test data
"""

import psycopg2
import psycopg2.extras
import os
import sys
import hashlib
from datetime import datetime, <PERSON><PERSON><PERSON>

def print_status(message, status="INFO"):
    colors = {
        "INFO": "\033[0;32m",
        "WARNING": "\033[1;33m", 
        "ERROR": "\033[0;31m",
        "SUCCESS": "\033[0;32m"
    }
    reset = "\033[0m"
    print(f"{colors.get(status, '')}{message}{reset}")

def hash_password(password):
    """Hash password using SHA-256"""
    return hashlib.sha256(password.encode()).hexdigest()

def setup_database():
    """Set up the complete database schema and test data"""
    
    # Database connection parameters
    db_params = {
        'host': os.environ.get('DB_HOST', 'localhost'),
        'port': os.environ.get('DB_PORT', '5432'),
        'database': os.environ.get('DB_NAME', 'mobile_automation_saas'),
        'user': os.environ.get('DB_USER', 'mobile_automation_app'),
        'password': os.environ.get('DB_PASSWORD', 'secure_password_123')
    }
    
    try:
        print_status("🔌 Connecting to database...", "INFO")
        conn = psycopg2.connect(**db_params)
        conn.autocommit = True
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        print_status("✅ Database connection successful", "SUCCESS")
        
        # Create tables
        print_status("📋 Creating database tables...", "INFO")
        
        # 1. Tenants table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tenants (
                id SERIAL PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                subdomain VARCHAR(100) UNIQUE NOT NULL,
                subscription_tier VARCHAR(50) DEFAULT 'basic',
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        # 2. Users table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id SERIAL PRIMARY KEY,
                tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
                email VARCHAR(255) NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                first_name VARCHAR(100),
                last_name VARCHAR(100),
                role VARCHAR(50) DEFAULT 'user',
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(tenant_id, email)
            );
        """)
        
        # 3. Device bridges table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS device_bridges (
                id SERIAL PRIMARY KEY,
                tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
                bridge_name VARCHAR(255) NOT NULL,
                bridge_token VARCHAR(255) UNIQUE NOT NULL,
                connection_status VARCHAR(50) DEFAULT 'disconnected',
                last_seen TIMESTAMP WITH TIME ZONE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        # 4. Tenant devices table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tenant_devices (
                id SERIAL PRIMARY KEY,
                tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
                bridge_id INTEGER REFERENCES device_bridges(id) ON DELETE SET NULL,
                device_id VARCHAR(255) NOT NULL,
                device_name VARCHAR(255),
                device_model VARCHAR(255),
                platform VARCHAR(20) NOT NULL,
                os_version VARCHAR(50),
                capabilities JSONB DEFAULT '{}',
                status VARCHAR(50) DEFAULT 'available',
                last_seen TIMESTAMP WITH TIME ZONE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(tenant_id, device_id)
            );
        """)
        
        # 5. Automation sessions table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS automation_sessions (
                id SERIAL PRIMARY KEY,
                token TEXT NOT NULL,
                user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
                tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
                platform VARCHAR(20) NOT NULL,
                expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        # 6. Device commands table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS device_commands (
                id SERIAL PRIMARY KEY,
                tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
                device_id VARCHAR(255) NOT NULL,
                bridge_id INTEGER NOT NULL REFERENCES device_bridges(id) ON DELETE CASCADE,
                command_id VARCHAR(255) NOT NULL UNIQUE,
                command_type VARCHAR(100) NOT NULL,
                parameters JSONB DEFAULT '{}',
                status VARCHAR(50) DEFAULT 'pending',
                response JSONB,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                sent_at TIMESTAMP WITH TIME ZONE,
                completed_at TIMESTAMP WITH TIME ZONE,
                error_message TEXT
            );
        """)
        
        # 7. Audit logs table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS audit_logs (
                id SERIAL PRIMARY KEY,
                tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
                user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
                action VARCHAR(100) NOT NULL,
                resource_type VARCHAR(100),
                resource_id VARCHAR(255),
                details JSONB DEFAULT '{}',
                ip_address INET,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        print_status("✅ Database tables created successfully", "SUCCESS")
        
        # Create indexes
        print_status("📊 Creating database indexes...", "INFO")
        
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_tenants_subdomain ON tenants(subdomain);",
            "CREATE INDEX IF NOT EXISTS idx_users_tenant_email ON users(tenant_id, email);",
            "CREATE INDEX IF NOT EXISTS idx_device_bridges_tenant ON device_bridges(tenant_id);",
            "CREATE INDEX IF NOT EXISTS idx_tenant_devices_tenant_platform ON tenant_devices(tenant_id, platform);",
            "CREATE INDEX IF NOT EXISTS idx_automation_sessions_token ON automation_sessions(token);",
            "CREATE INDEX IF NOT EXISTS idx_automation_sessions_expires ON automation_sessions(expires_at);",
            "CREATE INDEX IF NOT EXISTS idx_device_commands_tenant ON device_commands(tenant_id);",
            "CREATE INDEX IF NOT EXISTS idx_device_commands_status ON device_commands(status);",
            "CREATE INDEX IF NOT EXISTS idx_audit_logs_tenant ON audit_logs(tenant_id);"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        print_status("✅ Database indexes created successfully", "SUCCESS")
        
        # Insert test data
        print_status("🧪 Inserting test data...", "INFO")
        
        # Insert test tenants
        test_tenants = [
            ('Test Company 1', 'testcompany1', 'premium'),
            ('Test Company 2', 'testcompany2', 'basic'),
            ('Demo Organization', 'demo', 'enterprise')
        ]
        
        for name, subdomain, tier in test_tenants:
            cursor.execute("""
                INSERT INTO tenants (name, subdomain, subscription_tier)
                VALUES (%s, %s, %s)
                ON CONFLICT (subdomain) DO NOTHING
                RETURNING id;
            """, (name, subdomain, tier))
            
            result = cursor.fetchone()
            if result:
                print_status(f"   Created tenant: {name} ({subdomain})", "INFO")
        
        # Insert test users
        test_users = [
            ('testcompany1', '<EMAIL>', 'testpass123', 'Test', 'User1', 'admin'),
            ('testcompany1', '<EMAIL>', 'password123', 'John', 'Doe', 'user'),
            ('testcompany2', '<EMAIL>', 'testpass123', 'Test', 'User2', 'admin'),
            ('demo', '<EMAIL>', 'demopass123', 'Demo', 'User', 'admin')
        ]
        
        for subdomain, email, password, first_name, last_name, role in test_users:
            # Get tenant ID
            cursor.execute("SELECT id FROM tenants WHERE subdomain = %s", (subdomain,))
            tenant = cursor.fetchone()
            
            if tenant:
                tenant_id = tenant['id']
                password_hash = hash_password(password)
                
                cursor.execute("""
                    INSERT INTO users (tenant_id, email, password_hash, first_name, last_name, role)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    ON CONFLICT (tenant_id, email) DO NOTHING;
                """, (tenant_id, email, password_hash, first_name, last_name, role))
                
                print_status(f"   Created user: {email} for {subdomain}", "INFO")
        
        print_status("✅ Test data inserted successfully", "SUCCESS")
        
        # Clean up expired sessions
        cursor.execute("DELETE FROM automation_sessions WHERE expires_at < NOW();")
        
        cursor.close()
        conn.close()
        
        print_status("🎉 Database setup completed successfully!", "SUCCESS")
        return True
        
    except Exception as e:
        print_status(f"❌ Database setup failed: {e}", "ERROR")
        return False

def main():
    """Main function"""
    print_status("🗄️  SaaS Platform Database Setup", "INFO")
    print_status("=" * 50, "INFO")
    
    # Load environment variables from .env file if it exists
    if os.path.exists('.env'):
        print_status("📄 Loading environment variables from .env file", "INFO")
        with open('.env', 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
    
    success = setup_database()
    
    if success:
        print_status("\n🎉 Database is ready for the SaaS platform!", "SUCCESS")
        print_status("\n📋 Test Credentials:", "INFO")
        print_status("   Tenant: testcompany1", "INFO")
        print_status("   Email: <EMAIL>", "INFO")
        print_status("   Password: testpass123", "INFO")
        sys.exit(0)
    else:
        print_status("\n❌ Database setup failed. Please check the errors above.", "ERROR")
        sys.exit(1)

if __name__ == "__main__":
    main()
