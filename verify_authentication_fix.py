#!/usr/bin/env python3
"""
Authentication Fix Verification Script
Verifies that the authentication flow between SaaS app and automation services works correctly
"""

import os
import sys
import requests
import json
import time
from datetime import datetime

def print_status(message, status="INFO"):
    colors = {
        "INFO": "\033[0;32m",
        "WARNING": "\033[1;33m", 
        "ERROR": "\033[0;31m",
        "SUCCESS": "\033[0;32m"
    }
    reset = "\033[0m"
    print(f"{colors.get(status, '')}{message}{reset}")

def check_service_health(port, service_name):
    """Check if a service is responding on the given port"""
    try:
        # For the main SaaS service, we need to include the tenant header
        headers = {}
        if port == 5000:  # Main SaaS service
            headers['X-Tenant-Subdomain'] = 'testcompany1'

        response = requests.get(f"http://localhost:{port}/health", headers=headers, timeout=5)
        if response.status_code == 200:
            print_status(f"✅ {service_name} is healthy on port {port}", "SUCCESS")
            return True
        else:
            print_status(f"❌ {service_name} health check failed on port {port}: {response.status_code}", "ERROR")
            if response.text:
                print_status(f"   Response: {response.text[:200]}", "INFO")
            return False
    except requests.exceptions.RequestException as e:
        print_status(f"❌ {service_name} is not responding on port {port}: {e}", "ERROR")
        return False

def test_login():
    """Test login to the main SaaS application"""
    print_status("Testing login to main SaaS application...", "INFO")
    
    try:
        response = requests.post('http://localhost:5000/api/auth/login', 
                               headers={
                                   'Content-Type': 'application/json',
                                   'X-Tenant-Subdomain': 'testcompany1'
                               },
                               json={
                                   'email': '<EMAIL>',
                                   'password': 'testpass123'
                               },
                               timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('access_token'):
                print_status("✅ Login successful, access token received", "SUCCESS")
                return data['access_token']
            else:
                print_status("❌ Login response missing access token", "ERROR")
                return None
        else:
            print_status(f"❌ Login failed: {response.status_code} - {response.text}", "ERROR")
            return None
            
    except requests.exceptions.RequestException as e:
        print_status(f"❌ Login request failed: {e}", "ERROR")
        return None

def test_ios_automation_access(access_token):
    """Test iOS automation access"""
    print_status("Testing iOS automation access...", "INFO")

    try:
        response = requests.post('http://localhost:5000/api/automation/ios/access',
                               headers={
                                   'Content-Type': 'application/json',
                                   'Authorization': f'Bearer {access_token}',
                                   'X-Tenant-Subdomain': 'testcompany1'
                               },
                               timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('access_url'):
                print_status("✅ iOS automation access URL generated", "SUCCESS")
                return data['access_url']
            else:
                print_status("❌ iOS automation response missing access URL", "ERROR")
                return None
        else:
            print_status(f"❌ iOS automation access failed: {response.status_code} - {response.text}", "ERROR")
            return None
            
    except requests.exceptions.RequestException as e:
        print_status(f"❌ iOS automation access request failed: {e}", "ERROR")
        return None

def test_android_automation_access(access_token):
    """Test Android automation access"""
    print_status("Testing Android automation access...", "INFO")

    try:
        response = requests.post('http://localhost:5000/api/automation/android/access',
                               headers={
                                   'Content-Type': 'application/json',
                                   'Authorization': f'Bearer {access_token}',
                                   'X-Tenant-Subdomain': 'testcompany1'
                               },
                               timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('access_url'):
                print_status("✅ Android automation access URL generated", "SUCCESS")
                return data['access_url']
            else:
                print_status("❌ Android automation response missing access URL", "ERROR")
                return None
        else:
            print_status(f"❌ Android automation access failed: {response.status_code} - {response.text}", "ERROR")
            return None
            
    except requests.exceptions.RequestException as e:
        print_status(f"❌ Android automation access request failed: {e}", "ERROR")
        return None

def test_automation_page_access(url, platform):
    """Test accessing the automation page with the generated URL"""
    print_status(f"Testing {platform} automation page access...", "INFO")
    
    try:
        response = requests.get(url, allow_redirects=True, timeout=10)
        
        if response.status_code == 200:
            # Check if the response contains expected content
            if "automation" in response.text.lower() or "dashboard" in response.text.lower():
                print_status(f"✅ {platform} automation page accessible and contains expected content", "SUCCESS")
                return True
            else:
                print_status(f"⚠️  {platform} automation page accessible but may not contain expected content", "WARNING")
                return True
        else:
            print_status(f"❌ {platform} automation page access failed: {response.status_code}", "ERROR")
            return False
            
    except requests.exceptions.RequestException as e:
        print_status(f"❌ {platform} automation page request failed: {e}", "ERROR")
        return False

def test_database_connection():
    """Test database connection and schema"""
    print_status("Testing database connection and schema...", "INFO")
    
    try:
        import psycopg2
        
        conn = psycopg2.connect(
            host='localhost',
            port='5432',
            database='mobile_automation_saas',
            user='mobile_automation_app',
            password='secure_password_123'
        )
        
        cursor = conn.cursor()
        
        # Check if required tables exist
        required_tables = ['tenants', 'users', 'automation_sessions']
        for table in required_tables:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = %s
                )
            """, (table,))
            
            if cursor.fetchone()[0]:
                print_status(f"✅ Table '{table}' exists", "SUCCESS")
            else:
                print_status(f"❌ Table '{table}' missing", "ERROR")
                return False
        
        # Check test user exists
        cursor.execute("SELECT COUNT(*) FROM users WHERE email = '<EMAIL>'")
        user_count = cursor.fetchone()[0]
        
        if user_count > 0:
            print_status("✅ Test user exists in database", "SUCCESS")
        else:
            print_status("❌ Test user missing from database", "ERROR")
            return False
        
        cursor.close()
        conn.close()
        
        print_status("✅ Database connection and schema verification successful", "SUCCESS")
        return True
        
    except Exception as e:
        print_status(f"❌ Database verification failed: {e}", "ERROR")
        return False

def main():
    """Main verification function"""
    print_status("🔧 Mobile Automation SaaS Authentication Verification", "INFO")
    print_status("=" * 60, "INFO")
    print_status("📋 This script verifies the authentication flow after deployment.", "INFO")
    print_status("📋 Run this script on the server after executing deploy_authentication_fix.sh", "INFO")
    print_status("=" * 60, "INFO")
    
    # Track overall success
    all_tests_passed = True
    
    # Test 1: Service Health Checks
    print_status("\n1. Service Health Checks", "INFO")
    print_status("-" * 30, "INFO")
    
    saas_healthy = check_service_health(5000, "Main SaaS Service")
    ios_healthy = check_service_health(8080, "iOS Automation Service")
    android_healthy = check_service_health(8081, "Android Automation Service")
    
    if not saas_healthy:
        print_status("❌ Main SaaS service is required for authentication tests", "ERROR")
        all_tests_passed = False
        return False
    
    # Test 2: Database Verification
    print_status("\n2. Database Verification", "INFO")
    print_status("-" * 30, "INFO")
    
    db_ok = test_database_connection()
    if not db_ok:
        all_tests_passed = False
    
    # Test 3: Authentication Flow
    print_status("\n3. Authentication Flow Test", "INFO")
    print_status("-" * 30, "INFO")
    
    # Login test
    access_token = test_login()
    if not access_token:
        print_status("❌ Cannot proceed with automation tests - login failed", "ERROR")
        all_tests_passed = False
        return False
    
    # iOS automation test
    ios_url = test_ios_automation_access(access_token)
    if ios_url:
        ios_page_ok = test_automation_page_access(ios_url, "iOS")
        if not ios_page_ok:
            all_tests_passed = False
    else:
        all_tests_passed = False
    
    # Android automation test
    android_url = test_android_automation_access(access_token)
    if android_url:
        android_page_ok = test_automation_page_access(android_url, "Android")
        if not android_page_ok:
            all_tests_passed = False
    else:
        all_tests_passed = False
    
    # Final Results
    print_status("\n" + "=" * 60, "INFO")
    print_status("VERIFICATION RESULTS", "INFO")
    print_status("=" * 60, "INFO")
    
    if all_tests_passed:
        print_status("🎉 ALL TESTS PASSED! Authentication fix is working correctly.", "SUCCESS")
        print_status("\nUsers should now be able to:", "INFO")
        print_status("1. Login to the tenant dashboard", "INFO")
        print_status("2. Click iOS/Android automation buttons", "INFO")
        print_status("3. Access automation interfaces without authentication errors", "INFO")
        print_status("4. Connect devices and run mobile automation tests", "INFO")
        return True
    else:
        print_status("❌ SOME TESTS FAILED! Authentication fix needs attention.", "ERROR")
        print_status("\nPlease check:", "INFO")
        print_status("1. Service logs for error details", "INFO")
        print_status("2. Database connectivity and schema", "INFO")
        print_status("3. JWT secret key configuration", "INFO")
        print_status("4. Network connectivity between services", "INFO")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
