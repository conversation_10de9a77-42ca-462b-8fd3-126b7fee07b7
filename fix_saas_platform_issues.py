#!/usr/bin/env python3
"""
SaaS Platform Issue Fix Script
Diagnoses and fixes the authentication flow issues
"""

import requests
import subprocess
import time
import os
import sys
import signal
import psutil
from pathlib import Path

def print_status(message, status="INFO"):
    colors = {
        "INFO": "\033[0;32m",
        "WARNING": "\033[1;33m", 
        "ERROR": "\033[0;31m",
        "SUCCESS": "\033[0;32m"
    }
    reset = "\033[0m"
    print(f"{colors.get(status, '')}{message}{reset}")

def check_process_running(port):
    """Check if a process is running on a specific port"""
    for conn in psutil.net_connections():
        if conn.laddr.port == port and conn.status == psutil.CONN_LISTEN:
            return True
    return False

def kill_process_on_port(port):
    """Kill process running on a specific port"""
    for conn in psutil.net_connections():
        if conn.laddr.port == port and conn.status == psutil.CONN_LISTEN:
            try:
                process = psutil.Process(conn.pid)
                print_status(f"Killing process {conn.pid} on port {port}", "WARNING")
                process.terminate()
                time.sleep(2)
                if process.is_running():
                    process.kill()
                return True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
    return False

def check_required_files():
    """Check if all required files exist"""
    print_status("🔍 Checking required files...", "INFO")
    
    required_files = [
        "start_saas_platform.py",
        "saas_infrastructure/app/saas_app.py",
        "saas_infrastructure/services/ios_automation_service.py",
        "saas_infrastructure/services/android_automation_service.py",
        "saas_infrastructure/services/bridge_client.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
            print_status(f"❌ Missing file: {file_path}", "ERROR")
        else:
            print_status(f"✅ Found: {file_path}", "SUCCESS")
    
    return len(missing_files) == 0

def check_python_dependencies():
    """Check if required Python packages are installed"""
    print_status("📦 Checking Python dependencies...", "INFO")
    
    required_packages = [
        'flask',
        'flask_jwt_extended',
        'flask_cors',
        'sqlalchemy',
        'psycopg2',
        'requests',
        'flask_socketio'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print_status(f"✅ {package} is installed", "SUCCESS")
        except ImportError:
            missing_packages.append(package)
            print_status(f"❌ {package} is missing", "ERROR")
    
    if missing_packages:
        print_status("Installing missing packages...", "INFO")
        subprocess.run([sys.executable, "-m", "pip", "install"] + missing_packages)
    
    return len(missing_packages) == 0

def fix_saas_app_endpoints():
    """Fix missing API endpoints in the SaaS app"""
    print_status("🔧 Checking SaaS app API endpoints...", "INFO")
    
    saas_app_path = "saas_infrastructure/app/saas_app.py"
    
    if not os.path.exists(saas_app_path):
        print_status(f"❌ SaaS app file not found: {saas_app_path}", "ERROR")
        return False
    
    # Read the current file
    with open(saas_app_path, 'r') as f:
        content = f.read()
    
    # Check if automation endpoints exist
    if '/api/automation/ios/access' not in content:
        print_status("❌ iOS automation endpoint missing", "ERROR")
        return False
    
    if '/api/automation/android/access' not in content:
        print_status("❌ Android automation endpoint missing", "ERROR")
        return False
    
    print_status("✅ SaaS app endpoints look correct", "SUCCESS")
    return True

def start_services():
    """Start all SaaS platform services"""
    print_status("🚀 Starting SaaS platform services...", "INFO")
    
    # Kill any existing processes on the ports
    ports_to_check = [5000, 8080, 8081]
    for port in ports_to_check:
        if check_process_running(port):
            print_status(f"Port {port} is in use, killing existing process...", "WARNING")
            kill_process_on_port(port)
            time.sleep(2)
    
    # Load environment variables
    env_file = '.env' if os.path.exists('.env') else '/root/.env'
    if os.path.exists(env_file):
        print_status(f"Loading environment from {env_file}", "INFO")
        with open(env_file, 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
    
    # Start the platform
    print_status("Starting platform with start_saas_platform.py...", "INFO")
    
    try:
        # Run the start script in the background
        process = subprocess.Popen([
            sys.executable, "start_saas_platform.py", "run"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for services to start
        print_status("Waiting for services to start...", "INFO")
        time.sleep(15)
        
        # Check if services are running
        services_status = {
            "Main SaaS (5000)": check_process_running(5000),
            "iOS Service (8080)": check_process_running(8080),
            "Android Service (8081)": check_process_running(8081)
        }
        
        all_running = True
        for service, running in services_status.items():
            if running:
                print_status(f"✅ {service} is running", "SUCCESS")
            else:
                print_status(f"❌ {service} is not running", "ERROR")
                all_running = False
        
        if not all_running:
            # Try to get error output
            try:
                stdout, stderr = process.communicate(timeout=5)
                if stderr:
                    print_status(f"Error output: {stderr.decode()}", "ERROR")
            except subprocess.TimeoutExpired:
                pass
        
        return all_running
        
    except Exception as e:
        print_status(f"❌ Failed to start services: {e}", "ERROR")
        return False

def test_api_endpoints():
    """Test the API endpoints that were failing"""
    print_status("🧪 Testing API endpoints...", "INFO")
    
    base_url = "http://localhost:5000"
    
    # Test authentication first
    headers = {
        'X-Tenant-Subdomain': 'testcompany1',
        'Content-Type': 'application/json'
    }
    
    login_data = {
        "email": "<EMAIL>",
        "password": "testpass123"
    }
    
    try:
        # Login to get token
        response = requests.post(
            f"{base_url}/api/auth/login",
            headers=headers,
            json=login_data,
            timeout=10
        )
        
        if response.status_code != 200:
            print_status(f"❌ Login failed: {response.status_code} - {response.text}", "ERROR")
            return False
        
        auth_data = response.json()
        token = auth_data.get('access_token')
        
        if not token:
            print_status("❌ No access token received", "ERROR")
            return False
        
        print_status("✅ Authentication successful", "SUCCESS")
        
        # Test automation endpoints
        auth_headers = {
            'X-Tenant-Subdomain': 'testcompany1',
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        # Test iOS automation endpoint
        ios_response = requests.post(
            f"{base_url}/api/automation/ios/access",
            headers=auth_headers,
            timeout=10
        )
        
        if ios_response.status_code == 200:
            print_status("✅ iOS automation endpoint working", "SUCCESS")
        else:
            print_status(f"❌ iOS automation endpoint failed: {ios_response.status_code}", "ERROR")
        
        # Test Android automation endpoint
        android_response = requests.post(
            f"{base_url}/api/automation/android/access",
            headers=auth_headers,
            timeout=10
        )
        
        if android_response.status_code == 200:
            print_status("✅ Android automation endpoint working", "SUCCESS")
        else:
            print_status(f"❌ Android automation endpoint failed: {android_response.status_code}", "ERROR")
        
        # Test device API
        devices_response = requests.get(
            f"{base_url}/api/devices",
            headers=auth_headers,
            params={'platform': 'ios'},
            timeout=10
        )
        
        if devices_response.status_code == 200:
            print_status("✅ Device API working", "SUCCESS")
        else:
            print_status(f"❌ Device API failed: {devices_response.status_code}", "ERROR")
        
        return True
        
    except Exception as e:
        print_status(f"❌ API endpoint test failed: {e}", "ERROR")
        return False

def main():
    """Main fix function"""
    print_status("🔧 SaaS Platform Issue Fix", "INFO")
    print_status("=" * 50, "INFO")
    
    # Step 1: Check required files
    if not check_required_files():
        print_status("❌ Missing required files. Please ensure all files are present.", "ERROR")
        return False
    
    # Step 2: Check Python dependencies
    check_python_dependencies()
    
    # Step 3: Check SaaS app endpoints
    if not fix_saas_app_endpoints():
        print_status("❌ SaaS app endpoints need to be fixed", "ERROR")
        return False
    
    # Step 4: Start services
    if not start_services():
        print_status("❌ Failed to start all services", "ERROR")
        return False
    
    # Step 5: Test API endpoints
    time.sleep(5)  # Give services more time to fully start
    if not test_api_endpoints():
        print_status("❌ API endpoints are not working correctly", "ERROR")
        return False
    
    print_status("🎉 All issues fixed! Platform should be working now.", "SUCCESS")
    return True

if __name__ == "__main__":
    success = main()
    
    if success:
        print_status("\n🚀 Platform is ready! Run the test again:", "SUCCESS")
        print_status("python3 test_complete_authentication_flow.py", "INFO")
    else:
        print_status("\n❌ Some issues remain. Check the output above.", "ERROR")
    
    sys.exit(0 if success else 1)
