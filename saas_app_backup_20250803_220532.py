#!/usr/bin/env python3
"""
Multi-Tenant SaaS Flask Application for Mobile Automation Platform
Handles tenant isolation, authentication, and device bridge management
"""

import os
import sys
import uuid
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path

from flask import Flask, request, jsonify, session, g, render_template, redirect, url_for, make_response
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager, create_access_token, verify_jwt_in_request, get_jwt_identity, get_jwt
from flask_socketio import SocketIO, emit, join_room, leave_room, disconnect
from flask_cors import CORS
from sqlalchemy import text
import psycopg2.extras
import bcrypt
from werkzeug.middleware.proxy_fix import ProxyFix
from .dashboard_integration import create_dashboard_integration
from .template_adapter import setup_template_adapter
from .device_manager import create_device_manager

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SaaSApp:
    def __init__(self, config=None):
        self.app = Flask(__name__)
        self.setup_config(config)
        self.setup_extensions()
        self.setup_database()
        self.setup_routes()
        self.setup_websockets()
        self.setup_dashboard_integration()
        self.setup_template_adapter()
        self.setup_device_manager()
        
    def setup_config(self, config):
        """Setup Flask application configuration"""
        # Default configuration
        self.app.config.update({
            'SECRET_KEY': os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production'),
            'JWT_SECRET_KEY': os.environ.get('JWT_SECRET_KEY', 'jwt-secret-key-change-in-production'),
            'JWT_ACCESS_TOKEN_EXPIRES': timedelta(hours=24),
            'SQLALCHEMY_DATABASE_URI': self.get_database_uri(),
            'SQLALCHEMY_TRACK_MODIFICATIONS': False,
            'SQLALCHEMY_ENGINE_OPTIONS': {
                'pool_pre_ping': True,
                'pool_recycle': 300,
            }
        })
        
        # Override with custom config if provided
        if config:
            self.app.config.update(config)
    
    def get_database_uri(self):
        """Build database URI from environment variables"""
        # First try to use DATABASE_URL if available
        database_url = os.environ.get('DATABASE_URL')
        if database_url:
            logger.info(f"Using DATABASE_URL: {database_url[:50]}...")  # Log first 50 chars for security
            return database_url

        # Fallback to individual environment variables
        host = os.environ.get('DATABASE_HOST', 'localhost')
        port = os.environ.get('DATABASE_PORT', '5432')
        name = os.environ.get('DATABASE_NAME', 'mobile_automation_saas')
        user = os.environ.get('DATABASE_USER', 'mobile_automation_app')
        password = os.environ.get('DATABASE_PASSWORD', '')

        fallback_uri = f'postgresql://{user}:{password}@{host}:{port}/{name}'
        logger.warning(f"DATABASE_URL not found, using fallback: {fallback_uri[:50]}...")
        return fallback_uri
    
    def setup_extensions(self):
        """Setup Flask extensions"""
        # Database
        self.db = SQLAlchemy(self.app)
        
        # JWT for authentication
        self.jwt = JWTManager(self.app)
        
        # WebSocket support
        self.socketio = SocketIO(
            self.app,
            cors_allowed_origins="*",
            async_mode='threading'
        )
        
        # CORS for API access
        CORS(self.app)
        
        # Proxy fix for deployment behind reverse proxy
        self.app.wsgi_app = ProxyFix(self.app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_prefix=1)
    
    def setup_database(self):
        """Setup database models and context"""
        
        # Tenant context management
        @self.app.before_request
        def set_tenant_context():
            """Set tenant context for each request"""
            g.tenant_id = None
            g.user_id = None
            g.tenant_data = None

            # Skip tenant context for health checks and static files
            if request.endpoint in ['health', 'static']:
                return

            # Extract tenant from subdomain or header
            tenant_subdomain = self.extract_tenant_subdomain()
            logger.info(f"Request to {request.endpoint} with tenant subdomain: {tenant_subdomain}")

            if tenant_subdomain:
                tenant = self.get_tenant_by_subdomain(tenant_subdomain)
                if tenant:
                    g.tenant_id = tenant['id']
                    g.tenant_data = tenant
                    logger.info(f"Tenant context set: {tenant['name']} (ID: {tenant['id']})")

                    # Set PostgreSQL session variables for RLS
                    try:
                        self.db.session.execute(
                            text("SET app.current_tenant = :tenant_id"),
                            {"tenant_id": str(tenant['id'])}
                        )
                    except Exception as e:
                        logger.warning(f"Failed to set PostgreSQL session variable: {e}")
                else:
                    logger.warning(f"Tenant not found for subdomain: {tenant_subdomain}")
            else:
                logger.info(f"No tenant subdomain found in request to {request.endpoint}")
        
        @self.app.teardown_appcontext
        def close_db(error):
            """Close database connection"""
            if hasattr(g, 'db_conn'):
                g.db_conn.close()
    
    def extract_tenant_subdomain(self):
        """Extract tenant subdomain from request"""
        # Try header first (for API requests)
        tenant_subdomain = request.headers.get('X-Tenant-Subdomain')
        
        if not tenant_subdomain:
            # Extract from subdomain
            host = request.host.lower()
            if '.' in host:
                subdomain = host.split('.')[0]
                # Skip common subdomains
                if subdomain not in ['www', 'api', 'admin']:
                    tenant_subdomain = subdomain
        
        return tenant_subdomain
    
    def get_tenant_by_subdomain(self, subdomain):
        """Get tenant information by subdomain"""
        try:
            logger.info(f"Looking up tenant for subdomain: {subdomain}")

            # First try with is_active check
            cursor = self.db.session.execute(
                text("SELECT * FROM tenants WHERE subdomain = :subdomain AND is_active = true"),
                {"subdomain": subdomain}
            )
            result = cursor.fetchone()

            # If not found with is_active=true, try without is_active check (for backwards compatibility)
            if not result:
                logger.info(f"Tenant not found with is_active=true, trying without is_active check")
                cursor = self.db.session.execute(
                    text("SELECT * FROM tenants WHERE subdomain = :subdomain"),
                    {"subdomain": subdomain}
                )
                result = cursor.fetchone()

            if result:
                tenant_dict = dict(result._mapping)
                logger.info(f"Found tenant: {tenant_dict.get('name')} (ID: {tenant_dict.get('id')})")
                return tenant_dict
            else:
                logger.warning(f"No tenant found for subdomain: {subdomain}")
                return None

        except Exception as e:
            logger.error(f"Error fetching tenant for subdomain '{subdomain}': {e}")
            return None
    
    def setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/health')
        def health():
            """Health check endpoint"""
            return jsonify({'status': 'healthy', 'timestamp': datetime.utcnow().isoformat()})
        
        @self.app.route('/')
        def index():
            """Main dashboard route"""
            if not g.tenant_id:
                # Show tenant selection page instead of error
                return self.render_tenant_selection()
            
            # Check if user is authenticated
            try:
                verify_jwt_in_request(optional=True)
                user_id = get_jwt_identity()
                if user_id:
                    # Set user data for dashboard
                    g.user_id = user_id
                    g.user_data = self.get_user_data(user_id)
                    return self.render_dashboard()
            except:
                pass

            # For browser requests, check session authentication
            if self.is_browser_request():
                if self.is_session_authenticated():
                    return self.render_dashboard()

            # Redirect to login
            return self.render_login()

        @self.app.route('/tenant/<subdomain>')
        def tenant_access(subdomain):
            """Direct tenant access route"""
            # Manually set tenant context for this request
            tenant = self.get_tenant_by_subdomain(subdomain)
            if not tenant:
                return jsonify({'error': f'Tenant "{subdomain}" not found'}), 404

            # Set tenant context
            g.tenant_id = tenant['id']
            g.tenant_data = tenant

            # Check if user is authenticated
            try:
                # First try JWT from headers (API requests)
                verify_jwt_in_request(optional=True)
                user_id = get_jwt_identity()
                if user_id:
                    # Set user data for dashboard
                    g.user_id = user_id
                    g.user_data = self.get_user_data(user_id)
                    return self.render_dashboard()
            except:
                pass

            # For browser requests, check session authentication
            if self.is_browser_request():
                if self.is_session_authenticated():
                    return self.render_dashboard()

            # Redirect to login
            return self.render_login()

        @self.app.route('/api/auth/login', methods=['POST'])
        def login():
            """User authentication endpoint"""
            if not g.tenant_id:
                return jsonify({'error': 'Tenant not found'}), 404
            
            data = request.get_json()
            email = data.get('email')
            password = data.get('password')
            
            if not email or not password:
                return jsonify({'error': 'Email and password required'}), 400
            
            user = self.authenticate_user(email, password)
            if not user:
                return jsonify({'error': 'Invalid credentials'}), 401
            
            # Create JWT token
            token = create_access_token(
                identity=str(user['id']),
                additional_claims={
                    'tenant_id': str(g.tenant_id),
                    'tenant_subdomain': g.tenant_data['subdomain'],
                    'user_role': user['role'],
                    'subscription_tier': g.tenant_data['subscription_tier']
                }
            )
            
            # Log audit event
            self.log_audit_event(
                user['id'], 'user_login', 'user', user['id'],
                {'ip_address': request.remote_addr}
            )

            response_data = {
                'access_token': token,
                'user': {
                    'id': user['id'],
                    'email': user['email'],
                    'first_name': user['first_name'],
                    'last_name': user['last_name'],
                    'role': user['role']
                },
                'tenant': {
                    'id': g.tenant_id,
                    'name': g.tenant_data['name'],
                    'subdomain': g.tenant_data['subdomain'],
                    'subscription_tier': g.tenant_data['subscription_tier']
                }
            }

            # For browser requests, use session-based authentication
            if self.is_browser_request():
                # Store user info in session for web browsers
                session['user_id'] = user['id']
                session['tenant_id'] = str(g.tenant_id)
                session['tenant_subdomain'] = g.tenant_data['subdomain']
                session['user_role'] = user['role']
                session['authenticated'] = True



                # For browser requests, return JSON with redirect URL
                response_data['redirect_url'] = f"/tenant/{g.tenant_data['subdomain']}"
                response_data['success'] = True
                return jsonify(response_data)
            else:
                # For API requests, return JSON only
                return jsonify(response_data)
        
        @self.app.route('/api/auth/register', methods=['POST'])
        def register():
            """User registration endpoint (for new tenants)"""
            data = request.get_json()
            
            # Validate required fields
            required_fields = ['email', 'password', 'first_name', 'last_name', 'tenant_name', 'subdomain']
            for field in required_fields:
                if not data.get(field):
                    return jsonify({'error': f'{field} is required'}), 400
            
            try:
                tenant_id, user_id = self.create_tenant_and_user(data)
                
                # Create JWT token
                token = create_access_token(
                    identity=str(user_id),
                    additional_claims={
                        'tenant_id': str(tenant_id),
                        'tenant_subdomain': data['subdomain'],
                        'user_role': 'admin',
                        'subscription_tier': 'starter'
                    }
                )
                
                return jsonify({
                    'access_token': token,
                    'tenant_id': tenant_id,
                    'user_id': user_id,
                    'message': 'Registration successful'
                }), 201
                
            except Exception as e:
                logger.error(f"Registration error: {e}")
                return jsonify({'error': 'Registration failed'}), 500

        @self.app.route('/api/auth/verify', methods=['GET', 'POST'])
        def verify():
            """Verify authentication status"""
            if not g.tenant_id:
                return jsonify({'error': 'Tenant not found'}), 404

            # Try JWT authentication first
            try:
                verify_jwt_in_request(optional=True)
                user_id = get_jwt_identity()
                if user_id:
                    claims = get_jwt()

                    # Verify tenant matches
                    if claims.get('tenant_id') != str(g.tenant_id):
                        return jsonify({'error': 'Invalid tenant access', 'authenticated': False}), 403

                    # Get user data
                    user_data = self.get_user_data(user_id)
                    if not user_data:
                        return jsonify({'error': 'User not found', 'authenticated': False}), 404

                    return jsonify({
                        'authenticated': True,
                        'user': user_data,
                        'tenant': g.tenant_data,
                        'auth_method': 'jwt'
                    })
            except:
                pass

            # Try session authentication for browser requests
            if session.get('authenticated') and session.get('tenant_id') == str(g.tenant_id):
                user_id = session.get('user_id')
                user_data = self.get_user_data(user_id)
                if user_data:
                    return jsonify({
                        'authenticated': True,
                        'user': user_data,
                        'tenant': g.tenant_data,
                        'auth_method': 'session'
                    })

            # No valid authentication found
            return jsonify({'authenticated': False, 'error': 'Not authenticated'}), 401

        @self.app.route('/api/auth/logout', methods=['POST'])
        def logout():
            """User logout endpoint"""
            if self.is_browser_request():
                # For browser requests, clear session and redirect
                session.clear()
                return redirect('/')
            else:
                # For API requests, return success message
                return jsonify({'message': 'Logged out successfully'})

        @self.app.route('/api/devices')
        def list_devices():
            """List devices for current tenant"""
            if not self.require_auth():
                return jsonify({'error': 'Authentication required'}), 401
            
            try:
                cursor = self.db.session.execute(text("""
                    SELECT d.*, b.bridge_name, b.connection_status as bridge_status
                    FROM tenant_devices d
                    LEFT JOIN device_bridges b ON d.bridge_id = b.id
                    WHERE d.tenant_id = :tenant_id
                    ORDER BY d.last_seen DESC
                """), {"tenant_id": g.tenant_id})
                
                devices = [dict(row) for row in cursor.fetchall()]
                
                return jsonify({'devices': devices})
                
            except Exception as e:
                logger.error(f"Error listing devices: {e}")
                return jsonify({'error': 'Failed to list devices'}), 500
        
        @self.app.route('/api/tenant/info')
        def tenant_info():
            """Get tenant information"""
            if not g.tenant_id:
                return jsonify({'error': 'Tenant not found'}), 404

            try:
                return jsonify({
                    'tenant': {
                        'id': g.tenant_id,
                        'name': g.tenant_data['name'],
                        'subdomain': g.tenant_data['subdomain'],
                        'subscription_tier': g.tenant_data['subscription_tier'],
                        'created_at': g.tenant_data['created_at'].isoformat() if g.tenant_data.get('created_at') else None
                    }
                })
            except Exception as e:
                logger.error(f"Error getting tenant info: {e}")
                return jsonify({'error': 'Failed to get tenant info'}), 500

        @self.app.route('/api/')
        def api_root():
            """API root endpoint"""
            return jsonify({
                'message': 'Mobile Automation SaaS API',
                'version': '1.0.0',
                'endpoints': {
                    'auth': {
                        'login': '/api/auth/login',
                        'register': '/api/auth/register'
                    },
                    'tenant': {
                        'info': '/api/tenant/info'
                    },
                    'devices': {
                        'list': '/api/devices',
                        'register_bridge': '/api/bridge/register'
                    },
                    'automation': {
                        'ios_access': '/api/automation/ios/access',
                        'android_access': '/api/automation/android/access'
                    },
                    'health': '/health'
                }
            })

        @self.app.route('/api/automation/ios/access', methods=['POST'])
        def get_ios_access():
            """Generate iOS automation access URL with session token"""
            if not self.require_auth():
                return jsonify({'error': 'Authentication required'}), 401

            try:
                # Generate session token for iOS access
                session_token = self.generate_automation_session_token('ios')

                # Get server configuration
                ios_base_url = os.environ.get('IOS_SERVICE_URL', 'http://localhost:8080')

                # Build access URL
                access_url = f"{ios_base_url}/ios/{g.tenant_data['subdomain']}/?token={session_token}"

                return jsonify({
                    'access_url': access_url,
                    'session_token': session_token,
                    'expires_in': 3600  # 1 hour
                })

            except Exception as e:
                logger.error(f"Error generating iOS access: {e}")
                return jsonify({'error': 'Failed to generate access URL'}), 500

        @self.app.route('/api/automation/android/access', methods=['POST'])
        def get_android_access():
            """Generate Android automation access URL with session token"""
            if not self.require_auth():
                return jsonify({'error': 'Authentication required'}), 401

            try:
                # Generate session token for Android access
                session_token = self.generate_automation_session_token('android')

                # Get server configuration
                android_base_url = os.environ.get('ANDROID_SERVICE_URL', 'http://localhost:8081')

                # Build access URL
                access_url = f"{android_base_url}/android/{g.tenant_data['subdomain']}/?token={session_token}"

                return jsonify({
                    'access_url': access_url,
                    'session_token': session_token,
                    'expires_in': 3600  # 1 hour
                })

            except Exception as e:
                logger.error(f"Error generating Android access: {e}")
                return jsonify({'error': 'Failed to generate access URL'}), 500

        @self.app.route('/api/dashboard/stats', methods=['GET'])
        def get_dashboard_stats():
            """Get dashboard statistics for the current tenant"""
            if not self.require_auth():
                return jsonify({'error': 'Authentication required'}), 401

            try:
                # Get device statistics
                device_stats = self.get_tenant_device_stats()

                # Get test execution statistics
                test_stats = self.get_tenant_test_stats()

                # Get usage statistics
                usage_stats = self.get_tenant_usage_stats()

                return jsonify({
                    'ios': {
                        'devices': device_stats.get('ios_devices', 0),
                        'online': device_stats.get('ios_online', 0),
                        'tests': test_stats.get('ios_tests', 0)
                    },
                    'android': {
                        'devices': device_stats.get('android_devices', 0),
                        'online': device_stats.get('android_online', 0),
                        'tests': test_stats.get('android_tests', 0)
                    },
                    'total': {
                        'devices': device_stats.get('total_devices', 0),
                        'active_tests': test_stats.get('active_tests', 0)
                    },
                    'usage': {
                        'minutes_used': usage_stats.get('minutes_used', 0),
                        'minutes_remaining': usage_stats.get('minutes_remaining', 0)
                    }
                })

            except Exception as e:
                logger.error(f"Error getting dashboard stats: {e}")
                return jsonify({'error': 'Failed to load dashboard statistics'}), 500

        @self.app.route('/api/bridge/register', methods=['POST'])
        def register_bridge():
            """Register a new device bridge"""
            if not self.require_auth():
                return jsonify({'error': 'Authentication required'}), 401

            data = request.get_json()
            bridge_name = data.get('bridge_name', f'Bridge-{uuid.uuid4().hex[:8]}')

            try:
                # Generate bridge token
                bridge_token = str(uuid.uuid4())

                # Insert bridge record
                cursor = self.db.session.execute(text("""
                    INSERT INTO device_bridges (tenant_id, user_id, bridge_token, bridge_name)
                    VALUES (:tenant_id, :user_id, :bridge_token, :bridge_name)
                    RETURNING id, bridge_token
                """), {
                    "tenant_id": g.tenant_id,
                    "user_id": g.user_id,
                    "bridge_token": bridge_token,
                    "bridge_name": bridge_name
                })

                result = cursor.fetchone()
                self.db.session.commit()

                # Log audit event
                self.log_audit_event(
                    g.user_id, 'bridge_registered', 'device_bridge', result['id']
                )

                return jsonify({
                    'bridge_id': result['id'],
                    'bridge_token': result['bridge_token'],
                    'websocket_endpoint': f"wss://{request.host}/bridge",
                    'bridge_name': bridge_name
                })

            except Exception as e:
                logger.error(f"Error registering bridge: {e}")
                self.db.session.rollback()
                return jsonify({'error': 'Failed to register bridge'}), 500
    
    def authenticate_user(self, email, password):
        """Authenticate user credentials"""
        try:
            cursor = self.db.session.execute(text("""
                SELECT * FROM users
                WHERE tenant_id = :tenant_id AND email = :email AND is_active = true
            """), {"tenant_id": g.tenant_id, "email": email})
            
            user = cursor.fetchone()
            if not user:
                return None
            
            user_dict = dict(user._mapping)
            
            # Verify password
            if bcrypt.checkpw(password.encode('utf-8'), user_dict['password_hash'].encode('utf-8')):
                # Update last login
                self.db.session.execute(text("""
                    UPDATE users SET last_login_at = NOW() WHERE id = :user_id
                """), {"user_id": user_dict['id']})
                self.db.session.commit()
                
                return user_dict
            
            return None
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return None
    
    def create_tenant_and_user(self, data):
        """Create new tenant and admin user"""
        try:
            # Check if subdomain is available
            existing = self.get_tenant_by_subdomain(data['subdomain'])
            if existing:
                raise ValueError("Subdomain already taken")
            
            # Create tenant
            tenant_cursor = self.db.session.execute(text("""
                INSERT INTO tenants (name, subdomain, subscription_tier)
                VALUES (:name, :subdomain, :tier)
                RETURNING id
            """), {
                "name": data['tenant_name'],
                "subdomain": data['subdomain'],
                "tier": 'starter'
            })
            
            tenant_id = tenant_cursor.fetchone()['id']
            
            # Hash password
            password_hash = bcrypt.hashpw(
                data['password'].encode('utf-8'), 
                bcrypt.gensalt()
            ).decode('utf-8')
            
            # Create admin user
            user_cursor = self.db.session.execute(text("""
                INSERT INTO users (tenant_id, email, password_hash, first_name, last_name, role, email_verified)
                VALUES (:tenant_id, :email, :password_hash, :first_name, :last_name, :role, :email_verified)
                RETURNING id
            """), {
                "tenant_id": tenant_id,
                "email": data['email'],
                "password_hash": password_hash,
                "first_name": data['first_name'],
                "last_name": data['last_name'],
                "role": 'admin',
                "email_verified": True
            })
            
            user_id = user_cursor.fetchone()['id']
            
            self.db.session.commit()
            
            return tenant_id, user_id
            
        except Exception as e:
            self.db.session.rollback()
            raise e
    
    def require_auth(self):
        """Require JWT authentication and set user context"""
        try:
            verify_jwt_in_request()
            user_id = get_jwt_identity()
            claims = get_jwt()
            
            # Verify tenant matches
            if claims.get('tenant_id') != str(g.tenant_id):
                return False
            
            g.user_id = user_id

            # Get user data
            g.user_data = self.get_user_data(user_id)

            # Set user context for RLS
            self.db.session.execute(
                "SET app.current_user = :user_id",
                {"user_id": user_id}
            )

            return True
            
        except Exception as e:
            logger.error(f"Auth verification error: {e}")
            return False

    def get_user_data(self, user_id):
        """Get user data for the current tenant"""
        try:
            cursor = self.db.session.execute(text("""
                SELECT id, email, first_name, last_name, role, is_active, created_at
                FROM users
                WHERE id = :user_id AND tenant_id = :tenant_id
            """), {
                'user_id': user_id,
                'tenant_id': g.tenant_id
            })

            user_row = cursor.fetchone()
            if user_row:
                return dict(user_row._mapping)
            return None

        except Exception as e:
            logger.error(f"Error getting user data: {e}")
            return None

    def generate_automation_session_token(self, platform):
        """Generate session token for automation service access"""
        try:
            # Create token payload
            payload = {
                'user_id': g.user_id,
                'tenant_id': g.tenant_id,
                'tenant_subdomain': g.tenant_data['subdomain'],
                'platform': platform,
                'issued_at': datetime.utcnow().isoformat(),
                'expires_at': (datetime.utcnow() + timedelta(hours=1)).isoformat()
            }

            # Create JWT token
            token = create_access_token(
                identity=g.user_id,
                additional_claims=payload,
                expires_delta=timedelta(hours=1)
            )

            # Store token in database for validation
            self.db.session.execute(
                text("""
                    INSERT INTO automation_sessions (token, user_id, tenant_id, platform, expires_at)
                    VALUES (:token, :user_id, :tenant_id, :platform, :expires_at)
                """),
                {
                    'token': token,
                    'user_id': g.user_id,
                    'tenant_id': g.tenant_id,
                    'platform': platform,
                    'expires_at': datetime.utcnow() + timedelta(hours=1)
                }
            )
            self.db.session.commit()

            return token

        except Exception as e:
            logger.error(f"Error generating automation session token: {e}")
            self.db.session.rollback()
            raise

    def get_tenant_device_stats(self):
        """Get device statistics for the current tenant"""
        try:
            cursor = self.db.session.execute(text("""
                SELECT
                    platform,
                    COUNT(*) as device_count,
                    COUNT(CASE WHEN last_seen > NOW() - INTERVAL '5 minutes' THEN 1 END) as online_count
                FROM tenant_devices
                WHERE tenant_id = :tenant_id
                GROUP BY platform
            """), {'tenant_id': g.tenant_id})

            stats = {
                'ios_devices': 0,
                'ios_online': 0,
                'android_devices': 0,
                'android_online': 0,
                'total_devices': 0
            }

            for row in cursor:
                platform = row[0].lower()
                device_count = row[1]
                online_count = row[2]

                if platform == 'ios':
                    stats['ios_devices'] = device_count
                    stats['ios_online'] = online_count
                elif platform == 'android':
                    stats['android_devices'] = device_count
                    stats['android_online'] = online_count

                stats['total_devices'] += device_count

            return stats

        except Exception as e:
            logger.error(f"Error getting device stats: {e}")
            return {}

    def get_tenant_test_stats(self):
        """Get test execution statistics for the current tenant"""
        try:
            cursor = self.db.session.execute(text("""
                SELECT
                    platform,
                    COUNT(CASE WHEN status = 'running' THEN 1 END) as running_tests,
                    COUNT(*) as total_tests
                FROM test_executions
                WHERE tenant_id = :tenant_id
                AND created_at > NOW() - INTERVAL '24 hours'
                GROUP BY platform
            """), {'tenant_id': g.tenant_id})

            stats = {
                'ios_tests': 0,
                'android_tests': 0,
                'active_tests': 0
            }

            for row in cursor:
                platform = row[0].lower()
                running_tests = row[1]
                total_tests = row[2]

                if platform == 'ios':
                    stats['ios_tests'] = total_tests
                elif platform == 'android':
                    stats['android_tests'] = total_tests

                stats['active_tests'] += running_tests

            return stats

        except Exception as e:
            logger.error(f"Error getting test stats: {e}")
            return {}

    def get_tenant_usage_stats(self):
        """Get usage statistics for the current tenant"""
        try:
            # Get current billing period usage
            cursor = self.db.session.execute(text("""
                SELECT
                    SUM(CASE WHEN metric_type = 'test_minutes' THEN value ELSE 0 END) as minutes_used
                FROM usage_tracking
                WHERE tenant_id = :tenant_id
                AND billing_period = DATE_TRUNC('month', CURRENT_DATE)
            """), {'tenant_id': g.tenant_id})

            row = cursor.fetchone()
            minutes_used = row[0] if row and row[0] else 0

            # Get tenant subscription limits
            tenant_cursor = self.db.session.execute(text("""
                SELECT subscription_tier FROM tenants WHERE id = :tenant_id
            """), {'tenant_id': g.tenant_id})

            tenant_row = tenant_cursor.fetchone()
            subscription_tier = tenant_row[0] if tenant_row else 'basic'

            # Define tier limits (this could be moved to a configuration table)
            tier_limits = {
                'basic': 1000,
                'professional': 5000,
                'enterprise': 20000
            }

            minutes_limit = tier_limits.get(subscription_tier, 1000)
            minutes_remaining = max(0, minutes_limit - minutes_used)

            return {
                'minutes_used': int(minutes_used),
                'minutes_remaining': int(minutes_remaining),
                'minutes_limit': minutes_limit
            }

        except Exception as e:
            logger.error(f"Error getting usage stats: {e}")
            return {}

    def is_session_authenticated(self):
        """Check if user is authenticated via session (for web browsers)"""
        try:
            # Check if session has authentication info
            if not session.get('authenticated'):
                return False

            # Verify tenant matches current request
            if session.get('tenant_id') != str(g.tenant_id):
                return False

            # Set user context from session
            user_id = session.get('user_id')
            if user_id:
                g.user_id = user_id
                g.user_data = self.get_user_data(user_id)

                # Set user context for RLS
                try:
                    self.db.session.execute(
                        text("SET app.current_user = :user_id"),
                        {"user_id": user_id}
                    )
                except Exception as rls_error:
                    logger.warning(f"Failed to set RLS context: {rls_error}")

                return True

            return False

        except Exception as e:
            logger.error(f"Session authentication error: {e}")
            return False

    def verify_token_from_cookie(self, token):
        """Verify JWT token from cookie and set user context"""
        try:
            import jwt

            logger.info(f"Attempting to verify token: {token[:50]}...")
            logger.info(f"Current tenant_id: {g.tenant_id}")

            # Decode the token using PyJWT directly
            decoded_token = jwt.decode(
                token,
                self.app.config['JWT_SECRET_KEY'],
                algorithms=['HS256']
            )
            user_id = decoded_token['sub']
            claims = decoded_token

            logger.info(f"Decoded token claims: {claims}")

            # Verify tenant matches
            if claims.get('tenant_id') != str(g.tenant_id):
                logger.error(f"Tenant mismatch: token={claims.get('tenant_id')}, current={g.tenant_id}")
                return False

            # Set user context
            g.user_id = user_id
            g.user_data = self.get_user_data(user_id)

            # Set user context for RLS
            self.db.session.execute(
                "SET app.current_user = :user_id",
                {"user_id": user_id}
            )

            logger.info(f"Token verification successful for user: {user_id}")
            return True

        except Exception as e:
            logger.error(f"Cookie token verification error: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    def log_audit_event(self, user_id, action, resource_type=None, resource_id=None, details=None):
        """Log audit event"""
        try:
            self.db.session.execute(text("""
                INSERT INTO audit_logs (tenant_id, user_id, action, resource_type, resource_id, details, ip_address)
                VALUES (:tenant_id, :user_id, :action, :resource_type, :resource_id, :details, :ip_address)
            """), {
                "tenant_id": g.tenant_id,
                "user_id": user_id,
                "action": action,
                "resource_type": resource_type,
                "resource_id": resource_id,
                "details": json.dumps(details or {}),
                "ip_address": request.remote_addr
            })
            self.db.session.commit()
        except Exception as e:
            logger.error(f"Error logging audit event: {e}")

    def is_browser_request(self):
        """Detect if request is from a browser vs API client"""
        accept_header = request.headers.get('Accept', '')
        user_agent = request.headers.get('User-Agent', '')

        # Check if request accepts HTML
        accepts_html = 'text/html' in accept_header

        # Check if it's explicitly an API request (JSON only, no HTML)
        is_api_request = (
            'application/json' in accept_header and 'text/html' not in accept_header
        )

        # Check for common API clients
        api_clients = ['curl', 'postman', 'insomnia', 'httpie', 'python-requests']
        is_api_client = any(client in user_agent.lower() for client in api_clients)

        # Return True if it's a browser request (accepts HTML and not an API client)
        return accepts_html and not is_api_request and not is_api_client

    def render_dashboard(self):
        """Render the main dashboard"""
        if self.is_browser_request():
            # Return HTML template for browser requests
            return render_template('dashboard.html',
                                 tenant=g.tenant_data,
                                 user=getattr(g, 'user_data', None))
        else:
            # Return JSON for API requests
            return jsonify({
                'page': 'dashboard',
                'tenant': g.tenant_data,
                'message': 'Welcome to Mobile Automation Platform'
            })
    
    def render_login(self):
        """Render the login page"""
        if self.is_browser_request():
            # Return HTML template for browser requests
            return render_template('login.html',
                                 tenant=g.tenant_data)
        else:
            # Return JSON for API requests
            return jsonify({
                'page': 'login',
                'tenant': g.tenant_data,
                'message': 'Please log in to continue'
            })

    def render_tenant_selection(self):
        """Render the tenant selection page"""
        try:
            # Get list of available tenants for selection
            cursor = self.db.session.execute(text("""
                SELECT id, name, subdomain, subscription_tier
                FROM tenants
                WHERE is_active = true
                ORDER BY name
            """))
            tenants = [dict(row._mapping) for row in cursor.fetchall()]

            if self.is_browser_request():
                # Return HTML template for browser requests
                return render_template('tenant_selection.html',
                                     available_tenants=tenants)
            else:
                # Return JSON for API requests
                return jsonify({
                    'page': 'tenant_selection',
                    'message': 'Mobile Automation SaaS Platform',
                    'description': 'Please select your organization or use the API with X-Tenant-Subdomain header',
                    'available_tenants': tenants,
                    'api_usage': {
                        'tenant_info': 'curl -H "X-Tenant-Subdomain: testcompany1" http://*************:5000/api/tenant/info',
                        'authentication': 'curl -X POST -H "Content-Type: application/json" -H "X-Tenant-Subdomain: testcompany1" -d \'{"email":"<EMAIL>","password":"testpass123"}\' http://*************:5000/api/auth/login'
                    },
                    'web_access': {
                        'testcompany1': 'http://testcompany1.*************:5000',
                        'testcompany2': 'http://testcompany2.*************:5000'
                    }
                })
        except Exception as e:
            logger.error(f"Error rendering tenant selection: {e}")
            if self.is_browser_request():
                # Return HTML template with empty tenant list
                return render_template('tenant_selection.html',
                                     available_tenants=[],
                                     error='Unable to load tenant list')
            else:
                # Return JSON error for API requests
                return jsonify({
                    'page': 'tenant_selection',
                    'message': 'Mobile Automation SaaS Platform',
                    'description': 'Multi-tenant mobile automation testing platform',
                    'error': 'Unable to load tenant list',
                    'api_usage': {
                        'tenant_info': 'curl -H "X-Tenant-Subdomain: testcompany1" http://*************:5000/api/tenant/info',
                        'authentication': 'curl -X POST -H "Content-Type: application/json" -H "X-Tenant-Subdomain: testcompany1" -d \'{"email":"<EMAIL>","password":"testpass123"}\' http://*************:5000/api/auth/login'
                    }
                })



    def setup_websockets(self):
        """Setup WebSocket handlers for device bridges"""
        
        @self.socketio.on('connect', namespace='/bridge')
        def handle_bridge_connect(auth):
            """Handle device bridge connection"""
            logger.info(f"Bridge connection attempt from {request.remote_addr}")

            # Verify bridge token
            bridge_token = auth.get('bridge_token') if auth else None
            bridge_name = auth.get('bridge_name', 'Unknown Bridge')

            if not bridge_token:
                logger.warning("Bridge connection rejected: No token provided")
                disconnect()
                return False

            # Register bridge with device manager
            connection_info = {
                'session_id': request.sid,
                'remote_addr': request.environ.get('REMOTE_ADDR')
            }

            bridge_data = self.device_manager.register_bridge(
                bridge_token, bridge_name, connection_info
            )

            if not bridge_data:
                logger.warning(f"Bridge connection rejected: Invalid token {bridge_token}")
                disconnect()
                return False

            # Join bridge room
            join_room(f"bridge_{bridge_data['id']}")
            join_room(f"tenant_{bridge_data['tenant_id']}")

            logger.info(f"Bridge {bridge_data['id']} connected successfully")

            # Send confirmation
            emit('bridge_registered', {
                'status': 'success',
                'bridge_id': bridge_data['id'],
                'tenant_id': bridge_data['tenant_id']
            })
        
        @self.socketio.on('disconnect', namespace='/bridge')
        def handle_bridge_disconnect():
            """Handle device bridge disconnection"""
            # Note: We can't easily get the bridge_id here without storing it in session
            # The device manager will handle cleanup of stale connections periodically
            logger.info(f"Bridge disconnected from {request.remote_addr}")

            # Trigger cleanup of stale connections
            self.device_manager.cleanup_stale_connections()
        
        @self.socketio.on('device_update', namespace='/bridge')
        def handle_device_update(data):
            """Handle device list update from bridge"""
            bridge_id = data.get('bridge_id')
            devices = data.get('devices', [])

            if not bridge_id:
                emit('error', {'message': 'Bridge ID required'})
                return

            try:
                # Update devices using device manager
                success = self.device_manager.update_device_list(bridge_id, devices)

                if success:
                    emit('device_update_ack', {'status': 'success'})
                else:
                    emit('error', {'message': 'Failed to update devices'})

            except Exception as e:
                logger.error(f"Error updating devices: {e}")
                emit('error', {'message': 'Failed to update devices'})

        @self.socketio.on('command_response', namespace='/bridge')
        def handle_command_response(data):
            """Handle command response from bridge"""
            bridge_id = data.get('bridge_id')

            if not bridge_id:
                emit('error', {'message': 'Bridge ID required'})
                return

            try:
                # Handle response using device manager
                self.device_manager.handle_command_response(bridge_id, data)
                emit('command_response_ack', {'status': 'success'})

            except Exception as e:
                logger.error(f"Error handling command response: {e}")
                emit('error', {'message': 'Command response handling failed'})
    
    def validate_bridge_token(self, bridge_token):
        """Validate bridge token and return bridge info"""
        try:
            cursor = self.db.session.execute(text("""
                SELECT * FROM device_bridges
                WHERE bridge_token = :token AND is_active = true
            """), {"token": bridge_token})
            
            result = cursor.fetchone()
            return dict(result._mapping) if result else None
            
        except Exception as e:
            logger.error(f"Error validating bridge token: {e}")
            return None
    
    def update_bridge_status(self, bridge_id, status):
        """Update bridge connection status"""
        try:
            self.db.session.execute(text("""
                UPDATE device_bridges
                SET connection_status = :status, last_seen = NOW()
                WHERE id = :bridge_id
            """), {"status": status, "bridge_id": bridge_id})
            self.db.session.commit()
        except Exception as e:
            logger.error(f"Error updating bridge status: {e}")
    
    def update_bridge_devices(self, bridge_id, devices):
        """Update devices for a bridge"""
        try:
            # Get bridge info
            bridge = self.get_bridge_by_id(bridge_id)
            if not bridge:
                return
            
            # Clear existing devices for this bridge
            self.db.session.execute(text("""
                DELETE FROM tenant_devices WHERE bridge_id = :bridge_id
            """), {"bridge_id": bridge_id})

            # Insert new devices
            for device in devices:
                self.db.session.execute(text("""
                    INSERT INTO tenant_devices (
                        tenant_id, bridge_id, device_udid, device_name,
                        device_model, platform, os_version, capabilities
                    ) VALUES (
                        :tenant_id, :bridge_id, :device_udid, :device_name,
                        :device_model, :platform, :os_version, :capabilities
                    )
                """), {
                    "tenant_id": bridge['tenant_id'],
                    "bridge_id": bridge_id,
                    "device_udid": device['udid'],
                    "device_name": device['name'],
                    "device_model": device.get('model'),
                    "platform": device['platform'],
                    "os_version": device.get('os_version'),
                    "capabilities": json.dumps(device.get('capabilities', {}))
                })
            
            # Update device count
            self.db.session.execute(text("""
                UPDATE device_bridges
                SET device_count = :count, last_seen = NOW()
                WHERE id = :bridge_id
            """), {"count": len(devices), "bridge_id": bridge_id})
            
            self.db.session.commit()
            
        except Exception as e:
            logger.error(f"Error updating bridge devices: {e}")
            self.db.session.rollback()
    
    def get_bridge_by_id(self, bridge_id):
        """Get bridge information by ID"""
        try:
            cursor = self.db.session.execute(text("""
                SELECT * FROM device_bridges WHERE id = :bridge_id
            """), {"bridge_id": bridge_id})
            
            result = cursor.fetchone()
            return dict(result._mapping) if result else None

        except Exception as e:
            logger.error(f"Error getting bridge: {e}")
            return None

    def setup_dashboard_integration(self):
        """Setup dashboard integration for existing iOS and Android dashboards"""
        try:
            self.dashboard_integration = create_dashboard_integration(self.app, self.db)
            logger.info("Dashboard integration setup completed")
        except Exception as e:
            logger.error(f"Failed to setup dashboard integration: {e}")

    def setup_template_adapter(self):
        """Setup template adapter for multi-tenant template rendering"""
        try:
            self.template_adapter = setup_template_adapter(self.app)
            logger.info("Template adapter setup completed")
        except Exception as e:
            logger.error(f"Failed to setup template adapter: {e}")

    def setup_device_manager(self):
        """Setup device manager for handling device bridges and communication"""
        try:
            self.device_manager = create_device_manager(self.db, self.socketio)
            logger.info("Device manager setup completed")
        except Exception as e:
            logger.error(f"Failed to setup device manager: {e}")

def create_app(config=None):
    """Application factory"""
    saas_app = SaaSApp(config)
    return saas_app.app, saas_app.socketio

if __name__ == '__main__':
    # Load environment variables from .env file if it exists
    from dotenv import load_dotenv
    load_dotenv()

    app, socketio = create_app()

    # Get configuration from environment
    host = os.environ.get('FLASK_HOST', '0.0.0.0')
    port = int(os.environ.get('FLASK_PORT', os.environ.get('PORT', 5000)))
    debug = os.environ.get('FLASK_DEBUG', 'false').lower() == 'true'

    logger.info(f"Starting SaaS Application on {host}:{port}")
    logger.info(f"Environment: {os.environ.get('ENVIRONMENT', 'development')}")
    logger.info(f"Debug mode: {debug}")

    # Development/Test server
    # For test environment, allow unsafe Werkzeug to avoid production server restrictions
    socketio.run(
        app,
        host=host,
        port=port,
        debug=debug,
        use_reloader=False,  # Disable reloader for production stability
        allow_unsafe_werkzeug=True  # Allow Werkzeug in test environment
    )
