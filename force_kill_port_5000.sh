#!/bin/bash

echo "🔥 Force killing process on port 5000..."

# Method 1: Direct PID kill (from your log, PID 8848)
echo "Trying to kill PID 8848 directly..."
sudo kill -9 8848 2>/dev/null
sleep 1

# Method 2: Find and kill using lsof
echo "Finding processes using port 5000..."
PIDS=$(sudo lsof -ti:5000 2>/dev/null)
if [ -n "$PIDS" ]; then
    echo "Found PIDs: $PIDS"
    for pid in $PIDS; do
        echo "Killing PID $pid..."
        sudo kill -9 $pid 2>/dev/null
    done
else
    echo "No processes found using lsof"
fi

# Method 3: Using fuser
echo "Using fuser to kill processes on port 5000..."
sudo fuser -k 5000/tcp 2>/dev/null

# Method 4: Using netstat and awk
echo "Using netstat method..."
PIDS=$(sudo netstat -tlnp | grep :5000 | awk '{print $7}' | cut -d'/' -f1 | grep -v '-')
if [ -n "$PIDS" ]; then
    echo "Found PIDs via netstat: $PIDS"
    for pid in $PIDS; do
        if [[ "$pid" =~ ^[0-9]+$ ]]; then
            echo "Killing PID $pid..."
            sudo kill -9 $pid 2>/dev/null
        fi
    done
fi

# Method 5: Kill all Python processes (nuclear option)
echo "Killing all Python processes as last resort..."
sudo pkill -9 -f python 2>/dev/null

# Wait and check
sleep 3
echo "Checking if port 5000 is now free..."
if sudo lsof -i:5000 >/dev/null 2>&1; then
    echo "❌ Port 5000 is still in use:"
    sudo lsof -i:5000
    echo ""
    echo "Manual commands to try:"
    echo "sudo systemctl stop apache2"
    echo "sudo systemctl stop nginx"
    echo "sudo systemctl stop lighttpd"
    echo "sudo docker stop \$(sudo docker ps -q)"
else
    echo "✅ Port 5000 is now free!"
fi

echo "Checking other ports..."
for port in 8080 8081; do
    if sudo lsof -i:$port >/dev/null 2>&1; then
        echo "❌ Port $port is in use:"
        sudo lsof -i:$port
        sudo lsof -ti:$port | xargs sudo kill -9 2>/dev/null
    else
        echo "✅ Port $port is free"
    fi
done

echo "Done!"
