#!/usr/bin/env python3
"""
Database Setup Verification Script
Verifies that PostgreSQL permissions are correctly configured
"""

import psycopg2
import psycopg2.extras
import os
import sys
from datetime import datetime

def print_status(message, status="INFO"):
    colors = {
        "INFO": "\033[0;32m",
        "WARNING": "\033[1;33m", 
        "ERROR": "\033[0;31m",
        "SUCCESS": "\033[0;32m"
    }
    reset = "\033[0m"
    print(f"{colors.get(status, '')}{message}{reset}")

def test_database_connection():
    """Test basic database connection"""
    print_status("🔌 Testing database connection...", "INFO")
    
    db_params = {
        'host': os.environ.get('DB_HOST', 'localhost'),
        'port': os.environ.get('DB_PORT', '5432'),
        'database': os.environ.get('DB_NAME', 'mobile_automation_saas'),
        'user': os.environ.get('DB_USER', 'mobile_automation_app'),
        'password': os.environ.get('DB_PASSWORD', 'secure_password_123')
    }
    
    try:
        conn = psycopg2.connect(**db_params)
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # Test basic query
        cursor.execute("SELECT current_database(), current_user, version();")
        result = cursor.fetchone()
        
        print_status(f"✅ Connected to database: {result['current_database']}", "SUCCESS")
        print_status(f"✅ Connected as user: {result['current_user']}", "SUCCESS")
        print_status(f"✅ PostgreSQL version: {result['version'][:50]}...", "SUCCESS")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print_status(f"❌ Database connection failed: {e}", "ERROR")
        return False

def test_schema_permissions():
    """Test schema-level permissions"""
    print_status("🔐 Testing schema permissions...", "INFO")
    
    db_params = {
        'host': os.environ.get('DB_HOST', 'localhost'),
        'port': os.environ.get('DB_PORT', '5432'),
        'database': os.environ.get('DB_NAME', 'mobile_automation_saas'),
        'user': os.environ.get('DB_USER', 'mobile_automation_app'),
        'password': os.environ.get('DB_PASSWORD', 'secure_password_123')
    }
    
    try:
        conn = psycopg2.connect(**db_params)
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # Test schema permissions
        cursor.execute("""
            SELECT 
                has_schema_privilege(current_user, 'public', 'CREATE') as can_create_in_schema,
                has_schema_privilege(current_user, 'public', 'USAGE') as can_use_schema,
                has_database_privilege(current_user, current_database(), 'CREATE') as can_create_objects,
                has_database_privilege(current_user, current_database(), 'CONNECT') as can_connect;
        """)
        
        perms = cursor.fetchone()
        
        if perms['can_create_in_schema']:
            print_status("✅ Can create objects in public schema", "SUCCESS")
        else:
            print_status("❌ Cannot create objects in public schema", "ERROR")
            
        if perms['can_use_schema']:
            print_status("✅ Can use public schema", "SUCCESS")
        else:
            print_status("❌ Cannot use public schema", "ERROR")
            
        if perms['can_create_objects']:
            print_status("✅ Can create database objects", "SUCCESS")
        else:
            print_status("❌ Cannot create database objects", "ERROR")
            
        if perms['can_connect']:
            print_status("✅ Can connect to database", "SUCCESS")
        else:
            print_status("❌ Cannot connect to database", "ERROR")
        
        cursor.close()
        conn.close()
        
        return all(perms.values())
        
    except Exception as e:
        print_status(f"❌ Schema permission test failed: {e}", "ERROR")
        return False

def test_table_operations():
    """Test table creation, insertion, and deletion"""
    print_status("📋 Testing table operations...", "INFO")
    
    db_params = {
        'host': os.environ.get('DB_HOST', 'localhost'),
        'port': os.environ.get('DB_PORT', '5432'),
        'database': os.environ.get('DB_NAME', 'mobile_automation_saas'),
        'user': os.environ.get('DB_USER', 'mobile_automation_app'),
        'password': os.environ.get('DB_PASSWORD', 'secure_password_123')
    }
    
    try:
        conn = psycopg2.connect(**db_params)
        conn.autocommit = True
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # Test table creation
        print_status("   Testing table creation...", "INFO")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS permission_verification_test (
                id SERIAL PRIMARY KEY,
                test_name VARCHAR(100) NOT NULL,
                test_value INTEGER,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            );
        """)
        print_status("   ✅ Table creation successful", "SUCCESS")
        
        # Test index creation
        print_status("   Testing index creation...", "INFO")
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_permission_test_name 
            ON permission_verification_test(test_name);
        """)
        print_status("   ✅ Index creation successful", "SUCCESS")
        
        # Test data insertion
        print_status("   Testing data insertion...", "INFO")
        cursor.execute("""
            INSERT INTO permission_verification_test (test_name, test_value)
            VALUES ('permission_test', %s);
        """, (int(datetime.now().timestamp()),))
        print_status("   ✅ Data insertion successful", "SUCCESS")
        
        # Test data selection
        print_status("   Testing data selection...", "INFO")
        cursor.execute("""
            SELECT COUNT(*) as record_count 
            FROM permission_verification_test 
            WHERE test_name = 'permission_test';
        """)
        result = cursor.fetchone()
        print_status(f"   ✅ Data selection successful - Found {result['record_count']} records", "SUCCESS")
        
        # Test data update
        print_status("   Testing data update...", "INFO")
        cursor.execute("""
            UPDATE permission_verification_test 
            SET test_value = test_value + 1 
            WHERE test_name = 'permission_test';
        """)
        print_status("   ✅ Data update successful", "SUCCESS")
        
        # Test data deletion
        print_status("   Testing data deletion...", "INFO")
        cursor.execute("""
            DELETE FROM permission_verification_test 
            WHERE test_name = 'permission_test';
        """)
        print_status("   ✅ Data deletion successful", "SUCCESS")
        
        # Test table deletion
        print_status("   Testing table deletion...", "INFO")
        cursor.execute("DROP TABLE permission_verification_test;")
        print_status("   ✅ Table deletion successful", "SUCCESS")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print_status(f"❌ Table operations test failed: {e}", "ERROR")
        return False

def test_sequence_operations():
    """Test sequence operations"""
    print_status("🔢 Testing sequence operations...", "INFO")
    
    db_params = {
        'host': os.environ.get('DB_HOST', 'localhost'),
        'port': os.environ.get('DB_PORT', '5432'),
        'database': os.environ.get('DB_NAME', 'mobile_automation_saas'),
        'user': os.environ.get('DB_USER', 'mobile_automation_app'),
        'password': os.environ.get('DB_PASSWORD', 'secure_password_123')
    }
    
    try:
        conn = psycopg2.connect(**db_params)
        conn.autocommit = True
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # Test sequence creation
        cursor.execute("CREATE SEQUENCE IF NOT EXISTS test_sequence START 1;")
        print_status("   ✅ Sequence creation successful", "SUCCESS")
        
        # Test sequence usage
        cursor.execute("SELECT nextval('test_sequence') as next_value;")
        result = cursor.fetchone()
        print_status(f"   ✅ Sequence usage successful - Next value: {result['next_value']}", "SUCCESS")
        
        # Test sequence deletion
        cursor.execute("DROP SEQUENCE test_sequence;")
        print_status("   ✅ Sequence deletion successful", "SUCCESS")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print_status(f"❌ Sequence operations test failed: {e}", "ERROR")
        return False

def main():
    """Main verification function"""
    print_status("🔍 Database Setup Verification", "INFO")
    print_status("=" * 50, "INFO")
    
    # Load environment variables from .env file if it exists
    if os.path.exists('.env'):
        print_status("📄 Loading environment variables from .env file", "INFO")
        with open('.env', 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
    elif os.path.exists('/root/.env'):
        print_status("📄 Loading environment variables from /root/.env file", "INFO")
        with open('/root/.env', 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
    
    tests = [
        ("Database Connection", test_database_connection),
        ("Schema Permissions", test_schema_permissions),
        ("Table Operations", test_table_operations),
        ("Sequence Operations", test_sequence_operations)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print_status(f"\n🧪 Running: {test_name}", "INFO")
        try:
            if test_func():
                passed += 1
                print_status(f"✅ {test_name} PASSED", "SUCCESS")
            else:
                print_status(f"❌ {test_name} FAILED", "ERROR")
        except Exception as e:
            print_status(f"❌ {test_name} ERROR: {e}", "ERROR")
    
    print_status("=" * 50, "INFO")
    print_status(f"🎯 Verification Results: {passed}/{total} tests passed", "INFO")
    
    if passed == total:
        print_status("🎉 ALL TESTS PASSED! Database is ready for the SaaS platform.", "SUCCESS")
        print_status("\n📋 Next Steps:", "INFO")
        print_status("1. Run: python3 setup_server_database.py", "INFO")
        print_status("2. Run: python3 start_saas_platform.py run", "INFO")
        print_status("3. Run: python3 test_complete_authentication_flow.py", "INFO")
        return True
    else:
        print_status(f"⚠️  {total - passed} tests failed. Please check the PostgreSQL permissions.", "WARNING")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
