#!/usr/bin/env python3

import requests
import json
import jwt
import os

def debug_jwt_authentication():
    """Debug JWT authentication step by step"""
    
    print("🔍 JWT Authentication Debug")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    tenant_subdomain = "testcompany1"
    
    # Step 1: Login and get JWT token
    print("1. Getting JWT token...")
    login_response = requests.post(f"{base_url}/api/auth/login",
                                 headers={
                                     'Content-Type': 'application/json',
                                     'X-Tenant-Subdomain': tenant_subdomain
                                 },
                                 json={
                                     'email': '<EMAIL>',
                                     'password': 'testpass123'
                                 })
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code} - {login_response.text}")
        return
    
    login_data = login_response.json()
    access_token = login_data['access_token']
    print(f"✅ Login successful, got token: {access_token[:50]}...")
    
    # Step 2: Decode JWT token to see claims
    print("\n2. Decoding JWT token...")
    try:
        # Decode without verification first to see the claims
        decoded_token = jwt.decode(access_token, options={"verify_signature": False})
        print(f"✅ Token claims: {json.dumps(decoded_token, indent=2)}")
        
        tenant_id_in_token = decoded_token.get('tenant_id')
        user_id_in_token = decoded_token.get('sub')
        print(f"📋 Tenant ID in token: {tenant_id_in_token}")
        print(f"📋 User ID in token: {user_id_in_token}")
        
    except Exception as e:
        print(f"❌ Failed to decode token: {e}")
        return
    
    # Step 3: Test tenant info endpoint
    print("\n3. Testing tenant info endpoint...")
    tenant_response = requests.get(f"{base_url}/api/tenant/info",
                                 headers={
                                     'X-Tenant-Subdomain': tenant_subdomain
                                 })
    
    if tenant_response.status_code == 200:
        tenant_data = tenant_response.json()
        print(f"✅ Tenant info: {json.dumps(tenant_data, indent=2)}")
        actual_tenant_id = tenant_data.get('tenant', {}).get('id')
        print(f"📋 Actual tenant ID from API: {actual_tenant_id}")
        
        # Compare tenant IDs
        if str(tenant_id_in_token) == str(actual_tenant_id):
            print("✅ Tenant IDs match!")
        else:
            print(f"❌ Tenant ID mismatch: token={tenant_id_in_token}, api={actual_tenant_id}")
    else:
        print(f"❌ Tenant info failed: {tenant_response.status_code} - {tenant_response.text}")
    
    # Step 4: Test auth verification endpoint
    print("\n4. Testing auth verification endpoint...")
    verify_response = requests.get(f"{base_url}/api/auth/verify",
                                 headers={
                                     'Authorization': f'Bearer {access_token}',
                                     'X-Tenant-Subdomain': tenant_subdomain
                                 })
    
    if verify_response.status_code == 200:
        verify_data = verify_response.json()
        print(f"✅ Auth verification successful: {json.dumps(verify_data, indent=2)}")
    else:
        print(f"❌ Auth verification failed: {verify_response.status_code} - {verify_response.text}")
    
    # Step 5: Test automation access with detailed debugging
    print("\n5. Testing iOS automation access with debugging...")
    
    # First, test without tenant header
    print("5a. Testing WITHOUT tenant header...")
    ios_response_no_header = requests.post(f"{base_url}/api/automation/ios/access",
                                         headers={
                                             'Content-Type': 'application/json',
                                             'Authorization': f'Bearer {access_token}'
                                         })
    print(f"Response without tenant header: {ios_response_no_header.status_code} - {ios_response_no_header.text}")
    
    # Then, test with tenant header
    print("5b. Testing WITH tenant header...")
    ios_response_with_header = requests.post(f"{base_url}/api/automation/ios/access",
                                           headers={
                                               'Content-Type': 'application/json',
                                               'Authorization': f'Bearer {access_token}',
                                               'X-Tenant-Subdomain': tenant_subdomain
                                           })
    print(f"Response with tenant header: {ios_response_with_header.status_code} - {ios_response_with_header.text}")
    
    # Step 6: Test with different JWT secret
    print("\n6. Testing JWT secret configuration...")
    try:
        # Try to decode with the expected secret
        jwt_secret = os.environ.get('JWT_SECRET_KEY', 'jwt-secret-key-change-in-production')
        print(f"Using JWT secret: {jwt_secret}")
        
        decoded_with_secret = jwt.decode(access_token, jwt_secret, algorithms=['HS256'])
        print(f"✅ Token verified with secret successfully")
        
    except Exception as e:
        print(f"❌ Token verification with secret failed: {e}")
    
    print("\n" + "=" * 50)
    print("🔍 Debug completed")

if __name__ == "__main__":
    debug_jwt_authentication()
