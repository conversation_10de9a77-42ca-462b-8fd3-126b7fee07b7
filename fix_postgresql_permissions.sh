#!/bin/bash

# PostgreSQL Permission Fix Script for SaaS Platform
# Run this script as root on your server to fix database permissions

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_status "🔧 PostgreSQL Permission Fix Script Starting..."
echo "Server: *************"
echo "User: root"
echo "Target Database: mobile_automation_saas"
echo "Target User: mobile_automation_app"
echo "=" * 60

# Step 1: Check if PostgreSQL is installed and running
print_step "1. Checking PostgreSQL installation and status..."

if ! command -v psql &> /dev/null; then
    print_error "PostgreSQL is not installed. Installing..."
    
    # Update package list
    apt update
    
    # Install PostgreSQL
    apt install -y postgresql postgresql-contrib
    
    print_status "PostgreSQL installed successfully"
else
    print_status "PostgreSQL is already installed"
fi

# Check if PostgreSQL is running
if ! systemctl is-active --quiet postgresql; then
    print_warning "PostgreSQL is not running. Starting..."
    systemctl start postgresql
    systemctl enable postgresql
    print_status "PostgreSQL started and enabled"
else
    print_status "PostgreSQL is running"
fi

# Step 2: Check current PostgreSQL version and configuration
print_step "2. Checking PostgreSQL version and configuration..."

PG_VERSION=$(sudo -u postgres psql -t -c "SELECT version();" | head -1)
print_status "PostgreSQL Version: $PG_VERSION"

# Step 3: Check if database and user exist
print_step "3. Checking existing database and user..."

# Check if database exists
DB_EXISTS=$(sudo -u postgres psql -tAc "SELECT 1 FROM pg_database WHERE datname='mobile_automation_saas'")
if [ "$DB_EXISTS" = "1" ]; then
    print_status "Database 'mobile_automation_saas' exists"
else
    print_warning "Database 'mobile_automation_saas' does not exist"
fi

# Check if user exists
USER_EXISTS=$(sudo -u postgres psql -tAc "SELECT 1 FROM pg_roles WHERE rolname='mobile_automation_app'")
if [ "$USER_EXISTS" = "1" ]; then
    print_status "User 'mobile_automation_app' exists"
else
    print_warning "User 'mobile_automation_app' does not exist"
fi

# Step 4: Create database and user with proper permissions
print_step "4. Creating/updating database and user with proper permissions..."

sudo -u postgres psql << 'EOF'
-- Create user if not exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'mobile_automation_app') THEN
        CREATE ROLE mobile_automation_app LOGIN PASSWORD 'secure_password_123';
        RAISE NOTICE 'User mobile_automation_app created';
    ELSE
        RAISE NOTICE 'User mobile_automation_app already exists';
    END IF;
END
$$;

-- Create database if not exists
SELECT 'CREATE DATABASE mobile_automation_saas OWNER mobile_automation_app'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'mobile_automation_saas')\gexec

-- Grant necessary privileges to the user
ALTER USER mobile_automation_app CREATEDB;
ALTER USER mobile_automation_app WITH SUPERUSER;

-- Connect to the database and set up permissions
\c mobile_automation_saas

-- Grant all privileges on database
GRANT ALL PRIVILEGES ON DATABASE mobile_automation_saas TO mobile_automation_app;

-- Grant all privileges on public schema
GRANT ALL ON SCHEMA public TO mobile_automation_app;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO mobile_automation_app;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO mobile_automation_app;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO mobile_automation_app;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO mobile_automation_app;

-- Make sure the user owns the public schema
ALTER SCHEMA public OWNER TO mobile_automation_app;

\q
EOF

print_status "Database and user permissions configured successfully"

# Step 5: Verify permissions
print_step "5. Verifying database permissions..."

# Test connection as the application user
print_status "Testing connection as mobile_automation_app user..."

PGPASSWORD='secure_password_123' psql -h localhost -U mobile_automation_app -d mobile_automation_saas -c "
SELECT 
    current_database() as database,
    current_user as user,
    session_user as session_user,
    has_database_privilege(current_user, current_database(), 'CREATE') as can_create_objects;
"

# Test table creation
print_status "Testing table creation permissions..."

PGPASSWORD='secure_password_123' psql -h localhost -U mobile_automation_app -d mobile_automation_saas -c "
CREATE TABLE IF NOT EXISTS permission_test (
    id SERIAL PRIMARY KEY,
    test_column VARCHAR(50)
);

INSERT INTO permission_test (test_column) VALUES ('test_value');

SELECT * FROM permission_test;

DROP TABLE permission_test;
"

print_status "✅ Permission test completed successfully"

# Step 6: Update PostgreSQL configuration for better connectivity
print_step "6. Updating PostgreSQL configuration..."

# Find PostgreSQL config directory
PG_CONFIG_DIR=$(sudo -u postgres psql -t -c "SHOW config_file;" | xargs dirname)
print_status "PostgreSQL config directory: $PG_CONFIG_DIR"

# Backup original configurations
cp "$PG_CONFIG_DIR/postgresql.conf" "$PG_CONFIG_DIR/postgresql.conf.backup.$(date +%Y%m%d_%H%M%S)"
cp "$PG_CONFIG_DIR/pg_hba.conf" "$PG_CONFIG_DIR/pg_hba.conf.backup.$(date +%Y%m%d_%H%M%S)"

# Update postgresql.conf for better connectivity
print_status "Updating postgresql.conf..."

# Enable listening on all addresses
sed -i "s/#listen_addresses = 'localhost'/listen_addresses = '*'/" "$PG_CONFIG_DIR/postgresql.conf"

# Update pg_hba.conf for local connections
print_status "Updating pg_hba.conf..."

# Add entry for local connections with password authentication
if ! grep -q "host.*mobile_automation_saas.*mobile_automation_app" "$PG_CONFIG_DIR/pg_hba.conf"; then
    echo "host    mobile_automation_saas    mobile_automation_app    127.0.0.1/32    md5" >> "$PG_CONFIG_DIR/pg_hba.conf"
    echo "host    mobile_automation_saas    mobile_automation_app    ::1/128         md5" >> "$PG_CONFIG_DIR/pg_hba.conf"
fi

# Restart PostgreSQL to apply changes
print_status "Restarting PostgreSQL to apply configuration changes..."
systemctl restart postgresql

# Wait for PostgreSQL to start
sleep 5

# Step 7: Final verification
print_step "7. Final verification of database setup..."

# Test connection again
print_status "Final connection test..."

PGPASSWORD='secure_password_123' psql -h localhost -U mobile_automation_app -d mobile_automation_saas -c "
SELECT 
    'Database connection successful' as status,
    current_timestamp as timestamp;
"

print_status "✅ PostgreSQL permissions fixed successfully!"

# Step 8: Create environment file
print_step "8. Creating environment configuration..."

cat > /root/.env << 'EOF'
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=mobile_automation_saas
DB_USER=mobile_automation_app
DB_PASSWORD=secure_password_123

# Service URLs
SAAS_BASE_URL=http://localhost:5000
IOS_SERVICE_URL=http://localhost:8080
ANDROID_SERVICE_URL=http://localhost:8081

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production-$(date +%s)
JWT_ACCESS_TOKEN_EXPIRES=3600

# Server Configuration
SAAS_PORT=5000
IOS_PORT=8080
ANDROID_PORT=8081
EOF

print_status "Environment file created at /root/.env"

echo ""
echo "🎉 PostgreSQL Permission Fix Completed Successfully!"
echo "=" * 60
echo ""
echo "📋 Summary of changes made:"
echo "✅ PostgreSQL installed and running"
echo "✅ Database 'mobile_automation_saas' created"
echo "✅ User 'mobile_automation_app' created with full privileges"
echo "✅ Schema permissions granted"
echo "✅ Connection authentication configured"
echo "✅ Environment file created"
echo ""
echo "🔧 Next steps:"
echo "1. Run: python3 setup_server_database.py"
echo "2. Run: python3 start_saas_platform.py run"
echo "3. Run: python3 test_complete_authentication_flow.py"
echo ""
echo "🔗 Database connection details:"
echo "   Host: localhost"
echo "   Port: 5432"
echo "   Database: mobile_automation_saas"
echo "   User: mobile_automation_app"
echo "   Password: secure_password_123"
