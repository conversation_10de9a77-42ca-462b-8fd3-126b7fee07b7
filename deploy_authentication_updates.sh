#!/bin/bash

# Deployment Script for SaaS Authentication Updates
# Run this script on your server to deploy the authentication flow updates

set -e  # Exit on any error

echo "🚀 Starting SaaS Authentication Updates Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "start_saas_platform.py" ]; then
    print_error "start_saas_platform.py not found. Please run this script from the project root directory."
    exit 1
fi

print_status "Found project root directory"

# 1. Install Python dependencies
print_status "Installing Python dependencies..."
if [ -d "venv" ]; then
    source venv/bin/activate
    print_status "Activated virtual environment"
else
    print_warning "Virtual environment not found. Creating one..."
    python3 -m venv venv
    source venv/bin/activate
    print_status "Created and activated virtual environment"
fi

pip install --upgrade pip
pip install psycopg2-binary flask flask-jwt-extended flask-cors sqlalchemy requests flask-socketio

print_status "Python dependencies installed successfully"

# 2. Set up environment variables
print_status "Setting up environment variables..."

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    cat > .env << EOF
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=mobile_automation_saas
DB_USER=mobile_automation_app
DB_PASSWORD=secure_password_123

# Service URLs
SAAS_BASE_URL=http://localhost:5000
IOS_SERVICE_URL=http://localhost:8080
ANDROID_SERVICE_URL=http://localhost:8081

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ACCESS_TOKEN_EXPIRES=3600

# Server Configuration
SAAS_PORT=5000
IOS_PORT=8080
ANDROID_PORT=8081
EOF
    print_status "Created .env file with default configuration"
else
    print_status ".env file already exists"
fi

# 3. Set up PostgreSQL database
print_status "Setting up PostgreSQL database..."

# Check if PostgreSQL is running
if ! pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
    print_warning "PostgreSQL is not running. Attempting to start..."
    
    # Try different ways to start PostgreSQL
    if command -v systemctl >/dev/null 2>&1; then
        sudo systemctl start postgresql
    elif command -v service >/dev/null 2>&1; then
        sudo service postgresql start
    elif command -v brew >/dev/null 2>&1; then
        brew services start postgresql@14 || brew services start postgresql
    else
        print_error "Could not start PostgreSQL. Please start it manually."
        exit 1
    fi
    
    # Wait for PostgreSQL to start
    sleep 5
    
    if ! pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
        print_error "PostgreSQL failed to start. Please start it manually and run this script again."
        exit 1
    fi
fi

print_status "PostgreSQL is running"

# 4. Create database and user
print_status "Creating database and user..."

# Create database and user (this might fail if they already exist, which is fine)
sudo -u postgres psql -c "CREATE DATABASE mobile_automation_saas;" 2>/dev/null || true
sudo -u postgres psql -c "CREATE USER mobile_automation_app WITH PASSWORD 'secure_password_123';" 2>/dev/null || true
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE mobile_automation_saas TO mobile_automation_app;" 2>/dev/null || true

print_status "Database setup completed"

# 5. Run database migrations
print_status "Running database migrations..."

python3 -c "
import psycopg2
import os
from pathlib import Path

# Load environment variables
if os.path.exists('.env'):
    with open('.env', 'r') as f:
        for line in f:
            if '=' in line and not line.startswith('#'):
                key, value = line.strip().split('=', 1)
                os.environ[key] = value

try:
    conn = psycopg2.connect(
        host=os.environ.get('DB_HOST', 'localhost'),
        port=os.environ.get('DB_PORT', '5432'),
        database=os.environ.get('DB_NAME', 'mobile_automation_saas'),
        user=os.environ.get('DB_USER', 'mobile_automation_app'),
        password=os.environ.get('DB_PASSWORD', 'secure_password_123')
    )
    
    cursor = conn.cursor()
    
    # Create automation_sessions table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS automation_sessions (
            id SERIAL PRIMARY KEY,
            token TEXT NOT NULL,
            user_id INTEGER NOT NULL,
            tenant_id INTEGER NOT NULL,
            platform VARCHAR(20) NOT NULL,
            expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
    ''')
    
    # Create indexes
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_automation_sessions_token ON automation_sessions(token);')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_automation_sessions_user_tenant ON automation_sessions(user_id, tenant_id);')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_automation_sessions_expires ON automation_sessions(expires_at);')
    
    conn.commit()
    print('✅ Database tables created successfully')
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f'❌ Database error: {e}')
    exit(1)
"

print_status "Database migrations completed"

# 6. Test the platform startup
print_status "Testing platform startup..."

# Check if all required files exist
required_files=(
    "start_saas_platform.py"
    "saas_infrastructure/app/saas_app.py"
    "saas_infrastructure/services/ios_automation_service.py"
    "saas_infrastructure/services/android_automation_service.py"
    "saas_infrastructure/services/bridge_client.py"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        print_error "Required file not found: $file"
        exit 1
    fi
done

print_status "All required files found"

# 7. Syntax check
print_status "Performing syntax checks..."

python3 -m py_compile start_saas_platform.py
python3 -m py_compile saas_infrastructure/app/saas_app.py
python3 -m py_compile saas_infrastructure/services/ios_automation_service.py
python3 -m py_compile saas_infrastructure/services/android_automation_service.py
python3 -m py_compile saas_infrastructure/services/bridge_client.py

print_status "Syntax checks passed"

# 8. Create test script
print_status "Creating test script..."

cat > test_authentication_flow.py << 'EOF'
#!/usr/bin/env python3
"""
Test script for SaaS authentication flow
"""

import requests
import time
import json
import sys

def test_authentication_flow():
    """Test the complete authentication flow"""
    
    base_url = "http://localhost:5000"
    
    print("🧪 Testing SaaS Authentication Flow...")
    
    # Test 1: Health check
    print("\n1. Testing health endpoint...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Health check passed")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False
    
    # Test 2: Tenant info
    print("\n2. Testing tenant info...")
    try:
        headers = {'X-Tenant-Subdomain': 'testcompany1'}
        response = requests.get(f"{base_url}/api/tenant/info", headers=headers, timeout=5)
        if response.status_code == 200:
            print("✅ Tenant info endpoint working")
        else:
            print(f"❌ Tenant info failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Tenant info failed: {e}")
    
    # Test 3: iOS service
    print("\n3. Testing iOS service...")
    try:
        response = requests.get("http://localhost:8080/health", timeout=5)
        if response.status_code == 200:
            print("✅ iOS service is running")
        else:
            print(f"❌ iOS service failed: {response.status_code}")
    except Exception as e:
        print(f"❌ iOS service failed: {e}")
    
    # Test 4: Android service
    print("\n4. Testing Android service...")
    try:
        response = requests.get("http://localhost:8081/health", timeout=5)
        if response.status_code == 200:
            print("✅ Android service is running")
        else:
            print(f"❌ Android service failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Android service failed: {e}")
    
    print("\n🎉 Authentication flow test completed!")
    return True

if __name__ == "__main__":
    test_authentication_flow()
EOF

chmod +x test_authentication_flow.py

print_status "Test script created"

# 9. Final instructions
print_status "Deployment completed successfully! 🎉"

echo ""
echo "📋 Next Steps:"
echo "1. Start the platform: python3 start_saas_platform.py run"
echo "2. Test the authentication: python3 test_authentication_flow.py"
echo "3. Access the platform at: http://localhost:5000"
echo ""
echo "🔧 Configuration:"
echo "- Main SaaS app: http://localhost:5000"
echo "- iOS automation: http://localhost:8080"
echo "- Android automation: http://localhost:8081"
echo ""
echo "📝 Test tenant access:"
echo "- URL: http://testcompany1.localhost:5000"
echo "- Or use: curl -H 'X-Tenant-Subdomain: testcompany1' http://localhost:5000/api/tenant/info"

print_status "Ready to test! 🚀"
