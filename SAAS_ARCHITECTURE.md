# Mobile Automation SaaS Platform - New Architecture

## Overview

This document describes the new simplified architecture for the Mobile Automation SaaS Platform that replaces the problematic iframe-based approach with standalone services and direct browser access.

## Architecture Components

### 1. Main SaaS Application (`saas_infrastructure/app/saas_app.py`)
- **Port**: 5000 (configurable via `SAAS_PORT`)
- **Purpose**: Multi-tenant dashboard, authentication, tenant management
- **Features**:
  - Tenant authentication and session management
  - Simplified dashboard with iOS/Android access buttons
  - API endpoints for generating automation access URLs
  - Device bridge management and WebSocket communication
  - Usage tracking and billing

### 2. iOS Automation Service (`saas_infrastructure/services/ios_automation_service.py`)
- **Port**: 8080 (configurable via `IOS_SERVICE_PORT`)
- **Purpose**: Standalone iOS automation interface
- **Features**:
  - Tenant-aware iOS automation interface
  - Session token authentication
  - Integration with original iOS automation app
  - Tenant-scoped device management

### 3. Android Automation Service (`saas_infrastructure/services/android_automation_service.py`)
- **Port**: 8081 (configurable via `ANDROID_SERVICE_PORT`)
- **Purpose**: Standalone Android automation interface
- **Features**:
  - Tenant-aware Android automation interface
  - Session token authentication
  - Integration with original Android automation app
  - Tenant-scoped device management

## Key Improvements

### 1. Eliminated Iframe Complexity
- **Before**: Complex iframe integration with path rewriting and template modification
- **After**: Direct browser access to standalone services
- **Benefits**: No cross-frame communication issues, cleaner URLs, better user experience

### 2. Simplified Authentication Flow
- **Before**: Complex session sharing between iframe and parent
- **After**: JWT-based session tokens for service access
- **Benefits**: Secure, stateless authentication, easier to debug

### 3. Better Multi-Tenant Support
- **Before**: Single Flask instances trying to handle multiple tenants
- **After**: Tenant-aware services with URL-based tenant identification
- **Benefits**: True tenant isolation, concurrent access support

## URL Structure

### Main SaaS Dashboard
```
http://localhost:5000/<tenant-subdomain>/dashboard
```

### iOS Automation (Direct Access)
```
http://localhost:8080/ios/<tenant-subdomain>/?token=<session-token>
```

### Android Automation (Direct Access)
```
http://localhost:8081/android/<tenant-subdomain>/?token=<session-token>
```

## Authentication Flow

1. User logs into main SaaS dashboard
2. User clicks "Open iOS Automation" or "Open Android Automation"
3. JavaScript calls `/api/automation/ios/access` or `/api/automation/android/access`
4. SaaS app generates JWT session token with tenant context
5. Browser opens new window with automation service URL + token
6. Automation service validates token and establishes session
7. User accesses full automation interface in dedicated window

## Database Schema Updates

### New Table: `automation_sessions`
```sql
CREATE TABLE automation_sessions (
    id SERIAL PRIMARY KEY,
    token TEXT NOT NULL UNIQUE,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    platform VARCHAR(20) NOT NULL CHECK (platform IN ('ios', 'android')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_used_at TIMESTAMP WITH TIME ZONE
);
```

## Startup and Management

### Quick Start
```bash
# Start the entire platform
python start_saas_platform.py run

# Or start components individually
python start_saas_platform.py start
```

### Service Management
```bash
# Check service health
python start_saas_platform.py health

# Manage automation services specifically
python saas_infrastructure/services/service_manager.py status
python saas_infrastructure/services/service_manager.py health
```

### Environment Variables
```bash
# Main SaaS Application
export SAAS_PORT=5000
export FLASK_ENV=production

# Automation Services
export IOS_SERVICE_PORT=8080
export ANDROID_SERVICE_PORT=8081

# Database Configuration
export DATABASE_URL=postgresql://user:pass@host:port/dbname
# OR individual variables:
export DATABASE_HOST=localhost
export DATABASE_PORT=5432
export DATABASE_NAME=mobile_automation_saas
export DATABASE_USER=mobile_automation_app
export DATABASE_PASSWORD=your_password

# Security
export SECRET_KEY=your-secret-key
export JWT_SECRET_KEY=your-jwt-secret-key
```

## Development vs Production

### Development
- Services run on localhost with different ports
- Direct access to automation interfaces for debugging
- Simplified authentication for testing

### Production
- Services behind reverse proxy (nginx/Apache)
- SSL termination at proxy level
- Domain-based routing instead of port-based

### Example Production Setup
```nginx
# Main SaaS App
server {
    listen 443 ssl;
    server_name yourdomain.com;
    location / {
        proxy_pass http://localhost:5000;
    }
}

# iOS Automation Service
server {
    listen 443 ssl;
    server_name ios.yourdomain.com;
    location / {
        proxy_pass http://localhost:8080;
    }
}

# Android Automation Service
server {
    listen 443 ssl;
    server_name android.yourdomain.com;
    location / {
        proxy_pass http://localhost:8081;
    }
}
```

## Migration from Old Architecture

### Steps to Migrate
1. **Backup existing data**: Export tenant and user data
2. **Update database schema**: Run new migration scripts
3. **Deploy new services**: Start new automation services
4. **Update DNS/routing**: Point to new service endpoints
5. **Test thoroughly**: Verify all functionality works
6. **Remove old components**: Clean up iframe integration code

### Rollback Plan
- Keep old iframe-based dashboard as backup
- Database schema is backward compatible
- Can switch routing back to old system if needed

## Testing

### Unit Tests
```bash
# Test individual services
python -m pytest saas_infrastructure/tests/

# Test authentication flow
python -m pytest saas_infrastructure/tests/test_auth.py

# Test automation services
python -m pytest saas_infrastructure/tests/test_automation_services.py
```

### Integration Tests
```bash
# Test complete workflow
python -m pytest saas_infrastructure/tests/test_integration.py

# Test multi-tenant scenarios
python -m pytest saas_infrastructure/tests/test_multi_tenant.py
```

### Manual Testing Checklist
- [ ] User can log into tenant dashboard
- [ ] iOS automation opens in new window
- [ ] Android automation opens in new window
- [ ] Device management works in both platforms
- [ ] Test execution works correctly
- [ ] Multiple tenants can access simultaneously
- [ ] Session tokens expire correctly
- [ ] All original automation features preserved

## Troubleshooting

### Common Issues

1. **Services won't start**
   - Check port availability: `netstat -tulpn | grep :8080`
   - Verify database connection
   - Check environment variables

2. **Authentication failures**
   - Verify JWT secret keys match
   - Check token expiration times
   - Validate database session records

3. **Automation interface not loading**
   - Check automation service logs
   - Verify original app integration
   - Test direct service access

### Logs
```bash
# View all service logs
tail -f logs/saas_app.log
tail -f logs/ios_automation.log
tail -f logs/android_automation.log

# Or use service manager
python saas_infrastructure/services/service_manager.py status
```

## Future Enhancements

1. **Load Balancing**: Multiple instances of automation services
2. **Auto-scaling**: Dynamic service scaling based on usage
3. **Monitoring**: Comprehensive health monitoring and alerting
4. **API Gateway**: Centralized API management and rate limiting
5. **Microservices**: Further decomposition into smaller services
