# Hybrid Cloud Deployment Strategy: Local Devices + Cloud Web App

## Executive Summary

This updated deployment strategy focuses on a cost-effective hybrid architecture where users access our Flask-based mobile automation testing platform through a cloud-hosted web application while connecting their own physical iOS and Android devices locally. This approach eliminates expensive cloud device farm costs while maintaining professional SaaS capabilities.

## 1. Architecture Overview

### 1.1 Hybrid Architecture Components

```
┌─────────────────────────────────────────────────────────────────┐
│                    HYBRID ARCHITECTURE                          │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    Secure Tunnel    ┌─────────────────┐   │
│  │   Cloud Web     │◄──────────────────►│  Local Device   │   │
│  │   Application   │                     │     Bridge      │   │
│  │                 │                     │                 │   │
│  │ • Flask UI      │                     │ • iOS Devices  │   │
│  │ • User Auth     │                     │ • Android       │   │
│  │ • Test Mgmt     │                     │ • ADB/idevice   │   │
│  │ • Reporting     │                     │ • Local Appium  │   │
│  └─────────────────┘                     └─────────────────┘   │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 Key Benefits

- **Cost Reduction**: 80-90% lower infrastructure costs vs cloud device farms
- **Device Ownership**: Users control their own devices and data
- **Performance**: Direct local device access with minimal latency
- **Privacy**: Sensitive app data never leaves user's environment
- **Flexibility**: Support for any iOS/Android device user owns

## 2. Secure Tunneling Solutions Analysis

### 2.1 Tunneling Technology Comparison

| Solution | Monthly Cost | Bandwidth | Concurrent Connections | Security | Ease of Setup |
|----------|-------------|-----------|----------------------|----------|---------------|
| **Cloudflare Tunnel** | Free | Unlimited | Unlimited | TLS 1.3 | Excellent |
| **ngrok** | $8-25/month | 1GB-10GB | 1-20 | TLS 1.3 | Good |
| **Tailscale** | Free-$6/user | Unlimited | 100 devices | WireGuard | Excellent |
| **Custom WebSocket** | $0 | Unlimited | Custom | TLS 1.3 | Complex |
| **SSH Tunnel** | $0 | Unlimited | Limited | SSH | Moderate |

### 2.2 Recommended Solution: Cloudflare Tunnel

**Why Cloudflare Tunnel:**
- **Free Tier**: No cost for basic usage
- **Zero Configuration**: No port forwarding or firewall changes
- **Enterprise Security**: Built-in DDoS protection and TLS encryption
- **Global Network**: 300+ edge locations for low latency
- **Reliability**: 99.99% uptime SLA

#### **Cloudflare Tunnel Implementation**
```bash
# Install cloudflared on user's machine
curl -L --output cloudflared.deb https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64.deb
sudo dpkg -i cloudflared.deb

# Authenticate with Cloudflare
cloudflared tunnel login

# Create tunnel for device bridge
cloudflared tunnel create mobile-automation-bridge

# Configure tunnel
cat > ~/.cloudflared/config.yml << EOF
tunnel: mobile-automation-bridge
credentials-file: ~/.cloudflared/mobile-automation-bridge.json

ingress:
  - hostname: bridge-{user-id}.yourdomain.com
    service: http://localhost:9999
  - service: http_status:404
EOF

# Run tunnel
cloudflared tunnel run mobile-automation-bridge
```

## 3. Minimal Cloud Infrastructure

### 3.1 Simplified Cloud Architecture

#### **Core Components (Minimal Setup)**
- **Web Application**: Single Flask app serving all users
- **Database**: PostgreSQL for user management and test results
- **Authentication**: JWT-based user authentication
- **File Storage**: Object storage for test artifacts
- **Load Balancer**: Basic HTTP load balancer

#### **No Longer Needed**
- ❌ Cloud device farms (AWS Device Farm, BrowserStack)
- ❌ Complex Kubernetes orchestration
- ❌ Device management services
- ❌ WebRTC infrastructure
- ❌ VPN servers
- ❌ Redis clusters

### 3.2 Cost-Optimized Hosting Comparison

#### **Ultra-Low-Cost Providers (Recommended)**

| Provider | Instance Type | Specs | Monthly Cost | Annual Cost |
|----------|---------------|-------|--------------|-------------|
| **Hetzner** | CX21 | 2 vCPU, 4GB RAM | €4.15 ($4.50) | $54 |
| **Vultr** | Regular | 1 vCPU, 2GB RAM | $6.00 | $72 |
| **DigitalOcean** | Basic | 1 vCPU, 2GB RAM | $12.00 | $144 |
| **Linode** | Nanode | 1 vCPU, 1GB RAM | $5.00 | $60 |

#### **Database Options**

| Provider | Database Type | Specs | Monthly Cost |
|----------|---------------|-------|--------------|
| **Hetzner** | Managed PostgreSQL | 1 vCPU, 2GB | €10 ($11) |
| **PlanetScale** | Serverless MySQL | 1GB storage | $0-29 |
| **Supabase** | PostgreSQL | 500MB | Free-$25 |
| **Railway** | PostgreSQL | 1GB | $5 |

### 3.3 Recommended Minimal Setup

#### **Total Monthly Infrastructure Cost: $15-25**

**Hetzner CX21 + Managed Database:**
- **Web Server**: Hetzner CX21 - $4.50/month
- **Database**: Hetzner PostgreSQL - $11/month  
- **Object Storage**: Hetzner Storage Box 100GB - $3.50/month
- **Domain & SSL**: Cloudflare - Free
- **Total**: **$19/month** ($228/year)

## 4. Local Device Bridge Architecture

### 4.1 Enhanced Device Bridge Service

#### **Lightweight Bridge Application (device_bridge.py)**
```python
import asyncio
import websockets
import json
import subprocess
import ssl
import logging
from cryptography.fernet import Fernet
import uuid

class LocalDeviceBridge:
    def __init__(self, user_token, cloud_endpoint):
        self.user_token = user_token
        self.cloud_endpoint = cloud_endpoint
        self.bridge_id = str(uuid.uuid4())
        self.devices = {}
        self.websocket = None
        
    async def start_bridge(self):
        """Start the local device bridge"""
        logging.info("Starting Local Device Bridge...")
        
        # Start device discovery
        asyncio.create_task(self.device_discovery_loop())
        
        # Connect to cloud
        await self.connect_to_cloud()
        
        # Start command processing
        await self.process_cloud_commands()
    
    async def connect_to_cloud(self):
        """Establish WebSocket connection to cloud"""
        headers = {
            'Authorization': f'Bearer {self.user_token}',
            'X-Bridge-ID': self.bridge_id
        }
        
        try:
            self.websocket = await websockets.connect(
                f"wss://{self.cloud_endpoint}/bridge",
                extra_headers=headers,
                ssl=ssl.create_default_context()
            )
            
            # Register bridge
            await self.register_bridge()
            logging.info("Connected to cloud successfully")
            
        except Exception as e:
            logging.error(f"Failed to connect to cloud: {e}")
            raise
    
    async def device_discovery_loop(self):
        """Continuously discover connected devices"""
        while True:
            try:
                # Discover iOS devices
                ios_devices = await self.discover_ios_devices()
                
                # Discover Android devices
                android_devices = await self.discover_android_devices()
                
                # Update device registry
                current_devices = ios_devices + android_devices
                self.devices = {d['udid']: d for d in current_devices}
                
                # Send device update to cloud
                if self.websocket:
                    await self.send_device_update(current_devices)
                
                await asyncio.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                logging.error(f"Device discovery error: {e}")
                await asyncio.sleep(10)
    
    async def discover_ios_devices(self):
        """Discover iOS devices using idevice tools"""
        devices = []
        try:
            # Get connected iOS devices
            result = subprocess.run(
                ['idevice_id', '-l'], 
                capture_output=True, 
                text=True, 
                timeout=5
            )
            
            for udid in result.stdout.strip().split('\n'):
                if udid:
                    device_info = await self.get_ios_device_info(udid)
                    if device_info:
                        devices.append(device_info)
                        
        except FileNotFoundError:
            logging.warning("idevice tools not found - iOS support disabled")
        except Exception as e:
            logging.error(f"iOS discovery failed: {e}")
            
        return devices
    
    async def discover_android_devices(self):
        """Discover Android devices using ADB"""
        devices = []
        try:
            # Get connected Android devices
            result = subprocess.run(
                ['adb', 'devices'], 
                capture_output=True, 
                text=True, 
                timeout=5
            )
            
            for line in result.stdout.split('\n')[1:]:
                if '\tdevice' in line:
                    udid = line.split('\t')[0]
                    device_info = await self.get_android_device_info(udid)
                    if device_info:
                        devices.append(device_info)
                        
        except FileNotFoundError:
            logging.warning("ADB not found - Android support disabled")
        except Exception as e:
            logging.error(f"Android discovery failed: {e}")
            
        return devices
    
    async def get_ios_device_info(self, udid):
        """Get detailed iOS device information"""
        try:
            # Get device name
            name_result = subprocess.run(
                ['ideviceinfo', '-u', udid, '-k', 'DeviceName'],
                capture_output=True, text=True, timeout=3
            )
            
            # Get device model
            model_result = subprocess.run(
                ['ideviceinfo', '-u', udid, '-k', 'ProductType'],
                capture_output=True, text=True, timeout=3
            )
            
            return {
                'udid': udid,
                'name': name_result.stdout.strip() or f"iOS Device {udid[:8]}",
                'model': model_result.stdout.strip() or "Unknown",
                'platform': 'ios',
                'status': 'connected',
                'bridge_id': self.bridge_id
            }
            
        except Exception as e:
            logging.error(f"Failed to get iOS device info: {e}")
            return None
    
    async def get_android_device_info(self, udid):
        """Get detailed Android device information"""
        try:
            # Get device model
            model_result = subprocess.run(
                ['adb', '-s', udid, 'shell', 'getprop', 'ro.product.model'],
                capture_output=True, text=True, timeout=3
            )
            
            # Get Android version
            version_result = subprocess.run(
                ['adb', '-s', udid, 'shell', 'getprop', 'ro.build.version.release'],
                capture_output=True, text=True, timeout=3
            )
            
            return {
                'udid': udid,
                'name': model_result.stdout.strip() or f"Android Device {udid[:8]}",
                'model': model_result.stdout.strip() or "Unknown",
                'platform': 'android',
                'os_version': version_result.stdout.strip() or "Unknown",
                'status': 'connected',
                'bridge_id': self.bridge_id
            }
            
        except Exception as e:
            logging.error(f"Failed to get Android device info: {e}")
            return None
    
    async def send_device_update(self, devices):
        """Send device list update to cloud"""
        message = {
            'type': 'device_update',
            'bridge_id': self.bridge_id,
            'devices': devices,
            'timestamp': time.time()
        }
        
        try:
            await self.websocket.send(json.dumps(message))
        except Exception as e:
            logging.error(f"Failed to send device update: {e}")
    
    async def process_cloud_commands(self):
        """Process commands from cloud application"""
        try:
            async for message in self.websocket:
                try:
                    command = json.loads(message)
                    await self.handle_command(command)
                except Exception as e:
                    logging.error(f"Error processing command: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            logging.warning("Cloud connection closed")
            # Attempt reconnection
            await asyncio.sleep(5)
            await self.connect_to_cloud()
    
    async def handle_command(self, command):
        """Handle specific device commands"""
        command_type = command.get('type')
        device_udid = command.get('device_udid')
        
        if command_type == 'screenshot':
            screenshot_data = await self.take_screenshot(device_udid)
            await self.send_response({
                'type': 'screenshot_response',
                'device_udid': device_udid,
                'data': screenshot_data
            })
        elif command_type == 'install_app':
            app_path = command.get('app_path')
            success = await self.install_app(device_udid, app_path)
            await self.send_response({
                'type': 'install_response',
                'device_udid': device_udid,
                'success': success
            })
        elif command_type == 'run_test':
            test_config = command.get('test_config')
            result = await self.run_test(device_udid, test_config)
            await self.send_response({
                'type': 'test_response',
                'device_udid': device_udid,
                'result': result
            })

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) != 3:
        print("Usage: python device_bridge.py <user_token> <cloud_endpoint>")
        sys.exit(1)
    
    user_token = sys.argv[1]
    cloud_endpoint = sys.argv[2]
    
    bridge = LocalDeviceBridge(user_token, cloud_endpoint)
    asyncio.run(bridge.start_bridge())
```

### 4.2 One-Click Bridge Setup

#### **Automated Bridge Installer (install_bridge.py)**
```python
import os
import sys
import subprocess
import platform
import requests
import json

class BridgeInstaller:
    def __init__(self):
        self.system = platform.system().lower()
        self.arch = platform.machine().lower()
        
    def install_dependencies(self):
        """Install required dependencies based on OS"""
        print("Installing dependencies...")
        
        if self.system == "darwin":  # macOS
            self.install_macos_deps()
        elif self.system == "linux":
            self.install_linux_deps()
        elif self.system == "windows":
            self.install_windows_deps()
        else:
            raise Exception(f"Unsupported OS: {self.system}")
    
    def install_macos_deps(self):
        """Install macOS dependencies"""
        # Check if Homebrew is installed
        try:
            subprocess.run(['brew', '--version'], check=True, capture_output=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("Installing Homebrew...")
            subprocess.run([
                '/bin/bash', '-c',
                '$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)'
            ])
        
        # Install dependencies
        subprocess.run(['brew', 'install', 'libimobiledevice'], check=True)
        subprocess.run(['brew', 'install', 'android-platform-tools'], check=True)
        subprocess.run(['brew', 'install', 'python3'], check=True)
    
    def install_linux_deps(self):
        """Install Linux dependencies"""
        # Detect package manager
        if subprocess.run(['which', 'apt'], capture_output=True).returncode == 0:
            # Ubuntu/Debian
            subprocess.run(['sudo', 'apt', 'update'], check=True)
            subprocess.run(['sudo', 'apt', 'install', '-y', 
                          'libimobiledevice-utils', 'android-tools-adb', 
                          'python3', 'python3-pip'], check=True)
        elif subprocess.run(['which', 'yum'], capture_output=True).returncode == 0:
            # CentOS/RHEL
            subprocess.run(['sudo', 'yum', 'install', '-y',
                          'libimobiledevice-utils', 'android-tools', 
                          'python3', 'python3-pip'], check=True)
    
    def install_windows_deps(self):
        """Install Windows dependencies"""
        print("Please install the following manually:")
        print("1. Python 3.8+ from https://python.org")
        print("2. Android SDK Platform Tools")
        print("3. iTunes (for iOS device support)")
        input("Press Enter when dependencies are installed...")
    
    def download_bridge(self):
        """Download the device bridge application"""
        print("Downloading device bridge...")
        
        bridge_url = "https://releases.yourdomain.com/device_bridge.py"
        response = requests.get(bridge_url)
        
        with open("device_bridge.py", "w") as f:
            f.write(response.text)
        
        # Install Python dependencies
        subprocess.run([sys.executable, '-m', 'pip', 'install', 
                       'websockets', 'cryptography'], check=True)
    
    def setup_cloudflare_tunnel(self, user_token):
        """Set up Cloudflare tunnel for the user"""
        print("Setting up secure tunnel...")
        
        # Download cloudflared
        if self.system == "darwin":
            cloudflared_url = "https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-darwin-amd64.tgz"
        elif self.system == "linux":
            cloudflared_url = "https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64"
        else:
            cloudflared_url = "https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-windows-amd64.exe"
        
        # Download and install cloudflared
        response = requests.get(cloudflared_url)
        with open("cloudflared", "wb") as f:
            f.write(response.content)
        
        os.chmod("cloudflared", 0o755)
        
        # Create tunnel configuration
        tunnel_config = {
            "tunnel": f"mobile-bridge-{user_token[:8]}",
            "credentials-file": f"./tunnel-{user_token[:8]}.json",
            "ingress": [
                {
                    "hostname": f"bridge-{user_token[:8]}.yourdomain.com",
                    "service": "http://localhost:9999"
                },
                {
                    "service": "http_status:404"
                }
            ]
        }
        
        with open("tunnel-config.yml", "w") as f:
            json.dump(tunnel_config, f, indent=2)
        
        print(f"Tunnel configured for: bridge-{user_token[:8]}.yourdomain.com")
    
    def create_startup_script(self, user_token, cloud_endpoint):
        """Create startup script for the bridge"""
        script_content = f"""#!/bin/bash
# Mobile Automation Device Bridge Startup Script

echo "Starting Mobile Automation Device Bridge..."

# Start Cloudflare tunnel in background
./cloudflared tunnel --config tunnel-config.yml run &

# Wait for tunnel to establish
sleep 5

# Start device bridge
python3 device_bridge.py {user_token} {cloud_endpoint}
"""
        
        with open("start_bridge.sh", "w") as f:
            f.write(script_content)
        
        os.chmod("start_bridge.sh", 0o755)
        
        print("Bridge setup complete!")
        print("To start the bridge, run: ./start_bridge.sh")

def main():
    if len(sys.argv) != 3:
        print("Usage: python install_bridge.py <user_token> <cloud_endpoint>")
        sys.exit(1)
    
    user_token = sys.argv[1]
    cloud_endpoint = sys.argv[2]
    
    installer = BridgeInstaller()
    
    try:
        installer.install_dependencies()
        installer.download_bridge()
        installer.setup_cloudflare_tunnel(user_token)
        installer.create_startup_script(user_token, cloud_endpoint)
        
        print("\n✅ Bridge installation completed successfully!")
        print(f"🔗 Your devices will be accessible at: bridge-{user_token[:8]}.yourdomain.com")
        print("🚀 Run './start_bridge.sh' to start the bridge")
        
    except Exception as e:
        print(f"❌ Installation failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
```

## 5. Revised Cost Analysis & Business Model

### 5.1 Infrastructure Cost Comparison

#### **Traditional Cloud Device Farm Approach**
| Component | Monthly Cost | Annual Cost |
|-----------|-------------|-------------|
| AWS Device Farm | $500-2000 | $6,000-24,000 |
| Kubernetes Cluster | $200-800 | $2,400-9,600 |
| Load Balancers | $50-200 | $600-2,400 |
| Database | $100-500 | $1,200-6,000 |
| Storage | $50-200 | $600-2,400 |
| **Total** | **$900-3,700** | **$10,800-44,400** |

#### **Hybrid Local Device Approach**
| Component | Monthly Cost | Annual Cost |
|-----------|-------------|-------------|
| Web Server (Hetzner CX21) | $4.50 | $54 |
| Database (Managed PostgreSQL) | $11.00 | $132 |
| Object Storage (100GB) | $3.50 | $42 |
| Domain & SSL (Cloudflare) | $0 | $0 |
| Monitoring (UptimeRobot) | $7.00 | $84 |
| **Total** | **$26.00** | **$312** |

#### **Cost Savings: 97% reduction** ($44,400 → $312 annually)

### 5.2 Updated Revenue Model

#### **Simplified Pricing Tiers**

**Starter Plan - $19/month**
- 2 concurrent local devices
- 500 test executions/month
- Basic reporting
- Email support
- 30-day test history

**Professional Plan - $49/month**
- 5 concurrent local devices
- 2,000 test executions/month
- Advanced reporting & analytics
- Priority support
- 90-day test history
- API access

**Team Plan - $99/month**
- 10 concurrent local devices
- 5,000 test executions/month
- Team collaboration features
- Dedicated support
- 6-month test history
- Custom integrations

**Enterprise Plan - $199/month**
- Unlimited local devices
- Unlimited test executions
- On-premise deployment option
- 24/7 support
- 2-year test history
- White-label options

### 5.3 Unit Economics (Revised)

#### **Cost to Serve per Customer**
- **Infrastructure**: $0.50/customer/month (at 50+ customers)
- **Support**: $2.00/customer/month
- **Payment Processing**: 3% of revenue
- **Total Cost**: ~$3.50/customer/month

#### **Gross Margins by Plan**
| Plan | Revenue | Cost to Serve | Gross Margin |
|------|---------|---------------|--------------|
| Starter | $19 | $3.50 | 82% |
| Professional | $49 | $3.50 | 93% |
| Team | $99 | $3.50 | 96% |
| Enterprise | $199 | $3.50 | 98% |

### 5.4 Break-Even Analysis

**Monthly Break-Even**: 2 customers (any plan)
**Time to Profitability**: Month 1

#### **Revenue Projections (Conservative)**

| Month | Customers | MRR | Annual Run Rate |
|-------|-----------|-----|-----------------|
| 1-3   | 5-15     | $285-735 | $3,420-8,820 |
| 4-6   | 20-40    | $980-1,960 | $11,760-23,520 |
| 7-9   | 50-80    | $2,450-3,920 | $29,400-47,040 |
| 10-12 | 100-150  | $4,900-7,350 | $58,800-88,200 |

**Year 1 Target**: 150 customers, $88,200 ARR
**Year 2 Target**: 500 customers, $294,000 ARR
**Year 3 Target**: 1,500 customers, $882,000 ARR

## 6. Implementation Roadmap

### 6.1 Phase 1: Foundation (Weeks 1-4)

#### **Week 1-2: Cloud Infrastructure Setup**
- [ ] Set up Hetzner VPS with Ubuntu 22.04
- [ ] Install and configure PostgreSQL database
- [ ] Set up Cloudflare domain and SSL
- [ ] Deploy basic Flask application
- [ ] Configure automated backups

#### **Week 3-4: Core Application Development**
- [ ] Implement user authentication system
- [ ] Create device bridge WebSocket endpoint
- [ ] Build basic web UI for device management
- [ ] Implement test execution framework
- [ ] Add basic reporting functionality

### 6.2 Phase 2: Device Bridge (Weeks 5-8)

#### **Week 5-6: Bridge Development**
- [ ] Create local device bridge application
- [ ] Implement iOS device discovery (idevice tools)
- [ ] Implement Android device discovery (ADB)
- [ ] Add secure WebSocket communication
- [ ] Test end-to-end device connectivity

#### **Week 7-8: Bridge Distribution**
- [ ] Create automated bridge installer
- [ ] Implement Cloudflare tunnel integration
- [ ] Build one-click setup process
- [ ] Create user documentation
- [ ] Test on multiple platforms (macOS, Linux, Windows)

### 6.3 Phase 3: Testing & Polish (Weeks 9-12)

#### **Week 9-10: Testing Framework**
- [ ] Integrate Appium for test automation
- [ ] Add screenshot and video recording
- [ ] Implement test result storage
- [ ] Create test report generation
- [ ] Add real-time test monitoring

#### **Week 11-12: Production Readiness**
- [ ] Implement comprehensive error handling
- [ ] Add monitoring and alerting
- [ ] Create user onboarding flow
- [ ] Set up customer support system
- [ ] Conduct security audit

### 6.4 Phase 4: Launch & Growth (Weeks 13-16)

#### **Week 13-14: Beta Launch**
- [ ] Launch closed beta with 10-20 users
- [ ] Gather feedback and iterate
- [ ] Fix critical bugs and issues
- [ ] Optimize performance
- [ ] Prepare marketing materials

#### **Week 15-16: Public Launch**
- [ ] Launch public version
- [ ] Implement payment processing
- [ ] Start marketing campaigns
- [ ] Monitor system performance
- [ ] Begin customer acquisition

## 7. Technical Implementation Details

### 7.1 Simplified Cloud Application Architecture

#### **Single Flask Application (app.py)**
```python
from flask import Flask, render_template, request, jsonify, session
from flask_sqlalchemy import SQLAlchemy
from flask_socketio import SocketIO, emit
import jwt
import bcrypt
import uuid

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key'
app.config['SQLALCHEMY_DATABASE_URI'] = 'postgresql://user:pass@localhost/mobile_automation'

db = SQLAlchemy(app)
socketio = SocketIO(app, cors_allowed_origins="*")

# Database Models
class User(db.Model):
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    email = db.Column(db.String(255), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    subscription_tier = db.Column(db.String(50), default='starter')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class DeviceBridge(db.Model):
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('user.id'), nullable=False)
    bridge_token = db.Column(db.String(255), nullable=False)
    tunnel_endpoint = db.Column(db.String(255))
    last_seen = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)

class TestExecution(db.Model):
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('user.id'), nullable=False)
    device_udid = db.Column(db.String(255), nullable=False)
    test_name = db.Column(db.String(255), nullable=False)
    status = db.Column(db.String(50), default='pending')
    results = db.Column(db.JSON)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

# Routes
@app.route('/')
def dashboard():
    if 'user_id' not in session:
        return render_template('login.html')
    return render_template('dashboard.html')

@app.route('/api/auth/login', methods=['POST'])
def login():
    data = request.get_json()
    user = User.query.filter_by(email=data['email']).first()

    if user and bcrypt.checkpw(data['password'].encode(), user.password_hash):
        session['user_id'] = user.id
        return jsonify({'success': True, 'user_id': user.id})

    return jsonify({'success': False, 'error': 'Invalid credentials'}), 401

@app.route('/api/bridge/register', methods=['POST'])
def register_bridge():
    if 'user_id' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    bridge_token = str(uuid.uuid4())
    bridge = DeviceBridge(
        user_id=session['user_id'],
        bridge_token=bridge_token
    )

    db.session.add(bridge)
    db.session.commit()

    return jsonify({
        'bridge_token': bridge_token,
        'websocket_endpoint': f'wss://{request.host}/bridge'
    })

@socketio.on('bridge_connect')
def handle_bridge_connect(data):
    bridge_token = data.get('bridge_token')
    bridge = DeviceBridge.query.filter_by(bridge_token=bridge_token).first()

    if bridge:
        bridge.last_seen = datetime.utcnow()
        db.session.commit()
        emit('bridge_registered', {'status': 'success'})
    else:
        emit('bridge_error', {'error': 'Invalid bridge token'})

@socketio.on('device_update')
def handle_device_update(data):
    # Store device information and broadcast to user's dashboard
    emit('devices_updated', data, room=f"user_{data['user_id']}")

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    socketio.run(app, host='0.0.0.0', port=8080)
```

### 7.2 Deployment Script

#### **One-Command Deployment (deploy.sh)**
```bash
#!/bin/bash
set -e

echo "🚀 Deploying Mobile Automation Testing Platform..."

# Update system
sudo apt update && sudo apt upgrade -y

# Install dependencies
sudo apt install -y python3 python3-pip postgresql postgresql-contrib nginx

# Create database
sudo -u postgres createdb mobile_automation
sudo -u postgres createuser mobile_user
sudo -u postgres psql -c "ALTER USER mobile_user PASSWORD 'secure_password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE mobile_automation TO mobile_user;"

# Install Python dependencies
pip3 install flask flask-sqlalchemy flask-socketio psycopg2-binary gunicorn

# Clone application
git clone https://github.com/yourusername/mobile-automation-saas.git
cd mobile-automation-saas

# Configure environment
cat > .env << EOF
DATABASE_URL=postgresql://mobile_user:secure_password@localhost/mobile_automation
SECRET_KEY=$(openssl rand -hex 32)
FLASK_ENV=production
EOF

# Start application
gunicorn --worker-class eventlet -w 1 --bind 0.0.0.0:8080 app:app &

# Configure Nginx
sudo tee /etc/nginx/sites-available/mobile-automation << EOF
server {
    listen 80;
    server_name yourdomain.com;

    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
    }

    location /socket.io/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
EOF

sudo ln -s /etc/nginx/sites-available/mobile-automation /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl reload nginx

echo "✅ Deployment complete!"
echo "🌐 Access your application at: http://yourdomain.com"
echo "💰 Total monthly cost: ~$26"
```

This hybrid architecture reduces infrastructure costs by 97% while maintaining professional SaaS capabilities and providing users with secure access to their own devices.
