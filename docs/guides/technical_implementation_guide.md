# Technical Implementation Guide: Cloud SaaS Deployment

## 1. Enhanced Multi-Tenant Architecture

### 1.1 Database Schema Evolution

#### **Tenant Management Schema**
```sql
-- Core tenant management
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    subdomain VARCHAR(100) UNIQUE,
    subscription_tier VARCHAR(50) DEFAULT 'starter',
    max_devices INTEGER DEFAULT 2,
    max_test_minutes INTEGER DEFAULT 100,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);

-- Tenant-specific device registry
CREATE TABLE tenant_devices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id),
    device_udid VARCHAR(255),
    device_name VA<PERSON>HA<PERSON>(255),
    platform VARCHAR(20), -- 'ios' or 'android'
    bridge_endpoint VARCHAR(255),
    last_seen TIMESTAMP,
    is_online BOOLEAN DEFAULT false,
    device_metadata JSONB
);

-- Test execution tracking
CREATE TABLE test_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id),
    device_id UUID REFERENCES tenant_devices(id),
    test_suite_name VARCHAR(255),
    execution_time INTEGER, -- in seconds
    status VARCHAR(50),
    results JSONB,
    created_at TIMESTAMP DEFAULT NOW()
) PARTITION BY HASH (tenant_id);
```

### 1.2 Enhanced Flask Application Structure

#### **Multi-Tenant Flask App (app/multi_tenant_app.py)**
```python
from flask import Flask, request, g, session
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager, verify_jwt_in_request, get_jwt_identity
import uuid

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = '******************************/mobile_automation'
app.config['JWT_SECRET_KEY'] = 'your-secret-key'

db = SQLAlchemy(app)
jwt = JWTManager(app)

class TenantContext:
    def __init__(self):
        self.tenant_id = None
        self.tenant_data = None
    
    def set_tenant(self, tenant_id):
        self.tenant_id = tenant_id
        # Set PostgreSQL session variable for RLS
        db.session.execute(
            "SET app.current_tenant = :tenant_id",
            {"tenant_id": str(tenant_id)}
        )

@app.before_request
def load_tenant():
    g.tenant = TenantContext()
    
    # Extract tenant from subdomain or JWT token
    if request.headers.get('Authorization'):
        verify_jwt_in_request()
        token_data = get_jwt_identity()
        g.tenant.set_tenant(token_data['tenant_id'])
    elif request.host.startswith('tenant-'):
        # Extract from subdomain: tenant-{uuid}.yourdomain.com
        subdomain = request.host.split('.')[0]
        tenant_id = subdomain.replace('tenant-', '')
        g.tenant.set_tenant(tenant_id)

@app.route('/api/devices')
def get_tenant_devices():
    """Get devices for current tenant"""
    devices = db.session.query(TenantDevice).filter_by(
        tenant_id=g.tenant.tenant_id,
        is_online=True
    ).all()
    
    return jsonify([{
        'id': str(device.id),
        'name': device.device_name,
        'platform': device.platform,
        'udid': device.device_udid,
        'bridge_endpoint': device.bridge_endpoint,
        'last_seen': device.last_seen.isoformat() if device.last_seen else None
    } for device in devices])
```

## 2. Enhanced Device Bridge Service

### 2.1 Secure Cloud-Connected Bridge

#### **Enhanced Bridge Service (cloud_device_bridge.py)**
```python
import asyncio
import websockets
import json
import ssl
from cryptography.fernet import Fernet
import subprocess
import logging

class SecureCloudDeviceBridge:
    def __init__(self, tenant_id, cloud_endpoint, auth_token):
        self.tenant_id = tenant_id
        self.cloud_endpoint = cloud_endpoint
        self.auth_token = auth_token
        self.encryption_key = Fernet.generate_key()
        self.cipher = Fernet(self.encryption_key)
        self.devices = {}
        self.websocket = None
        
    async def connect_to_cloud(self):
        """Establish secure WebSocket connection to cloud"""
        ssl_context = ssl.create_default_context()
        headers = {
            'Authorization': f'Bearer {self.auth_token}',
            'X-Tenant-ID': self.tenant_id,
            'X-Bridge-Version': '2.0'
        }
        
        try:
            self.websocket = await websockets.connect(
                f"wss://{self.cloud_endpoint}/bridge",
                extra_headers=headers,
                ssl=ssl_context
            )
            logging.info(f"Connected to cloud endpoint: {self.cloud_endpoint}")
            await self.register_bridge()
            
        except Exception as e:
            logging.error(f"Failed to connect to cloud: {e}")
            raise
    
    async def register_bridge(self):
        """Register this bridge with the cloud service"""
        registration_data = {
            'type': 'bridge_registration',
            'tenant_id': self.tenant_id,
            'bridge_capabilities': {
                'ios_support': True,
                'android_support': True,
                'max_concurrent_devices': 5
            },
            'encryption_key': self.encryption_key.decode()
        }
        
        await self.websocket.send(json.dumps(registration_data))
        response = await self.websocket.recv()
        result = json.loads(response)
        
        if result.get('status') == 'registered':
            logging.info("Bridge successfully registered with cloud")
            await self.start_device_discovery()
        else:
            raise Exception(f"Bridge registration failed: {result}")
    
    async def start_device_discovery(self):
        """Continuously discover and report connected devices"""
        while True:
            try:
                # Discover iOS devices
                ios_devices = await self.discover_ios_devices()
                
                # Discover Android devices  
                android_devices = await self.discover_android_devices()
                
                all_devices = ios_devices + android_devices
                
                # Report device status to cloud
                device_update = {
                    'type': 'device_update',
                    'tenant_id': self.tenant_id,
                    'devices': all_devices,
                    'timestamp': time.time()
                }
                
                encrypted_data = self.cipher.encrypt(
                    json.dumps(device_update).encode()
                )
                
                await self.websocket.send(encrypted_data)
                
                # Update local device registry
                self.devices = {d['udid']: d for d in all_devices}
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logging.error(f"Device discovery error: {e}")
                await asyncio.sleep(30)  # Wait longer on error
    
    async def discover_ios_devices(self):
        """Discover connected iOS devices"""
        try:
            result = subprocess.run(
                ['idevice_id', '-l'], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            devices = []
            for udid in result.stdout.strip().split('\n'):
                if udid:
                    device_info = await self.get_ios_device_info(udid)
                    if device_info:
                        devices.append(device_info)
            
            return devices
            
        except Exception as e:
            logging.error(f"iOS device discovery failed: {e}")
            return []
    
    async def get_ios_device_info(self, udid):
        """Get detailed iOS device information"""
        try:
            # Get device name
            name_result = subprocess.run(
                ['ideviceinfo', '-u', udid, '-k', 'DeviceName'],
                capture_output=True, text=True, timeout=5
            )
            
            # Get device model
            model_result = subprocess.run(
                ['ideviceinfo', '-u', udid, '-k', 'ProductType'],
                capture_output=True, text=True, timeout=5
            )
            
            # Get iOS version
            version_result = subprocess.run(
                ['ideviceinfo', '-u', udid, '-k', 'ProductVersion'],
                capture_output=True, text=True, timeout=5
            )
            
            return {
                'udid': udid,
                'name': name_result.stdout.strip(),
                'model': model_result.stdout.strip(),
                'platform': 'ios',
                'os_version': version_result.stdout.strip(),
                'status': 'online',
                'bridge_port': 9999,
                'capabilities': ['app_install', 'screenshot', 'automation']
            }
            
        except Exception as e:
            logging.error(f"Failed to get iOS device info for {udid}: {e}")
            return None
    
    async def handle_cloud_commands(self):
        """Handle commands from cloud service"""
        try:
            async for message in self.websocket:
                try:
                    # Decrypt if needed
                    if isinstance(message, bytes):
                        decrypted = self.cipher.decrypt(message)
                        command = json.loads(decrypted.decode())
                    else:
                        command = json.loads(message)
                    
                    await self.process_command(command)
                    
                except Exception as e:
                    logging.error(f"Error processing command: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            logging.warning("Cloud connection closed")
            await self.reconnect()
    
    async def process_command(self, command):
        """Process commands from cloud"""
        command_type = command.get('type')
        
        if command_type == 'device_action':
            await self.handle_device_action(command)
        elif command_type == 'health_check':
            await self.send_health_response()
        elif command_type == 'update_config':
            await self.update_configuration(command)
    
    async def handle_device_action(self, command):
        """Handle device-specific actions"""
        device_udid = command.get('device_udid')
        action = command.get('action')
        
        if device_udid not in self.devices:
            await self.send_error(f"Device {device_udid} not found")
            return
        
        if action == 'screenshot':
            screenshot_data = await self.take_screenshot(device_udid)
            await self.send_response({
                'type': 'screenshot_response',
                'device_udid': device_udid,
                'data': screenshot_data
            })
        elif action == 'install_app':
            app_path = command.get('app_path')
            result = await self.install_app(device_udid, app_path)
            await self.send_response({
                'type': 'install_response',
                'device_udid': device_udid,
                'success': result
            })
```

## 3. Kubernetes Deployment Configuration

### 3.1 Production-Ready Kubernetes Manifests

#### **Namespace and ConfigMap (k8s/production/namespace.yaml)**
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: mobile-automation-prod
  labels:
    name: mobile-automation-prod
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: mobile-automation-prod
data:
  DATABASE_URL: "***********************************************/mobile_automation"
  REDIS_URL: "redis://redis:6379/0"
  JWT_SECRET_KEY: "production-secret-key"
  CLOUD_ENDPOINT: "wss://api.yourdomain.com"
  MAX_DEVICES_PER_TENANT: "10"
  DEFAULT_SUBSCRIPTION_TIER: "starter"
```

#### **PostgreSQL Deployment (k8s/production/postgres.yaml)**
```yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  namespace: mobile-automation-prod
spec:
  serviceName: postgres
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:14
        env:
        - name: POSTGRES_DB
          value: mobile_automation
        - name: POSTGRES_USER
          value: mobile_user
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi
```

#### **Main Application Deployment (k8s/production/app.yaml)**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mobile-automation-api
  namespace: mobile-automation-prod
spec:
  replicas: 3
  selector:
    matchLabels:
      app: mobile-automation-api
  template:
    metadata:
      labels:
        app: mobile-automation-api
    spec:
      containers:
      - name: api
        image: your-registry/mobile-automation:latest
        ports:
        - containerPort: 8080
        env:
        - name: DATABASE_URL
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: DATABASE_URL
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: REDIS_URL
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "400m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: mobile-automation-api-service
  namespace: mobile-automation-prod
spec:
  selector:
    app: mobile-automation-api
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP
```

## 4. Monitoring and Observability

### 4.1 Prometheus Monitoring Setup

#### **Prometheus Configuration (k8s/monitoring/prometheus.yaml)**
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: mobile-automation-prod
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
    scrape_configs:
    - job_name: 'mobile-automation-api'
      static_configs:
      - targets: ['mobile-automation-api-service:80']
      metrics_path: /metrics
    - job_name: 'postgres'
      static_configs:
      - targets: ['postgres:5432']
    - job_name: 'redis'
      static_configs:
      - targets: ['redis:6379']
```

### 4.2 Application Metrics

#### **Flask Metrics Integration (app/metrics.py)**
```python
from prometheus_client import Counter, Histogram, Gauge, generate_latest
from flask import Response
import time

# Metrics definitions
REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status', 'tenant_id']
)

REQUEST_DURATION = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration',
    ['method', 'endpoint', 'tenant_id']
)

ACTIVE_DEVICES = Gauge(
    'active_devices_total',
    'Number of active devices',
    ['tenant_id', 'platform']
)

TEST_EXECUTIONS = Counter(
    'test_executions_total',
    'Total test executions',
    ['tenant_id', 'platform', 'status']
)

def track_request_metrics(f):
    """Decorator to track request metrics"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        tenant_id = getattr(g, 'tenant', {}).get('tenant_id', 'unknown')
        
        try:
            response = f(*args, **kwargs)
            status = response.status_code if hasattr(response, 'status_code') else 200
            
            REQUEST_COUNT.labels(
                method=request.method,
                endpoint=request.endpoint,
                status=status,
                tenant_id=tenant_id
            ).inc()
            
            return response
            
        finally:
            REQUEST_DURATION.labels(
                method=request.method,
                endpoint=request.endpoint,
                tenant_id=tenant_id
            ).observe(time.time() - start_time)
    
    return wrapper

@app.route('/metrics')
def metrics():
    """Prometheus metrics endpoint"""
    return Response(generate_latest(), mimetype='text/plain')
```

## 5. Security Implementation

### 5.1 JWT Authentication with Tenant Scoping

#### **Enhanced Authentication (app/auth.py)**
```python
from flask_jwt_extended import create_access_token, get_jwt_identity
from werkzeug.security import check_password_hash
import uuid
from datetime import timedelta

class TenantAuthManager:
    def __init__(self, db):
        self.db = db
    
    def authenticate_user(self, email, password, tenant_subdomain=None):
        """Authenticate user and return tenant-scoped JWT"""
        user = self.db.session.query(User).filter_by(email=email).first()
        
        if not user or not check_password_hash(user.password_hash, password):
            return None
        
        # Get user's tenant
        tenant = self.db.session.query(Tenant).filter_by(id=user.tenant_id).first()
        
        if not tenant or not tenant.is_active:
            return None
        
        # Verify subdomain if provided
        if tenant_subdomain and tenant.subdomain != tenant_subdomain:
            return None
        
        # Create tenant-scoped JWT
        token_data = {
            'user_id': str(user.id),
            'tenant_id': str(tenant.id),
            'tenant_subdomain': tenant.subdomain,
            'subscription_tier': tenant.subscription_tier,
            'permissions': user.permissions
        }
        
        access_token = create_access_token(
            identity=token_data,
            expires_delta=timedelta(hours=24)
        )
        
        return {
            'access_token': access_token,
            'tenant_id': str(tenant.id),
            'tenant_name': tenant.name,
            'subscription_tier': tenant.subscription_tier
        }
```

## 6. Deployment Automation

### 6.1 CI/CD Pipeline Configuration

#### **GitHub Actions Workflow (.github/workflows/deploy.yml)**
```yaml
name: Deploy to Production
on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-cov
    - name: Run tests
      run: pytest --cov=app tests/

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Build Docker image
      run: |
        docker build -t mobile-automation:${{ github.sha }} .
        docker tag mobile-automation:${{ github.sha }} mobile-automation:latest
    - name: Push to registry
      run: |
        echo ${{ secrets.DOCKER_PASSWORD }} | docker login -u ${{ secrets.DOCKER_USERNAME }} --password-stdin
        docker push mobile-automation:${{ github.sha }}
        docker push mobile-automation:latest

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - uses: actions/checkout@v3
    - name: Deploy to DigitalOcean
      run: |
        doctl auth init --access-token ${{ secrets.DO_ACCESS_TOKEN }}
        doctl kubernetes cluster kubeconfig save mobile-automation-cluster
        kubectl set image deployment/mobile-automation-api mobile-automation-api=mobile-automation:${{ github.sha }}
        kubectl rollout status deployment/mobile-automation-api
```

### 6.2 Infrastructure as Code

#### **Terraform Configuration (terraform/main.tf)**
```hcl
terraform {
  required_providers {
    digitalocean = {
      source = "digitalocean/digitalocean"
      version = "~> 2.0"
    }
  }
}

provider "digitalocean" {
  token = var.do_token
}

resource "digitalocean_kubernetes_cluster" "mobile_automation" {
  name   = "mobile-automation-cluster"
  region = "nyc1"
  version = "1.28.2-do.0"

  node_pool {
    name       = "worker-pool"
    size       = "s-2vcpu-2gb"
    node_count = 3
    auto_scale = true
    min_nodes  = 2
    max_nodes  = 10
  }
}

resource "digitalocean_database_cluster" "postgres" {
  name       = "mobile-automation-db"
  engine     = "pg"
  version    = "14"
  size       = "db-s-1vcpu-1gb"
  region     = "nyc1"
  node_count = 1
}

resource "digitalocean_loadbalancer" "web" {
  name   = "mobile-automation-lb"
  region = "nyc1"

  forwarding_rule {
    entry_protocol  = "https"
    entry_port      = 443
    target_protocol = "http"
    target_port     = 80
    certificate_name = digitalocean_certificate.cert.name
  }

  healthcheck {
    protocol = "http"
    port     = 80
    path     = "/health"
  }

  droplet_tag = "mobile-automation"
}
```

This technical implementation guide provides the foundation for deploying your mobile automation testing framework as a secure, scalable SaaS platform with automated deployment, monitoring, and cost optimization.
