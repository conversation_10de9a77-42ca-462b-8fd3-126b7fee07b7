# Security & Compliance Framework for Mobile Automation Testing SaaS

## 1. Security Architecture Overview

### 1.1 Defense in Depth Strategy

Our security architecture implements multiple layers of protection to ensure comprehensive data protection and system integrity:

```
┌─────────────────────────────────────────────────────────────┐
│                    SECURITY LAYERS                          │
├─────────────────────────────────────────────────────────────┤
│ 1. Network Security (WAF, DDoS Protection, VPN)            │
│ 2. Infrastructure Security (K8s RBAC, Network Policies)    │
│ 3. Application Security (Authentication, Authorization)     │
│ 4. Data Security (Encryption, Backup, Retention)          │
│ 5. Device Security (Bridge Authentication, Isolation)      │
│ 6. Monitoring & Incident Response (SIEM, Alerting)        │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 Threat Model

#### **Primary Threats Addressed**
1. **Data Breaches**: Unauthorized access to tenant data
2. **Device Hijacking**: Malicious control of connected devices
3. **Man-in-the-Middle**: Interception of device communications
4. **Privilege Escalation**: Unauthorized access to admin functions
5. **DDoS Attacks**: Service availability disruption
6. **Insider Threats**: Malicious actions by authorized users

## 2. Multi-Tenant Data Isolation

### 2.1 Database-Level Isolation

#### **Row-Level Security (RLS) Implementation**
```sql
-- Enable RLS on all tenant tables
ALTER TABLE test_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE device_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_data ENABLE ROW LEVEL SECURITY;

-- Create tenant isolation policies
CREATE POLICY tenant_isolation_policy ON test_results
    USING (tenant_id = current_setting('app.current_tenant')::uuid);

CREATE POLICY device_isolation_policy ON device_sessions
    USING (tenant_id = current_setting('app.current_tenant')::uuid);

-- Ensure tenant context is always set
CREATE OR REPLACE FUNCTION set_tenant_context(tenant_uuid UUID)
RETURNS void AS $$
BEGIN
    PERFORM set_config('app.current_tenant', tenant_uuid::text, true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### **Schema-Based Isolation for Enterprise Customers**
```sql
-- Create dedicated schemas for enterprise tenants
CREATE SCHEMA tenant_enterprise_001;
CREATE SCHEMA tenant_enterprise_002;

-- Grant limited access to tenant-specific schemas
GRANT USAGE ON SCHEMA tenant_enterprise_001 TO tenant_001_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA tenant_enterprise_001 TO tenant_001_user;
```

### 2.2 Application-Level Isolation

#### **Tenant Context Middleware**
```python
from flask import g, request, abort
from functools import wraps
import jwt

class TenantSecurityMiddleware:
    def __init__(self, app):
        self.app = app
        app.before_request(self.load_tenant_context)
    
    def load_tenant_context(self):
        """Load and validate tenant context for every request"""
        try:
            # Extract tenant from JWT token
            token = request.headers.get('Authorization', '').replace('Bearer ', '')
            if not token:
                abort(401, 'Authentication required')
            
            payload = jwt.decode(token, app.config['JWT_SECRET_KEY'], algorithms=['HS256'])
            tenant_id = payload.get('tenant_id')
            
            if not tenant_id:
                abort(403, 'Invalid tenant context')
            
            # Validate tenant exists and is active
            tenant = db.session.query(Tenant).filter_by(
                id=tenant_id, 
                is_active=True
            ).first()
            
            if not tenant:
                abort(403, 'Tenant not found or inactive')
            
            # Set tenant context
            g.tenant_id = tenant_id
            g.tenant = tenant
            
            # Set database session context
            db.session.execute(
                "SELECT set_tenant_context(:tenant_id)",
                {"tenant_id": tenant_id}
            )
            
        except jwt.InvalidTokenError:
            abort(401, 'Invalid authentication token')
        except Exception as e:
            app.logger.error(f"Tenant context error: {e}")
            abort(500, 'Internal server error')

def require_tenant_access(f):
    """Decorator to ensure tenant access is properly validated"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not hasattr(g, 'tenant_id'):
            abort(403, 'Tenant context required')
        return f(*args, **kwargs)
    return decorated_function
```

## 3. Device Bridge Security

### 3.1 Secure Device Communication

#### **End-to-End Encryption for Device Bridges**
```python
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
import os

class SecureDeviceBridge:
    def __init__(self, tenant_id):
        self.tenant_id = tenant_id
        self.private_key = self.load_or_generate_private_key()
        self.public_key = self.private_key.public_key()
        self.session_keys = {}
    
    def load_or_generate_private_key(self):
        """Load existing private key or generate new one"""
        key_path = f"/secure/keys/tenant_{self.tenant_id}_private.pem"
        
        if os.path.exists(key_path):
            with open(key_path, "rb") as key_file:
                private_key = serialization.load_pem_private_key(
                    key_file.read(),
                    password=None,
                )
        else:
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048,
            )
            
            # Save private key securely
            pem = private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            )
            
            os.makedirs(os.path.dirname(key_path), exist_ok=True)
            with open(key_path, "wb") as key_file:
                key_file.write(pem)
            
            # Set secure permissions
            os.chmod(key_path, 0o600)
        
        return private_key
    
    def establish_secure_session(self, device_id, device_public_key):
        """Establish encrypted session with device"""
        # Generate session key
        session_key = os.urandom(32)  # 256-bit AES key
        
        # Encrypt session key with device's public key
        encrypted_session_key = device_public_key.encrypt(
            session_key,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
        
        # Store session key
        self.session_keys[device_id] = session_key
        
        return encrypted_session_key
    
    def encrypt_device_command(self, device_id, command_data):
        """Encrypt command for device transmission"""
        if device_id not in self.session_keys:
            raise ValueError("No secure session established")
        
        session_key = self.session_keys[device_id]
        iv = os.urandom(16)  # 128-bit IV for AES
        
        cipher = Cipher(algorithms.AES(session_key), modes.CBC(iv))
        encryptor = cipher.encryptor()
        
        # Pad data to block size
        padded_data = self.pad_data(command_data.encode())
        encrypted_data = encryptor.update(padded_data) + encryptor.finalize()
        
        return iv + encrypted_data
    
    def decrypt_device_response(self, device_id, encrypted_response):
        """Decrypt response from device"""
        if device_id not in self.session_keys:
            raise ValueError("No secure session established")
        
        session_key = self.session_keys[device_id]
        iv = encrypted_response[:16]
        encrypted_data = encrypted_response[16:]
        
        cipher = Cipher(algorithms.AES(session_key), modes.CBC(iv))
        decryptor = cipher.decryptor()
        
        padded_data = decryptor.update(encrypted_data) + decryptor.finalize()
        return self.unpad_data(padded_data).decode()
```

### 3.2 Device Authentication & Authorization

#### **Device Certificate Management**
```python
from cryptography import x509
from cryptography.x509.oid import NameOID
from cryptography.hazmat.primitives import hashes
import datetime

class DeviceCertificateManager:
    def __init__(self, ca_private_key, ca_certificate):
        self.ca_private_key = ca_private_key
        self.ca_certificate = ca_certificate
    
    def issue_device_certificate(self, tenant_id, device_id, device_public_key):
        """Issue certificate for device authentication"""
        subject = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Cloud"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "Mobile Testing"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Mobile Automation SaaS"),
            x509.NameAttribute(NameOID.COMMON_NAME, f"device-{device_id}"),
        ])
        
        # Add tenant ID as extension
        tenant_extension = x509.UnrecognizedExtension(
            oid=x509.ObjectIdentifier("1.3.6.1.4.1.99999.1"),
            value=tenant_id.encode()
        )
        
        certificate = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            self.ca_certificate.subject
        ).public_key(
            device_public_key
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.datetime.utcnow()
        ).not_valid_after(
            datetime.datetime.utcnow() + datetime.timedelta(days=365)
        ).add_extension(
            tenant_extension,
            critical=False,
        ).add_extension(
            x509.KeyUsage(
                digital_signature=True,
                key_encipherment=True,
                key_agreement=False,
                key_cert_sign=False,
                crl_sign=False,
                content_commitment=False,
                data_encipherment=False,
                encipher_only=False,
                decipher_only=False,
            ),
            critical=True,
        ).sign(self.ca_private_key, hashes.SHA256())
        
        return certificate
    
    def validate_device_certificate(self, certificate, expected_tenant_id):
        """Validate device certificate and extract tenant ID"""
        try:
            # Verify certificate signature
            self.ca_certificate.public_key().verify(
                certificate.signature,
                certificate.tbs_certificate_bytes,
                padding.PKCS1v15(),
                certificate.signature_hash_algorithm,
            )
            
            # Check validity period
            now = datetime.datetime.utcnow()
            if now < certificate.not_valid_before or now > certificate.not_valid_after:
                return False, "Certificate expired or not yet valid"
            
            # Extract and validate tenant ID
            for extension in certificate.extensions:
                if extension.oid.dotted_string == "1.3.6.1.4.1.99999.1":
                    cert_tenant_id = extension.value.decode()
                    if cert_tenant_id != expected_tenant_id:
                        return False, "Tenant ID mismatch"
                    return True, "Valid"
            
            return False, "No tenant ID found in certificate"
            
        except Exception as e:
            return False, f"Certificate validation failed: {e}"
```

## 4. Infrastructure Security

### 4.1 Kubernetes Security Configuration

#### **Network Policies for Tenant Isolation**
```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: tenant-isolation
  namespace: mobile-automation-prod
spec:
  podSelector:
    matchLabels:
      app: mobile-automation-api
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: nginx-ingress
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: postgres
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
```

#### **Pod Security Standards**
```yaml
apiVersion: v1
kind: Pod
metadata:
  name: mobile-automation-api
  namespace: mobile-automation-prod
spec:
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    runAsGroup: 1000
    fsGroup: 1000
    seccompProfile:
      type: RuntimeDefault
  containers:
  - name: api
    image: mobile-automation:latest
    securityContext:
      allowPrivilegeEscalation: false
      readOnlyRootFilesystem: true
      capabilities:
        drop:
        - ALL
    resources:
      limits:
        memory: "512Mi"
        cpu: "500m"
      requests:
        memory: "256Mi"
        cpu: "250m"
    volumeMounts:
    - name: tmp
      mountPath: /tmp
    - name: var-run
      mountPath: /var/run
  volumes:
  - name: tmp
    emptyDir: {}
  - name: var-run
    emptyDir: {}
```

### 4.2 Secrets Management

#### **HashiCorp Vault Integration**
```python
import hvac
from flask import current_app

class VaultSecretManager:
    def __init__(self):
        self.client = hvac.Client(
            url=current_app.config['VAULT_URL'],
            token=current_app.config['VAULT_TOKEN']
        )
    
    def get_tenant_secrets(self, tenant_id):
        """Retrieve tenant-specific secrets"""
        try:
            secret_path = f"secret/tenants/{tenant_id}"
            response = self.client.secrets.kv.v2.read_secret_version(
                path=secret_path
            )
            return response['data']['data']
        except Exception as e:
            current_app.logger.error(f"Failed to retrieve secrets for tenant {tenant_id}: {e}")
            return None
    
    def store_tenant_secret(self, tenant_id, key, value):
        """Store tenant-specific secret"""
        try:
            secret_path = f"secret/tenants/{tenant_id}"
            
            # Get existing secrets
            existing_secrets = self.get_tenant_secrets(tenant_id) or {}
            
            # Update with new secret
            existing_secrets[key] = value
            
            # Store updated secrets
            self.client.secrets.kv.v2.create_or_update_secret(
                path=secret_path,
                secret=existing_secrets
            )
            return True
        except Exception as e:
            current_app.logger.error(f"Failed to store secret for tenant {tenant_id}: {e}")
            return False
    
    def rotate_tenant_keys(self, tenant_id):
        """Rotate encryption keys for tenant"""
        new_key = os.urandom(32).hex()
        return self.store_tenant_secret(tenant_id, 'encryption_key', new_key)
```

## 5. Compliance Framework

### 5.1 GDPR Compliance

#### **Data Processing Principles**
- **Lawfulness**: Explicit consent for data processing
- **Purpose Limitation**: Data used only for stated purposes
- **Data Minimization**: Collect only necessary data
- **Accuracy**: Maintain accurate and up-to-date data
- **Storage Limitation**: Retain data only as long as necessary
- **Security**: Implement appropriate security measures

#### **Data Subject Rights Implementation**
```python
class GDPRComplianceManager:
    def __init__(self, db):
        self.db = db
    
    def export_user_data(self, user_id):
        """Export all user data (Right to Data Portability)"""
        user_data = {
            'personal_info': self.get_user_profile(user_id),
            'test_results': self.get_user_test_results(user_id),
            'device_sessions': self.get_user_device_sessions(user_id),
            'audit_logs': self.get_user_audit_logs(user_id)
        }
        return user_data
    
    def delete_user_data(self, user_id):
        """Delete all user data (Right to Erasure)"""
        try:
            # Anonymize test results (keep for analytics)
            self.db.session.execute(
                "UPDATE test_results SET user_id = NULL WHERE user_id = :user_id",
                {"user_id": user_id}
            )
            
            # Delete personal data
            self.db.session.execute(
                "DELETE FROM user_profiles WHERE user_id = :user_id",
                {"user_id": user_id}
            )
            
            # Delete device sessions
            self.db.session.execute(
                "DELETE FROM device_sessions WHERE user_id = :user_id",
                {"user_id": user_id}
            )
            
            self.db.session.commit()
            return True
        except Exception as e:
            self.db.session.rollback()
            raise e
    
    def consent_management(self, user_id, consent_type, granted):
        """Manage user consent (Consent Management)"""
        consent_record = {
            'user_id': user_id,
            'consent_type': consent_type,
            'granted': granted,
            'timestamp': datetime.utcnow(),
            'ip_address': request.remote_addr
        }
        
        self.db.session.execute(
            "INSERT INTO consent_records (user_id, consent_type, granted, timestamp, ip_address) "
            "VALUES (:user_id, :consent_type, :granted, :timestamp, :ip_address)",
            consent_record
        )
        self.db.session.commit()
```

### 5.2 SOC 2 Type II Compliance

#### **Security Controls Implementation**

**CC6.1 - Logical and Physical Access Controls**
- Multi-factor authentication for all admin access
- Role-based access control (RBAC)
- Regular access reviews and deprovisioning
- Physical security for data centers (cloud provider responsibility)

**CC6.2 - System Access Monitoring**
```python
class AccessMonitoring:
    def log_access_attempt(self, user_id, resource, action, success):
        """Log all access attempts for audit trail"""
        log_entry = {
            'timestamp': datetime.utcnow(),
            'user_id': user_id,
            'resource': resource,
            'action': action,
            'success': success,
            'ip_address': request.remote_addr,
            'user_agent': request.headers.get('User-Agent')
        }
        
        # Store in secure audit log
        self.audit_logger.info(json.dumps(log_entry))
        
        # Alert on suspicious activity
        if not success:
            self.check_for_brute_force(user_id)
    
    def check_for_brute_force(self, user_id):
        """Detect and respond to brute force attacks"""
        recent_failures = self.count_recent_failures(user_id, minutes=15)
        if recent_failures >= 5:
            self.lock_account(user_id)
            self.send_security_alert(user_id)
```

**CC6.3 - Data Encryption**
- Encryption at rest (AES-256)
- Encryption in transit (TLS 1.3)
- Key management and rotation
- Secure key storage (HashiCorp Vault)

This security and compliance framework ensures enterprise-grade protection while maintaining the flexibility needed for a growing SaaS platform.
