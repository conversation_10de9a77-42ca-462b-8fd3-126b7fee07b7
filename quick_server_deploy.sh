#!/bin/bash
# Quick Server Deployment Script
# Run this single command on your server after SSH login

set -e

echo "🚀 Starting Quick Server Deployment..."

# Check if we're in the right directory
if [ ! -f "start_saas_platform.py" ]; then
    echo "❌ Error: start_saas_platform.py not found. Please navigate to your project directory first."
    echo "Run: cd /path/to/your/MobileApp-AutoTest"
    exit 1
fi

# Make scripts executable
chmod +x deploy_authentication_updates.sh 2>/dev/null || true
chmod +x setup_server_database.py 2>/dev/null || true
chmod +x test_complete_authentication_flow.py 2>/dev/null || true

# Run deployment
echo "📦 Running deployment script..."
if [ -f "deploy_authentication_updates.sh" ]; then
    ./deploy_authentication_updates.sh
else
    echo "⚠️  deploy_authentication_updates.sh not found, continuing with manual setup..."
    
    # Manual setup
    if [ -d "venv" ]; then
        source venv/bin/activate
    else
        python3 -m venv venv
        source venv/bin/activate
    fi
    
    pip install --upgrade pip
    pip install psycopg2-binary flask flask-jwt-extended flask-cors sqlalchemy requests flask-socketio
fi

# Activate virtual environment
source venv/bin/activate

# Set up database
echo "🗄️  Setting up database..."
python3 setup_server_database.py

# Test the setup
echo "🧪 Testing authentication flow..."
python3 test_complete_authentication_flow.py &
TEST_PID=$!

# Start the platform
echo "🚀 Starting SaaS platform..."
echo "Platform will be available at:"
echo "  - Main app: http://localhost:5000"
echo "  - iOS automation: http://localhost:8080" 
echo "  - Android automation: http://localhost:8081"
echo ""
echo "Test tenant access: http://testcompany1.localhost:5000"
echo "Test credentials: <EMAIL> / testpass123"
echo ""
echo "Press Ctrl+C to stop all services"

python3 start_saas_platform.py run
