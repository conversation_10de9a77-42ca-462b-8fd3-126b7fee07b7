# Standalone Mobile Automation App Requirements

# Core Flask and web framework
Flask==2.3.3
Werkzeug==2.3.7
Jinja2==3.1.2

# Authentication and security
PyJWT==2.8.0
bcrypt==4.0.1
cryptography==41.0.4
pyotp==2.9.0
qrcode[pil]==7.4.2
Pillow==10.0.1

# Database (SQLite for standalone)
sqlite3  # Built into Python

# Hardware fingerprinting
psutil==5.9.6  # System information
platform  # Built into Python

# Packaging and obfuscation
PyInstaller==6.3.0
pyarmor==8.4.6  # Code obfuscation
upx==4.2.1  # Binary compression

# Web server
waitress==2.1.2  # Production WSGI server

# Existing mobile automation dependencies
setuptools>=65.5.1
wheel>=0.38.0
numpy>=1.24.2
Pillow>=10.0.0
requests>=2.28.0
opencv-python>=4.8.0
flask-socketio==5.3.3
python-engineio==4.4.1
python-socketio==5.8.0
pytesseract>=0.3.10
pure-python-adb==0.3.0.dev0
adbutils>=2.8.0
uiautomator2>=3.2.9
scikit-image>=0.20.0
scipy>=1.15.0
facebook-wda>=1.5.0
tidevice>=0.12.0
Appium-Python-Client>=5.0.0
webdriver-manager>=3.8.6
selenium>=4.10.0
cython==3.0.9
gevent==23.9.1
gevent-websocket==0.10.1
PyYAML>=6.0.0
tqdm>=4.66.0
docker>=6.1.0
psycopg2-binary>=2.9.0

# Additional security libraries
pycryptodome==3.19.0  # Advanced encryption
keyring==24.3.0  # Secure credential storage
