#!/usr/bin/env python3
"""
Complete Build and Deployment Script for Mobile Automation App
Handles the entire process from source to distributable packages.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path
import logging
import argparse

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import our build modules
from build_executable import ExecutableBuilder
from package import CrossPlatformPackager


class DeploymentManager:
    """Manages the complete deployment process"""
    
    def __init__(self):
        """Initialize deployment manager"""
        self.project_root = Path(__file__).parent
        self.builder = ExecutableBuilder()
        self.packager = CrossPlatformPackager()
    
    def check_dependencies(self):
        """Check if all required dependencies are installed"""
        logger.info("Checking dependencies...")
        
        required_packages = [
            'PyInstaller',
            'flask',
            'waitress',
            'bcrypt',
            'pyotp',
            'qrcode',
            'cryptography',
            'PyJWT',
            'psutil'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.lower().replace('-', '_'))
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            logger.error(f"Missing required packages: {missing_packages}")
            logger.info("Install missing packages with:")
            logger.info(f"pip install {' '.join(missing_packages)}")
            return False
        
        logger.info("All dependencies satisfied")
        return True
    
    def install_dependencies(self):
        """Install required dependencies"""
        logger.info("Installing dependencies...")
        
        requirements_file = self.project_root / "requirements.txt"
        
        if requirements_file.exists():
            cmd = [sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"Failed to install dependencies: {result.stderr}")
                return False
            
            logger.info("Dependencies installed successfully")
            return True
        else:
            logger.error("requirements.txt not found")
            return False
    
    def run_tests(self):
        """Run basic tests to ensure functionality"""
        logger.info("Running basic functionality tests...")
        
        try:
            # Test imports
            from auth.secure_auth import SecureAuthManager
            from security.anti_reverse import AntiReverseProtection
            from security.obfuscation import MasterObfuscator
            
            # Test basic functionality
            auth_manager = SecureAuthManager(":memory:")
            protection = AntiReverseProtection()
            obfuscator = MasterObfuscator()
            
            logger.info("Basic functionality tests passed")
            return True
            
        except Exception as e:
            logger.error(f"Functionality tests failed: {e}")
            return False
    
    def build_executable(self):
        """Build the executable"""
        logger.info("Building executable...")
        
        try:
            dist_package = self.builder.build()
            logger.info(f"Executable built successfully: {dist_package}")
            return dist_package
            
        except Exception as e:
            logger.error(f"Executable build failed: {e}")
            return None
    
    def create_packages(self, dist_package):
        """Create distribution packages"""
        logger.info("Creating distribution packages...")
        
        try:
            package_path = self.packager.create_package(dist_package)
            logger.info(f"Package created successfully: {package_path}")
            return package_path
            
        except Exception as e:
            logger.error(f"Package creation failed: {e}")
            return None
    
    def generate_deployment_report(self, dist_package, package_path):
        """Generate deployment report"""
        logger.info("Generating deployment report...")
        
        platform_info = {
            'system': platform.system(),
            'release': platform.release(),
            'machine': platform.machine(),
            'processor': platform.processor(),
            'python_version': platform.python_version()
        }
        
        report_content = f"""# Mobile Automation App - Deployment Report

## Build Information
- **Build Date**: {platform.node()}
- **Platform**: {platform_info['system']} {platform_info['release']}
- **Architecture**: {platform_info['machine']}
- **Python Version**: {platform_info['python_version']}

## Distribution Package
- **Location**: {dist_package}
- **Package**: {package_path}

## Security Features
- ✅ Anti-reverse engineering protection
- ✅ Code obfuscation
- ✅ Runtime integrity checks
- ✅ Anti-debugging measures
- ✅ Hardware fingerprinting
- ✅ Two-factor authentication
- ✅ Encrypted data storage

## Installation Instructions

### Windows
1. Extract the ZIP package
2. Run `install.bat` as Administrator
3. Follow the installation prompts
4. Launch from desktop shortcut or Start menu

### macOS
1. Extract the tar.gz package
2. Run `sudo ./install.sh`
3. Launch from Applications folder or desktop

### Linux
1. Extract the tar.gz package
2. Run `sudo ./install.sh`
3. Launch from applications menu or run `mobile-automation-app`

## Default Credentials
- **Email**: <EMAIL>
- **Password**: SecureAdmin123!

⚠️ **IMPORTANT**: Change the default password immediately after first login!

## Network Requirements
- **Main Interface**: Port 3000
- **iOS Dashboard**: Port 8080
- **Android Dashboard**: Port 8081

## Support
For technical support, please contact your system administrator.

## Security Notice
This application includes enterprise-grade security features:
- All communications are encrypted
- User sessions are monitored and logged
- Anti-tampering protection is active
- Unauthorized access attempts are blocked

Do not attempt to reverse engineer or modify this application.
"""
        
        report_file = self.project_root / "DEPLOYMENT_REPORT.md"
        with open(report_file, 'w') as f:
            f.write(report_content)
        
        logger.info(f"Deployment report generated: {report_file}")
        return report_file
    
    def deploy(self, install_deps=False, run_tests=False):
        """Complete deployment process"""
        logger.info("Starting complete deployment process...")
        
        try:
            # Check dependencies
            if not self.check_dependencies():
                if install_deps:
                    if not self.install_dependencies():
                        return False
                else:
                    logger.error("Dependencies not satisfied. Use --install-deps to install them.")
                    return False
            
            # Run tests if requested
            if run_tests:
                if not self.run_tests():
                    logger.error("Tests failed. Aborting deployment.")
                    return False
            
            # Build executable
            dist_package = self.build_executable()
            if not dist_package:
                return False
            
            # Create packages
            package_path = self.create_packages(dist_package)
            if not package_path:
                return False
            
            # Generate report
            report_file = self.generate_deployment_report(dist_package, package_path)
            
            # Success summary
            logger.info("Deployment completed successfully!")
            print("\n" + "="*60)
            print("🎉 DEPLOYMENT SUCCESSFUL!")
            print("="*60)
            print(f"📦 Distribution Package: {dist_package}")
            print(f"📋 Final Package: {package_path}")
            print(f"📄 Report: {report_file}")
            print("="*60)
            print("🚀 Ready for distribution to subscribers!")
            print("="*60 + "\n")
            
            return True
            
        except Exception as e:
            logger.error(f"Deployment failed: {e}")
            return False


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Complete build and deployment for Mobile Automation App"
    )
    
    parser.add_argument(
        '--install-deps',
        action='store_true',
        help='Automatically install missing dependencies'
    )
    
    parser.add_argument(
        '--run-tests',
        action='store_true',
        help='Run functionality tests before building'
    )
    
    parser.add_argument(
        '--clean',
        action='store_true',
        help='Clean build artifacts before starting'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Create deployment manager
    deployment_manager = DeploymentManager()
    
    # Clean if requested
    if args.clean:
        logger.info("Cleaning build artifacts...")
        deployment_manager.builder.clean_build()
    
    # Run deployment
    success = deployment_manager.deploy(
        install_deps=args.install_deps,
        run_tests=args.run_tests
    )
    
    if success:
        print("\n✅ Deployment completed successfully!")
        print("📦 Your secure mobile automation app is ready for distribution!")
        sys.exit(0)
    else:
        print("\n❌ Deployment failed!")
        print("Check the logs above for details.")
        sys.exit(1)


if __name__ == '__main__':
    main()
