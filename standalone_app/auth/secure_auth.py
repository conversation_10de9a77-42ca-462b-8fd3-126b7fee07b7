"""
Secure Authentication System for Standalone Mobile Automation App
Implements 2FA, hardware fingerprinting, JWT tokens, and anti-brute force protection.
"""

import os
import json
import time
import hashlib
import secrets
import sqlite3
import platform
import psutil
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Optional, Tuple, List
import logging

import bcrypt
import pyotp
import qrcode
import jwt
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
from io import BytesIO

logger = logging.getLogger(__name__)


class SecureAuthManager:
    """Secure authentication manager with enterprise-grade security"""
    
    def __init__(self, db_path: str = "auth.db", master_key: str = None):
        """Initialize secure authentication manager"""
        self.db_path = db_path
        self.master_key = master_key or self._generate_master_key()
        self.fernet = self._init_encryption()
        
        # Security settings
        self.max_login_attempts = 5
        self.lockout_duration = 1800  # 30 minutes in seconds
        self.session_timeout = 86400  # 24 hours in seconds
        self.totp_window = 2  # Allow 2 time windows
        
        # Initialize database
        self._init_database()
        
        # Hardware fingerprint cache
        self._hw_fingerprint = None
    
    def _generate_master_key(self) -> str:
        """Generate a master encryption key"""
        # In production, this should be derived from user input or secure storage
        return base64.urlsafe_b64encode(os.urandom(32)).decode()
    
    def _init_encryption(self) -> Fernet:
        """Initialize encryption with master key"""
        key = base64.urlsafe_b64encode(
            hashlib.sha256(self.master_key.encode()).digest()
        )
        return Fernet(key)
    
    def _init_database(self):
        """Initialize SQLite database with security tables"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id TEXT PRIMARY KEY,
                    email TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    first_name TEXT,
                    last_name TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login_at TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS user_mfa (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    secret_encrypted TEXT NOT NULL,
                    backup_codes_encrypted TEXT,
                    enabled BOOLEAN DEFAULT 0,
                    verified_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS device_fingerprints (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    fingerprint_hash TEXT NOT NULL,
                    device_info TEXT,
                    is_trusted BOOLEAN DEFAULT 0,
                    first_seen_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_seen_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS login_attempts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email TEXT,
                    ip_address TEXT,
                    success BOOLEAN,
                    attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    details TEXT
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    token_hash TEXT NOT NULL,
                    device_fingerprint TEXT,
                    expires_at TIMESTAMP NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)
            
            conn.commit()
    
    def create_user(self, email: str, password: str, first_name: str = "", 
                   last_name: str = "") -> Tuple[bool, str]:
        """Create a new user account"""
        try:
            user_id = secrets.token_urlsafe(16)
            password_hash = bcrypt.hashpw(password.encode(), bcrypt.gensalt()).decode()
            
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO users (id, email, password_hash, first_name, last_name)
                    VALUES (?, ?, ?, ?, ?)
                """, (user_id, email, password_hash, first_name, last_name))
                conn.commit()
            
            logger.info(f"User created: {email}")
            return True, user_id
            
        except sqlite3.IntegrityError:
            return False, "Email already exists"
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            return False, "Failed to create user"
    
    def authenticate(self, email: str, password: str, totp_code: str = None) -> Tuple[bool, Dict]:
        """Authenticate user with optional 2FA"""
        try:
            # Check rate limiting
            if self._is_rate_limited(email):
                self._log_attempt(email, False, "Rate limited")
                return False, {"error": "Too many failed attempts. Please try again later."}
            
            # Get user
            user = self._get_user_by_email(email)
            if not user:
                self._log_attempt(email, False, "User not found")
                return False, {"error": "Invalid credentials"}
            
            # Verify password
            if not bcrypt.checkpw(password.encode(), user['password_hash'].encode()):
                self._log_attempt(email, False, "Invalid password")
                return False, {"error": "Invalid credentials"}
            
            # Check if 2FA is enabled
            mfa_enabled = self._is_mfa_enabled(user['id'])
            
            # Generate device fingerprint
            device_fingerprint = self._generate_device_fingerprint()
            is_trusted_device = self._is_trusted_device(user['id'], device_fingerprint)
            
            # Require 2FA if enabled and (not trusted device or code provided)
            if mfa_enabled and (not is_trusted_device or totp_code):
                if not totp_code:
                    return False, {
                        "error": "Two-factor authentication required",
                        "requires_2fa": True,
                        "trusted_device": is_trusted_device
                    }
                
                if not self._verify_totp(user['id'], totp_code):
                    self._log_attempt(email, False, "Invalid 2FA code")
                    return False, {"error": "Invalid two-factor authentication code"}
            
            # Authentication successful
            session_data = self._create_session(user['id'], device_fingerprint)
            self._update_last_login(user['id'])
            self._log_attempt(email, True, "Login successful")
            
            # Register/update device fingerprint
            if not is_trusted_device:
                self._register_device_fingerprint(user['id'], device_fingerprint)
            else:
                self._update_device_last_seen(user['id'], device_fingerprint)
            
            return True, {
                "user": {
                    "id": user['id'],
                    "email": user['email'],
                    "first_name": user['first_name'],
                    "last_name": user['last_name']
                },
                "session": session_data,
                "mfa_enabled": mfa_enabled,
                "trusted_device": is_trusted_device
            }
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return False, {"error": "Authentication failed"}
    
    def setup_2fa(self, user_id: str, email: str) -> Dict:
        """Setup 2FA for user"""
        try:
            # Generate TOTP secret
            secret = pyotp.random_base32()
            totp = pyotp.TOTP(secret)
            
            # Generate QR code
            provisioning_uri = totp.provisioning_uri(
                name=email,
                issuer_name="Mobile Automation App"
            )
            
            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data(provisioning_uri)
            qr.make(fit=True)
            
            img = qr.make_image(fill_color="black", back_color="white")
            buffer = BytesIO()
            img.save(buffer, format='PNG')
            qr_code_data = base64.b64encode(buffer.getvalue()).decode()
            
            # Generate backup codes
            backup_codes = [secrets.token_hex(4).upper() for _ in range(10)]
            
            # Encrypt and store
            secret_encrypted = self.fernet.encrypt(secret.encode()).decode()
            backup_codes_encrypted = self.fernet.encrypt(
                json.dumps(backup_codes).encode()
            ).decode()
            
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO user_mfa 
                    (id, user_id, secret_encrypted, backup_codes_encrypted, enabled)
                    VALUES (?, ?, ?, ?, 0)
                """, (secrets.token_urlsafe(16), user_id, secret_encrypted, backup_codes_encrypted))
                conn.commit()
            
            return {
                "secret": secret,
                "qr_code": qr_code_data,
                "backup_codes": backup_codes,
                "provisioning_uri": provisioning_uri
            }
            
        except Exception as e:
            logger.error(f"Error setting up 2FA: {e}")
            raise
    
    def verify_2fa_setup(self, user_id: str, token: str) -> bool:
        """Verify 2FA setup and enable it"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT secret_encrypted FROM user_mfa 
                    WHERE user_id = ? AND enabled = 0
                """, (user_id,))
                result = cursor.fetchone()
                
                if not result:
                    return False
                
                # Decrypt secret and verify token
                secret = self.fernet.decrypt(result[0].encode()).decode()
                totp = pyotp.TOTP(secret)
                
                if totp.verify(token, valid_window=self.totp_window):
                    # Enable 2FA
                    conn.execute("""
                        UPDATE user_mfa 
                        SET enabled = 1, verified_at = CURRENT_TIMESTAMP
                        WHERE user_id = ?
                    """, (user_id,))
                    conn.commit()
                    return True
                
                return False
                
        except Exception as e:
            logger.error(f"Error verifying 2FA setup: {e}")
            return False
    
    def _verify_totp(self, user_id: str, token: str) -> bool:
        """Verify TOTP token"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT secret_encrypted, backup_codes_encrypted FROM user_mfa 
                    WHERE user_id = ? AND enabled = 1
                """, (user_id,))
                result = cursor.fetchone()
                
                if not result:
                    return False
                
                # Check backup codes first
                if result[1]:
                    backup_codes = json.loads(
                        self.fernet.decrypt(result[1].encode()).decode()
                    )
                    if token.upper() in backup_codes:
                        # Remove used backup code
                        backup_codes.remove(token.upper())
                        new_backup_codes = self.fernet.encrypt(
                            json.dumps(backup_codes).encode()
                        ).decode()
                        conn.execute("""
                            UPDATE user_mfa 
                            SET backup_codes_encrypted = ?
                            WHERE user_id = ?
                        """, (new_backup_codes, user_id))
                        conn.commit()
                        return True
                
                # Verify TOTP
                secret = self.fernet.decrypt(result[0].encode()).decode()
                totp = pyotp.TOTP(secret)
                return totp.verify(token, valid_window=self.totp_window)
                
        except Exception as e:
            logger.error(f"Error verifying TOTP: {e}")
            return False
    
    def _generate_device_fingerprint(self) -> str:
        """Generate hardware fingerprint"""
        if self._hw_fingerprint:
            return self._hw_fingerprint
        
        try:
            # Collect system information
            system_info = {
                'platform': platform.platform(),
                'processor': platform.processor(),
                'machine': platform.machine(),
                'node': platform.node(),
                'cpu_count': psutil.cpu_count(),
                'total_memory': psutil.virtual_memory().total,
                'disk_usage': psutil.disk_usage('/').total if os.name != 'nt' else psutil.disk_usage('C:').total
            }
            
            # Create fingerprint hash
            fingerprint_data = json.dumps(system_info, sort_keys=True)
            self._hw_fingerprint = hashlib.sha256(fingerprint_data.encode()).hexdigest()
            return self._hw_fingerprint
            
        except Exception as e:
            logger.error(f"Error generating device fingerprint: {e}")
            return hashlib.sha256(b"fallback_fingerprint").hexdigest()
    
    def _is_trusted_device(self, user_id: str, fingerprint: str) -> bool:
        """Check if device is trusted"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT is_trusted FROM device_fingerprints 
                    WHERE user_id = ? AND fingerprint_hash = ?
                """, (user_id, fingerprint))
                result = cursor.fetchone()
                return result and result[0]
        except Exception:
            return False
    
    def _register_device_fingerprint(self, user_id: str, fingerprint: str):
        """Register new device fingerprint"""
        try:
            device_info = {
                'platform': platform.platform(),
                'processor': platform.processor(),
                'timestamp': datetime.now().isoformat()
            }
            
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO device_fingerprints 
                    (id, user_id, fingerprint_hash, device_info, is_trusted)
                    VALUES (?, ?, ?, ?, 0)
                """, (secrets.token_urlsafe(16), user_id, fingerprint, json.dumps(device_info)))
                conn.commit()
        except Exception as e:
            logger.error(f"Error registering device fingerprint: {e}")
    
    def _update_device_last_seen(self, user_id: str, fingerprint: str):
        """Update device last seen timestamp"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    UPDATE device_fingerprints 
                    SET last_seen_at = CURRENT_TIMESTAMP
                    WHERE user_id = ? AND fingerprint_hash = ?
                """, (user_id, fingerprint))
                conn.commit()
        except Exception as e:
            logger.error(f"Error updating device last seen: {e}")
    
    def trust_device(self, user_id: str, fingerprint: str) -> bool:
        """Mark device as trusted"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    UPDATE device_fingerprints 
                    SET is_trusted = 1
                    WHERE user_id = ? AND fingerprint_hash = ?
                """, (user_id, fingerprint))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error trusting device: {e}")
            return False
    
    def _is_rate_limited(self, email: str) -> bool:
        """Check if user is rate limited"""
        try:
            cutoff_time = datetime.now() - timedelta(seconds=self.lockout_duration)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT COUNT(*) FROM login_attempts 
                    WHERE email = ? AND success = 0 AND attempt_time > ?
                """, (email, cutoff_time))
                failed_attempts = cursor.fetchone()[0]
                
                return failed_attempts >= self.max_login_attempts
        except Exception:
            return False
    
    def _log_attempt(self, email: str, success: bool, details: str):
        """Log login attempt"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO login_attempts (email, success, details)
                    VALUES (?, ?, ?)
                """, (email, success, details))
                conn.commit()
        except Exception as e:
            logger.error(f"Error logging attempt: {e}")
    
    def _create_session(self, user_id: str, device_fingerprint: str) -> Dict:
        """Create user session"""
        try:
            session_id = secrets.token_urlsafe(32)
            token = jwt.encode({
                'user_id': user_id,
                'session_id': session_id,
                'device_fingerprint': device_fingerprint,
                'exp': datetime.utcnow() + timedelta(seconds=self.session_timeout),
                'iat': datetime.utcnow()
            }, self.master_key, algorithm='HS256')
            
            token_hash = hashlib.sha256(token.encode()).hexdigest()
            expires_at = datetime.now() + timedelta(seconds=self.session_timeout)
            
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO user_sessions 
                    (id, user_id, token_hash, device_fingerprint, expires_at)
                    VALUES (?, ?, ?, ?, ?)
                """, (session_id, user_id, token_hash, device_fingerprint, expires_at))
                conn.commit()
            
            return {
                'session_id': session_id,
                'token': token,
                'expires_at': expires_at.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            raise
    
    def verify_session(self, token: str) -> Tuple[bool, Optional[Dict]]:
        """Verify session token"""
        try:
            # Decode JWT
            payload = jwt.decode(token, self.master_key, algorithms=['HS256'])
            user_id = payload['user_id']
            session_id = payload['session_id']
            
            # Check session in database
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT user_id, expires_at, is_active FROM user_sessions 
                    WHERE id = ? AND user_id = ?
                """, (session_id, user_id))
                session = cursor.fetchone()
                
                if not session or not session[2]:  # not active
                    return False, None
                
                # Check expiration
                expires_at = datetime.fromisoformat(session[1])
                if expires_at < datetime.now():
                    self._invalidate_session(session_id)
                    return False, None
                
                # Get user data
                user = self._get_user_by_id(user_id)
                if not user:
                    return False, None
                
                # Update last activity
                conn.execute("""
                    UPDATE user_sessions 
                    SET last_activity = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (session_id,))
                conn.commit()
                
                return True, {
                    'user': user,
                    'session_id': session_id,
                    'payload': payload
                }
                
        except jwt.ExpiredSignatureError:
            return False, None
        except jwt.InvalidTokenError:
            return False, None
        except Exception as e:
            logger.error(f"Error verifying session: {e}")
            return False, None
    
    def _invalidate_session(self, session_id: str):
        """Invalidate session"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    UPDATE user_sessions 
                    SET is_active = 0
                    WHERE id = ?
                """, (session_id,))
                conn.commit()
        except Exception as e:
            logger.error(f"Error invalidating session: {e}")
    
    def _get_user_by_email(self, email: str) -> Optional[Dict]:
        """Get user by email"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT * FROM users WHERE email = ? AND is_active = 1
                """, (email,))
                result = cursor.fetchone()
                return dict(result) if result else None
        except Exception:
            return None
    
    def _get_user_by_id(self, user_id: str) -> Optional[Dict]:
        """Get user by ID"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT * FROM users WHERE id = ? AND is_active = 1
                """, (user_id,))
                result = cursor.fetchone()
                return dict(result) if result else None
        except Exception:
            return None
    
    def _is_mfa_enabled(self, user_id: str) -> bool:
        """Check if MFA is enabled for user"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT enabled FROM user_mfa WHERE user_id = ?
                """, (user_id,))
                result = cursor.fetchone()
                return result and result[0]
        except Exception:
            return False
    
    def _update_last_login(self, user_id: str):
        """Update user's last login timestamp"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    UPDATE users 
                    SET last_login_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (user_id,))
                conn.commit()
        except Exception as e:
            logger.error(f"Error updating last login: {e}")
    
    def logout(self, session_id: str) -> bool:
        """Logout user"""
        try:
            self._invalidate_session(session_id)
            return True
        except Exception:
            return False
