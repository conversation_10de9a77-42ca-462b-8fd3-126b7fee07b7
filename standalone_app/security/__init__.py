"""
Security Module for Standalone Mobile Automation App
Provides anti-reverse engineering protection and code obfuscation.
"""

from .anti_reverse import (
    AntiReverseProtection,
    init_protection,
    check_protection,
    disable_protection,
    protected
)

from .obfuscation import (
    CodeObfuscator,
    ControlFlowObfuscator,
    AntiAnalysisObfuscator,
    BytecodeObfuscator,
    MasterObfuscator,
    get_obfuscator
)

__all__ = [
    'AntiReverseProtection',
    'init_protection',
    'check_protection', 
    'disable_protection',
    'protected',
    'CodeObfuscator',
    'ControlFlowObfuscator',
    'AntiAnalysisObfuscator',
    'BytecodeObfuscator',
    'MasterObfuscator',
    'get_obfuscator'
]
