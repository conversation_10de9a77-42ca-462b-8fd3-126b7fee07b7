"""
Anti-Reverse Engineering Protection System
Implements multiple layers of protection against decompilation and analysis.
"""

import os
import sys
import time
import hashlib
import threading
import platform
import subprocess
from typing import Dict, List, Optional
import logging

logger = logging.getLogger(__name__)


class AntiReverseProtection:
    """Anti-reverse engineering protection system"""
    
    def __init__(self):
        """Initialize protection system"""
        self.protection_enabled = True
        self.integrity_hash = None
        self.debug_detected = False
        self.vm_detected = False
        
        # Initialize protection layers
        self._init_integrity_check()
        self._start_monitoring()
    
    def _init_integrity_check(self):
        """Initialize integrity checking"""
        try:
            # Calculate hash of current executable
            if hasattr(sys, 'frozen') and sys.frozen:
                # Running as PyInstaller executable
                exe_path = sys.executable
            else:
                # Running as Python script
                exe_path = __file__
            
            with open(exe_path, 'rb') as f:
                content = f.read()
                self.integrity_hash = hashlib.sha256(content).hexdigest()
                
        except Exception as e:
            logger.warning(f"Could not initialize integrity check: {e}")
    
    def _start_monitoring(self):
        """Start background monitoring threads"""
        if not self.protection_enabled:
            return
        
        # Start anti-debugging monitor
        debug_thread = threading.Thread(target=self._monitor_debugging, daemon=True)
        debug_thread.start()
        
        # Start VM detection monitor
        vm_thread = threading.Thread(target=self._monitor_vm, daemon=True)
        vm_thread.start()
        
        # Start integrity monitor
        integrity_thread = threading.Thread(target=self._monitor_integrity, daemon=True)
        integrity_thread.start()
    
    def _monitor_debugging(self):
        """Monitor for debugging attempts"""
        while self.protection_enabled:
            try:
                # Check for common debugger processes
                if self._detect_debugger_processes():
                    self.debug_detected = True
                    self._trigger_protection("Debugger detected")
                
                # Check for debugging flags
                if self._detect_debug_flags():
                    self.debug_detected = True
                    self._trigger_protection("Debug flags detected")
                
                # Check for timing attacks
                if self._detect_timing_attacks():
                    self.debug_detected = True
                    self._trigger_protection("Timing attack detected")
                
                time.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                logger.error(f"Debug monitoring error: {e}")
                time.sleep(10)
    
    def _monitor_vm(self):
        """Monitor for virtual machine environment"""
        while self.protection_enabled:
            try:
                if self._detect_virtual_machine():
                    self.vm_detected = True
                    self._trigger_protection("Virtual machine detected")
                
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"VM monitoring error: {e}")
                time.sleep(60)
    
    def _monitor_integrity(self):
        """Monitor file integrity"""
        while self.protection_enabled:
            try:
                if not self._verify_integrity():
                    self._trigger_protection("File integrity violation")
                
                time.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Integrity monitoring error: {e}")
                time.sleep(120)
    
    def _detect_debugger_processes(self) -> bool:
        """Detect common debugger processes"""
        try:
            import psutil
            
            debugger_names = [
                'gdb', 'lldb', 'windbg', 'x64dbg', 'x32dbg', 'ollydbg',
                'ida', 'ida64', 'idaq', 'idaq64', 'idaw', 'idaw64',
                'radare2', 'r2', 'ghidra', 'cheat engine', 'processhacker',
                'procmon', 'procexp', 'wireshark', 'fiddler', 'burpsuite'
            ]
            
            for proc in psutil.process_iter(['name']):
                try:
                    proc_name = proc.info['name'].lower()
                    if any(debugger in proc_name for debugger in debugger_names):
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return False
            
        except ImportError:
            # psutil not available, use basic check
            return False
        except Exception:
            return False
    
    def _detect_debug_flags(self) -> bool:
        """Detect debugging flags and environment variables"""
        try:
            # Check environment variables
            debug_vars = [
                'PYTHONDEBUG', 'PYTHONINSPECT', 'PYTHONVERBOSE',
                'DEBUG', '_DEBUG', 'PYDEBUG'
            ]
            
            for var in debug_vars:
                if os.environ.get(var):
                    return True
            
            # Check Python debugging flags
            if hasattr(sys, 'gettrace') and sys.gettrace() is not None:
                return True
            
            # Check for pdb
            if 'pdb' in sys.modules:
                return True
            
            return False
            
        except Exception:
            return False
    
    def _detect_timing_attacks(self) -> bool:
        """Detect timing-based analysis attempts"""
        try:
            # Measure execution time of simple operations
            start_time = time.perf_counter()
            
            # Simple computation
            result = sum(range(1000))
            
            end_time = time.perf_counter()
            execution_time = end_time - start_time
            
            # If execution is unusually slow, might be under analysis
            if execution_time > 0.01:  # 10ms threshold
                return True
            
            return False
            
        except Exception:
            return False
    
    def _detect_virtual_machine(self) -> bool:
        """Detect virtual machine environment"""
        try:
            vm_indicators = []
            
            # Check system manufacturer
            try:
                system_info = platform.uname()
                vm_vendors = ['vmware', 'virtualbox', 'qemu', 'xen', 'hyper-v', 'parallels']
                
                system_str = ' '.join([
                    system_info.system,
                    system_info.node,
                    system_info.machine,
                    system_info.processor
                ]).lower()
                
                for vendor in vm_vendors:
                    if vendor in system_str:
                        vm_indicators.append(f"VM vendor detected: {vendor}")
            except Exception:
                pass
            
            # Check for VM-specific files (Windows)
            if platform.system() == 'Windows':
                vm_files = [
                    'C:\\windows\\system32\\drivers\\vmmouse.sys',
                    'C:\\windows\\system32\\drivers\\vmhgfs.sys',
                    'C:\\windows\\system32\\drivers\\VBoxMouse.sys',
                    'C:\\windows\\system32\\drivers\\VBoxGuest.sys'
                ]
                
                for vm_file in vm_files:
                    if os.path.exists(vm_file):
                        vm_indicators.append(f"VM file detected: {vm_file}")
            
            # Check MAC address for VM patterns
            try:
                import uuid
                mac = uuid.getnode()
                mac_str = ':'.join(['{:02x}'.format((mac >> i) & 0xff) for i in range(0, 48, 8)])
                
                vm_mac_prefixes = ['00:0c:29', '00:1c:14', '00:50:56', '08:00:27']
                for prefix in vm_mac_prefixes:
                    if mac_str.startswith(prefix):
                        vm_indicators.append(f"VM MAC detected: {prefix}")
            except Exception:
                pass
            
            return len(vm_indicators) > 0
            
        except Exception:
            return False
    
    def _verify_integrity(self) -> bool:
        """Verify file integrity"""
        try:
            if not self.integrity_hash:
                return True  # Can't verify if no hash
            
            # Re-calculate hash
            if hasattr(sys, 'frozen') and sys.frozen:
                exe_path = sys.executable
            else:
                exe_path = __file__
            
            with open(exe_path, 'rb') as f:
                content = f.read()
                current_hash = hashlib.sha256(content).hexdigest()
            
            return current_hash == self.integrity_hash
            
        except Exception:
            return True  # Assume OK if can't verify
    
    def _trigger_protection(self, reason: str):
        """Trigger protection response"""
        logger.warning(f"Protection triggered: {reason}")
        
        # Multiple protection responses
        self._obfuscate_execution()
        self._create_decoy_processes()
        self._corrupt_memory()
        self._exit_application(reason)
    
    def _obfuscate_execution(self):
        """Obfuscate execution flow"""
        try:
            # Create random delays
            import random
            time.sleep(random.uniform(0.1, 0.5))
            
            # Execute dummy operations
            dummy_data = [random.randint(1, 1000) for _ in range(100)]
            dummy_result = sum(dummy_data) * len(dummy_data)
            
            # Create fake network activity
            try:
                import socket
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                sock.connect_ex(('127.0.0.1', 80))
                sock.close()
            except Exception:
                pass
                
        except Exception:
            pass
    
    def _create_decoy_processes(self):
        """Create decoy processes to confuse analysis"""
        try:
            # Create multiple dummy threads
            for i in range(3):
                thread = threading.Thread(target=self._dummy_worker, daemon=True)
                thread.start()
                
        except Exception:
            pass
    
    def _dummy_worker(self):
        """Dummy worker thread"""
        try:
            import random
            while True:
                # Random computation
                data = [random.randint(1, 100) for _ in range(50)]
                result = sum(x * x for x in data)
                time.sleep(random.uniform(1, 5))
        except Exception:
            pass
    
    def _corrupt_memory(self):
        """Corrupt sensitive memory regions"""
        try:
            # Overwrite sensitive variables
            sensitive_vars = ['password', 'token', 'key', 'secret']
            
            # This is a placeholder - in real implementation,
            # you would overwrite actual sensitive memory
            for var_name in sensitive_vars:
                if var_name in globals():
                    globals()[var_name] = 'X' * 32
                    
        except Exception:
            pass
    
    def _exit_application(self, reason: str):
        """Exit application with protection message"""
        try:
            # Log the protection event
            logger.critical(f"Application terminated due to: {reason}")
            
            # Clear sensitive data
            self._clear_sensitive_data()
            
            # Exit with error code
            sys.exit(1)
            
        except Exception:
            os._exit(1)
    
    def _clear_sensitive_data(self):
        """Clear sensitive data from memory"""
        try:
            # This would clear actual sensitive data in a real implementation
            import gc
            gc.collect()
        except Exception:
            pass
    
    def check_environment(self) -> bool:
        """Check if environment is safe for execution"""
        if not self.protection_enabled:
            return True
        
        # Perform initial checks
        if self._detect_debugger_processes():
            self._trigger_protection("Initial debugger check failed")
            return False
        
        if self._detect_virtual_machine():
            logger.warning("Virtual machine detected - proceeding with caution")
            # Don't exit for VM, just log
        
        if not self._verify_integrity():
            self._trigger_protection("Initial integrity check failed")
            return False
        
        return True
    
    def disable_protection(self):
        """Disable protection (for development)"""
        self.protection_enabled = False
        logger.info("Anti-reverse engineering protection disabled")


# Global protection instance
_protection_instance = None


def init_protection() -> AntiReverseProtection:
    """Initialize protection system"""
    global _protection_instance
    if _protection_instance is None:
        _protection_instance = AntiReverseProtection()
    return _protection_instance


def check_protection() -> bool:
    """Check if protection allows execution"""
    protection = init_protection()
    return protection.check_environment()


def disable_protection():
    """Disable protection for development"""
    protection = init_protection()
    protection.disable_protection()


# Decorator for protecting functions
def protected(func):
    """Decorator to protect functions"""
    def wrapper(*args, **kwargs):
        if not check_protection():
            return None
        return func(*args, **kwargs)
    return wrapper
