"""
Code Obfuscation System
Provides multiple layers of code obfuscation and protection.
"""

import os
import sys
import base64
import zlib
import marshal
import random
import string
from typing import Dict, List, Any
import logging

logger = logging.getLogger(__name__)


class CodeObfuscator:
    """Advanced code obfuscation system"""
    
    def __init__(self):
        """Initialize obfuscator"""
        self.obfuscation_key = self._generate_key()
        self.string_map = {}
        self.function_map = {}
        
    def _generate_key(self) -> bytes:
        """Generate encryption key"""
        return os.urandom(32)
    
    def obfuscate_string(self, text: str) -> str:
        """Obfuscate string literals"""
        try:
            # Encode and compress
            encoded = text.encode('utf-8')
            compressed = zlib.compress(encoded)
            encrypted = self._xor_encrypt(compressed, self.obfuscation_key)
            
            # Base64 encode for storage
            b64_encoded = base64.b64encode(encrypted).decode('ascii')
            
            # Generate obfuscated variable name
            var_name = self._generate_var_name()
            self.string_map[text] = var_name
            
            return f"_decode_str('{b64_encoded}')"
            
        except Exception as e:
            logger.error(f"String obfuscation error: {e}")
            return repr(text)
    
    def obfuscate_function_name(self, name: str) -> str:
        """Obfuscate function names"""
        if name in self.function_map:
            return self.function_map[name]
        
        obfuscated = self._generate_var_name()
        self.function_map[name] = obfuscated
        return obfuscated
    
    def _generate_var_name(self) -> str:
        """Generate obfuscated variable name"""
        # Use confusing but valid Python identifiers
        prefixes = ['_', '__', '___']
        suffixes = ['_', '__', '___']
        
        # Mix of letters and numbers
        chars = string.ascii_letters + string.digits
        middle = ''.join(random.choices(chars, k=random.randint(8, 16)))
        
        prefix = random.choice(prefixes)
        suffix = random.choice(suffixes)
        
        return f"{prefix}{middle}{suffix}"
    
    def _xor_encrypt(self, data: bytes, key: bytes) -> bytes:
        """Simple XOR encryption"""
        result = bytearray()
        key_len = len(key)
        
        for i, byte in enumerate(data):
            result.append(byte ^ key[i % key_len])
        
        return bytes(result)
    
    def generate_decoder(self) -> str:
        """Generate decoder function"""
        key_b64 = base64.b64encode(self.obfuscation_key).decode('ascii')
        
        return f"""
import base64
import zlib

def _decode_str(encoded_str):
    try:
        key = base64.b64decode('{key_b64}')
        encrypted = base64.b64decode(encoded_str.encode('ascii'))
        
        # XOR decrypt
        result = bytearray()
        key_len = len(key)
        for i, byte in enumerate(encrypted):
            result.append(byte ^ key[i % key_len])
        
        # Decompress and decode
        compressed = bytes(result)
        decompressed = zlib.decompress(compressed)
        return decompressed.decode('utf-8')
    except:
        return ''
"""


class ControlFlowObfuscator:
    """Control flow obfuscation"""
    
    def __init__(self):
        """Initialize control flow obfuscator"""
        self.jump_table = {}
        self.state_counter = 0
    
    def obfuscate_if_statement(self, condition: str, true_block: str, false_block: str = "") -> str:
        """Obfuscate if statements using state machines"""
        state_var = f"_state_{self.state_counter}"
        self.state_counter += 1
        
        return f"""
{state_var} = 1 if ({condition}) else 2
if {state_var} == 1:
{self._indent_code(true_block)}
elif {state_var} == 2:
{self._indent_code(false_block)}
"""
    
    def obfuscate_loop(self, loop_code: str) -> str:
        """Obfuscate loops using complex control structures"""
        state_var = f"_loop_state_{self.state_counter}"
        counter_var = f"_loop_counter_{self.state_counter}"
        self.state_counter += 1
        
        return f"""
{state_var} = True
{counter_var} = 0
while {state_var}:
    try:
{self._indent_code(loop_code, 2)}
        {counter_var} += 1
        if {counter_var} > 1000:  # Safety break
            break
    except StopIteration:
        {state_var} = False
    except:
        continue
"""
    
    def _indent_code(self, code: str, levels: int = 1) -> str:
        """Indent code by specified levels"""
        indent = "    " * levels
        lines = code.split('\n')
        return '\n'.join(indent + line if line.strip() else line for line in lines)


class AntiAnalysisObfuscator:
    """Anti-analysis obfuscation techniques"""
    
    def __init__(self):
        """Initialize anti-analysis obfuscator"""
        self.dummy_functions = []
        self.fake_imports = []
    
    def add_dummy_functions(self, count: int = 10) -> str:
        """Add dummy functions to confuse analysis"""
        dummy_code = []
        
        for i in range(count):
            func_name = f"_dummy_func_{random.randint(1000, 9999)}"
            
            # Generate random dummy function
            dummy_code.append(f"""
def {func_name}():
    import random
    import time
    data = [random.randint(1, 100) for _ in range(50)]
    result = sum(x * x for x in data)
    time.sleep(random.uniform(0.001, 0.01))
    return result % 1000
""")
            
            self.dummy_functions.append(func_name)
        
        return '\n'.join(dummy_code)
    
    def add_fake_imports(self) -> str:
        """Add fake imports to confuse analysis"""
        fake_modules = [
            'fake_crypto', 'dummy_network', 'decoy_auth', 'phantom_db',
            'ghost_api', 'shadow_utils', 'mirror_security', 'echo_validation'
        ]
        
        import_code = []
        for module in fake_modules:
            import_code.append(f"""
try:
    import {module}
except ImportError:
    class {module}:
        def __getattr__(self, name):
            return lambda *args, **kwargs: None
    {module} = {module}()
""")
        
        return '\n'.join(import_code)
    
    def add_anti_debugging_checks(self) -> str:
        """Add anti-debugging checks throughout code"""
        return """
import sys
import time

def _check_debug():
    if hasattr(sys, 'gettrace') and sys.gettrace() is not None:
        sys.exit(1)
    
    start = time.perf_counter()
    time.sleep(0.001)
    end = time.perf_counter()
    if (end - start) > 0.01:
        sys.exit(1)

# Call debug check randomly
import random
if random.randint(1, 100) < 10:
    _check_debug()
"""


class BytecodeObfuscator:
    """Bytecode-level obfuscation"""
    
    def __init__(self):
        """Initialize bytecode obfuscator"""
        pass
    
    def compile_and_obfuscate(self, source_code: str) -> bytes:
        """Compile and obfuscate Python bytecode"""
        try:
            # Compile to bytecode
            compiled = compile(source_code, '<obfuscated>', 'exec')
            
            # Serialize bytecode
            bytecode = marshal.dumps(compiled)
            
            # Obfuscate bytecode
            obfuscated = self._obfuscate_bytecode(bytecode)
            
            return obfuscated
            
        except Exception as e:
            logger.error(f"Bytecode obfuscation error: {e}")
            return b''
    
    def _obfuscate_bytecode(self, bytecode: bytes) -> bytes:
        """Obfuscate bytecode using various techniques"""
        # XOR with random key
        key = os.urandom(16)
        
        obfuscated = bytearray()
        for i, byte in enumerate(bytecode):
            obfuscated.append(byte ^ key[i % len(key)])
        
        # Prepend key length and key
        result = len(key).to_bytes(4, 'little') + key + bytes(obfuscated)
        
        return result
    
    def generate_bytecode_loader(self) -> str:
        """Generate loader for obfuscated bytecode"""
        return """
import marshal

def _load_obfuscated_bytecode(data):
    try:
        # Extract key
        key_len = int.from_bytes(data[:4], 'little')
        key = data[4:4+key_len]
        obfuscated = data[4+key_len:]
        
        # Deobfuscate
        bytecode = bytearray()
        for i, byte in enumerate(obfuscated):
            bytecode.append(byte ^ key[i % len(key)])
        
        # Load and execute
        code_obj = marshal.loads(bytes(bytecode))
        return code_obj
    except:
        return None
"""


class MasterObfuscator:
    """Master obfuscation system combining all techniques"""
    
    def __init__(self):
        """Initialize master obfuscator"""
        self.string_obfuscator = CodeObfuscator()
        self.control_flow_obfuscator = ControlFlowObfuscator()
        self.anti_analysis_obfuscator = AntiAnalysisObfuscator()
        self.bytecode_obfuscator = BytecodeObfuscator()
    
    def obfuscate_file(self, input_file: str, output_file: str):
        """Obfuscate entire Python file"""
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            # Apply obfuscation layers
            obfuscated_code = self._apply_obfuscation(source_code)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(obfuscated_code)
            
            logger.info(f"Obfuscated {input_file} -> {output_file}")
            
        except Exception as e:
            logger.error(f"File obfuscation error: {e}")
    
    def _apply_obfuscation(self, source_code: str) -> str:
        """Apply all obfuscation techniques"""
        # Start with original code
        obfuscated = source_code
        
        # Add decoder function
        decoder = self.string_obfuscator.generate_decoder()
        
        # Add dummy functions
        dummy_funcs = self.anti_analysis_obfuscator.add_dummy_functions()
        
        # Add fake imports
        fake_imports = self.anti_analysis_obfuscator.add_fake_imports()
        
        # Add anti-debugging checks
        anti_debug = self.anti_analysis_obfuscator.add_anti_debugging_checks()
        
        # Combine all
        final_code = f"""
# Obfuscated Mobile Automation App
# Anti-reverse engineering protection active

{fake_imports}

{decoder}

{anti_debug}

{dummy_funcs}

# Original code (obfuscated)
{obfuscated}
"""
        
        return final_code
    
    def create_obfuscated_executable(self, source_files: List[str], output_dir: str):
        """Create obfuscated executable package"""
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            # Obfuscate each source file
            for source_file in source_files:
                filename = os.path.basename(source_file)
                output_file = os.path.join(output_dir, filename)
                self.obfuscate_file(source_file, output_file)
            
            # Create PyInstaller spec with obfuscation
            self._create_pyinstaller_spec(output_dir)
            
            logger.info(f"Created obfuscated package in {output_dir}")
            
        except Exception as e:
            logger.error(f"Executable creation error: {e}")
    
    def _create_pyinstaller_spec(self, output_dir: str):
        """Create PyInstaller spec file with security options"""
        spec_content = """
# -*- mode: python ; coding: utf-8 -*-

block_cipher = pyi_crypto.PyiBlockCipher(key='your-secret-key-here')

a = Analysis(
    ['auth_gateway.py'],
    pathex=[],
    binaries=[],
    datas=[('templates', 'templates'), ('static', 'static')],
    hiddenimports=[
        'waitress', 'flask', 'sqlite3', 'bcrypt', 'pyotp', 'qrcode',
        'PIL', 'cryptography', 'jwt', 'psutil'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='MobileAutomationApp',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='app_icon.ico'
)
"""
        
        spec_file = os.path.join(output_dir, 'app.spec')
        with open(spec_file, 'w') as f:
            f.write(spec_content)


# Global obfuscator instance
_obfuscator = None


def get_obfuscator() -> MasterObfuscator:
    """Get global obfuscator instance"""
    global _obfuscator
    if _obfuscator is None:
        _obfuscator = MasterObfuscator()
    return _obfuscator
