#!/usr/bin/env python3
"""
Mobile Automation App - Standalone Secure Application
Main entry point for the secure mobile automation platform.
"""

import os
import sys
import logging
import argparse
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Import security modules first
from security import init_protection, check_protection, disable_protection
from auth_gateway import AuthenticationGateway

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('mobile_automation.log')
    ]
)

logger = logging.getLogger(__name__)


class MobileAutomationApp:
    """Main application class"""
    
    def __init__(self, development_mode: bool = False):
        """Initialize the application"""
        self.development_mode = development_mode
        self.gateway = None
        
        # Initialize security protection
        if not development_mode:
            logger.info("Initializing security protection...")
            self.protection = init_protection()
            
            if not check_protection():
                logger.critical("Security validation failed - application cannot start")
                sys.exit(1)
            
            logger.info("Security protection active")
        else:
            logger.warning("Development mode - security protection disabled")
            disable_protection()
    
    def initialize(self):
        """Initialize application components"""
        try:
            logger.info("Initializing Mobile Automation App...")
            
            # Create application data directory
            app_data_dir = self._get_app_data_dir()
            os.makedirs(app_data_dir, exist_ok=True)
            
            # Initialize authentication gateway
            self.gateway = AuthenticationGateway(app_data_dir)
            
            # Create default admin user if none exists
            self._create_default_user()
            
            logger.info("Application initialized successfully")
            
        except Exception as e:
            logger.error(f"Application initialization failed: {e}")
            raise
    
    def _get_app_data_dir(self) -> str:
        """Get application data directory"""
        if sys.platform == "win32":
            app_data = os.environ.get('APPDATA', os.path.expanduser('~'))
            data_dir = os.path.join(app_data, 'MobileAutomationApp')
        elif sys.platform == "darwin":
            data_dir = os.path.expanduser('~/Library/Application Support/MobileAutomationApp')
        else:  # Linux
            data_dir = os.path.expanduser('~/.mobile_automation_app')
        
        return data_dir
    
    def _create_default_user(self):
        """Create default admin user if database is empty"""
        try:
            # Check if any users exist
            import sqlite3
            db_path = os.path.join(self.gateway.app_data_dir, "auth.db")
            
            with sqlite3.connect(db_path) as conn:
                cursor = conn.execute("SELECT COUNT(*) FROM users")
                user_count = cursor.fetchone()[0]
            
            if user_count == 0:
                # Create default admin user
                default_email = "<EMAIL>"
                default_password = "SecureAdmin123!"
                
                success, result = self.gateway.auth_manager.create_user(
                    email=default_email,
                    password=default_password,
                    first_name="Admin",
                    last_name="User"
                )
                
                if success:
                    logger.info(f"Default admin user created: {default_email}")
                    logger.info(f"Default password: {default_password}")
                    logger.warning("Please change the default password after first login!")
                else:
                    logger.error(f"Failed to create default user: {result}")
                    
        except Exception as e:
            logger.error(f"Error creating default user: {e}")
    
    def run(self, host: str = '127.0.0.1', port: int = 3000, debug: bool = False):
        """Run the application"""
        try:
            if not self.gateway:
                raise RuntimeError("Application not initialized")
            
            logger.info(f"Starting Mobile Automation App on {host}:{port}")
            
            if self.development_mode:
                logger.info("Running in development mode")
            
            # Print startup information
            self._print_startup_info(host, port)
            
            # Run the gateway
            self.gateway.run(host=host, port=port, debug=debug)
            
        except KeyboardInterrupt:
            logger.info("Application stopped by user")
        except Exception as e:
            logger.error(f"Application error: {e}")
            raise
        finally:
            self.shutdown()
    
    def _print_startup_info(self, host: str, port: int):
        """Print startup information"""
        print("\n" + "="*60)
        print("🔐 MOBILE AUTOMATION APP - SECURE EDITION")
        print("="*60)
        print(f"🌐 Web Interface: http://{host}:{port}")
        print(f"📱 iOS Dashboard: http://{host}:8080 (after authentication)")
        print(f"🤖 Android Dashboard: http://{host}:8081 (after authentication)")
        print("="*60)
        
        if self.development_mode:
            print("⚠️  DEVELOPMENT MODE - Security features disabled")
        else:
            print("🛡️  PRODUCTION MODE - Full security protection active")
        
        print("="*60)
        print("📋 Default Admin Credentials (change after first login):")
        print("   Email: <EMAIL>")
        print("   Password: SecureAdmin123!")
        print("="*60)
        print("🚀 Application ready! Open your browser to get started.")
        print("="*60 + "\n")
    
    def shutdown(self):
        """Shutdown the application"""
        try:
            logger.info("Shutting down application...")
            
            if self.gateway:
                self.gateway.shutdown()
            
            logger.info("Application shutdown complete")
            
        except Exception as e:
            logger.error(f"Shutdown error: {e}")


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Mobile Automation App - Secure iOS & Android Testing Platform"
    )
    
    parser.add_argument(
        '--host',
        default='127.0.0.1',
        help='Host to bind to (default: 127.0.0.1)'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=3000,
        help='Port to bind to (default: 3000)'
    )
    
    parser.add_argument(
        '--dev',
        action='store_true',
        help='Run in development mode (disables security features)'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug mode'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='Mobile Automation App v1.0.0'
    )
    
    args = parser.parse_args()
    
    try:
        # Create and initialize application
        app = MobileAutomationApp(development_mode=args.dev)
        app.initialize()
        
        # Run application
        app.run(
            host=args.host,
            port=args.port,
            debug=args.debug
        )
        
    except Exception as e:
        logger.error(f"Application failed to start: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
