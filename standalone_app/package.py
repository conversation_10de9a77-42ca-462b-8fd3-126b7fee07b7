#!/usr/bin/env python3
"""
Cross-Platform Packaging Script for Mobile Automation App
Creates distributable packages for Windows, macOS, and Linux.
"""

import os
import sys
import shutil
import zipfile
import tarfile
import platform
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CrossPlatformPackager:
    """Creates cross-platform distribution packages"""
    
    def __init__(self):
        """Initialize packager"""
        self.project_root = Path(__file__).parent
        self.dist_dir = self.project_root / "dist"
        self.packages_dir = self.project_root / "packages"
        
        # Ensure directories exist
        self.packages_dir.mkdir(exist_ok=True)
    
    def create_installer_script(self, platform_name: str) -> str:
        """Create platform-specific installer script"""
        
        if platform_name == "windows":
            return self._create_windows_installer()
        elif platform_name == "macos":
            return self._create_macos_installer()
        elif platform_name == "linux":
            return self._create_linux_installer()
        else:
            raise ValueError(f"Unsupported platform: {platform_name}")
    
    def _create_windows_installer(self) -> str:
        """Create Windows installer script"""
        installer_content = '''@echo off
REM Mobile Automation App Installer for Windows
echo.
echo ========================================
echo Mobile Automation App - Secure Edition
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with administrator privileges...
) else (
    echo Warning: Not running as administrator
    echo Some features may not work properly
    echo.
)

REM Create application directory
set "INSTALL_DIR=%PROGRAMFILES%\\MobileAutomationApp"
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%"
    echo Created installation directory: %INSTALL_DIR%
)

REM Copy application files
echo Copying application files...
copy "MobileAutomationApp.exe" "%INSTALL_DIR%\\" >nul
copy "README.md" "%INSTALL_DIR%\\" >nul

REM Create desktop shortcut
set "DESKTOP=%USERPROFILE%\\Desktop"
echo Creating desktop shortcut...
echo [InternetShortcut] > "%DESKTOP%\\Mobile Automation App.url"
echo URL=file:///%INSTALL_DIR%\\MobileAutomationApp.exe >> "%DESKTOP%\\Mobile Automation App.url"
echo IconFile=%INSTALL_DIR%\\MobileAutomationApp.exe >> "%DESKTOP%\\Mobile Automation App.url"
echo IconIndex=0 >> "%DESKTOP%\\Mobile Automation App.url"

REM Create start menu entry
set "STARTMENU=%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs"
if not exist "%STARTMENU%\\Mobile Automation" mkdir "%STARTMENU%\\Mobile Automation"
echo [InternetShortcut] > "%STARTMENU%\\Mobile Automation\\Mobile Automation App.url"
echo URL=file:///%INSTALL_DIR%\\MobileAutomationApp.exe >> "%STARTMENU%\\Mobile Automation\\Mobile Automation App.url"
echo IconFile=%INSTALL_DIR%\\MobileAutomationApp.exe >> "%STARTMENU%\\Mobile Automation\\Mobile Automation App.url"
echo IconIndex=0 >> "%STARTMENU%\\Mobile Automation\\Mobile Automation App.url"

REM Configure Windows Firewall (optional)
echo.
echo Configuring Windows Firewall...
netsh advfirewall firewall add rule name="Mobile Automation App" dir=in action=allow program="%INSTALL_DIR%\\MobileAutomationApp.exe" enable=yes >nul 2>&1

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo Application installed to: %INSTALL_DIR%
echo Desktop shortcut created
echo Start menu entry created
echo.
echo To start the application:
echo 1. Double-click the desktop shortcut, or
echo 2. Run: "%INSTALL_DIR%\\MobileAutomationApp.exe"
echo 3. Open browser to: http://127.0.0.1:3000
echo.
echo Default login credentials:
echo Email: <EMAIL>
echo Password: SecureAdmin123!
echo.
echo IMPORTANT: Change the default password after first login!
echo.
pause
'''
        return installer_content
    
    def _create_macos_installer(self) -> str:
        """Create macOS installer script"""
        installer_content = '''#!/bin/bash
# Mobile Automation App Installer for macOS

echo ""
echo "========================================"
echo "Mobile Automation App - Secure Edition"
echo "========================================"
echo ""

# Check if running with sudo
if [ "$EUID" -eq 0 ]; then
    echo "Running with administrator privileges..."
    INSTALL_DIR="/Applications/MobileAutomationApp"
    USER_HOME=$(eval echo ~$SUDO_USER)
else
    echo "Installing for current user..."
    INSTALL_DIR="$HOME/Applications/MobileAutomationApp"
    USER_HOME="$HOME"
fi

# Create application directory
echo "Creating installation directory..."
mkdir -p "$INSTALL_DIR"

# Copy application files
echo "Copying application files..."
cp "MobileAutomationApp" "$INSTALL_DIR/"
cp "README.md" "$INSTALL_DIR/"
chmod +x "$INSTALL_DIR/MobileAutomationApp"

# Create launch script
echo "Creating launch script..."
cat > "$INSTALL_DIR/launch.sh" << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
./MobileAutomationApp
EOF
chmod +x "$INSTALL_DIR/launch.sh"

# Create desktop shortcut
echo "Creating desktop shortcut..."
cat > "$USER_HOME/Desktop/Mobile Automation App.command" << EOF
#!/bin/bash
cd "$INSTALL_DIR"
./MobileAutomationApp
EOF
chmod +x "$USER_HOME/Desktop/Mobile Automation App.command"

# Create Applications folder shortcut
if [ -d "/Applications" ]; then
    ln -sf "$INSTALL_DIR" "/Applications/Mobile Automation App" 2>/dev/null || true
fi

# Configure firewall (optional)
echo ""
echo "Configuring firewall..."
sudo /usr/libexec/ApplicationFirewall/socketfilterfw --add "$INSTALL_DIR/MobileAutomationApp" 2>/dev/null || true
sudo /usr/libexec/ApplicationFirewall/socketfilterfw --unblockapp "$INSTALL_DIR/MobileAutomationApp" 2>/dev/null || true

echo ""
echo "========================================"
echo "Installation completed successfully!"
echo "========================================"
echo ""
echo "Application installed to: $INSTALL_DIR"
echo "Desktop shortcut created"
echo ""
echo "To start the application:"
echo "1. Double-click the desktop shortcut, or"
echo "2. Run: $INSTALL_DIR/MobileAutomationApp"
echo "3. Open browser to: http://127.0.0.1:3000"
echo ""
echo "Default login credentials:"
echo "Email: <EMAIL>"
echo "Password: SecureAdmin123!"
echo ""
echo "IMPORTANT: Change the default password after first login!"
echo ""
'''
        return installer_content
    
    def _create_linux_installer(self) -> str:
        """Create Linux installer script"""
        installer_content = '''#!/bin/bash
# Mobile Automation App Installer for Linux

echo ""
echo "========================================"
echo "Mobile Automation App - Secure Edition"
echo "========================================"
echo ""

# Check if running with sudo
if [ "$EUID" -eq 0 ]; then
    echo "Running with administrator privileges..."
    INSTALL_DIR="/opt/MobileAutomationApp"
    USER_HOME=$(eval echo ~$SUDO_USER)
    DESKTOP_DIR="$USER_HOME/Desktop"
    APPLICATIONS_DIR="/usr/share/applications"
else
    echo "Installing for current user..."
    INSTALL_DIR="$HOME/.local/share/MobileAutomationApp"
    USER_HOME="$HOME"
    DESKTOP_DIR="$HOME/Desktop"
    APPLICATIONS_DIR="$HOME/.local/share/applications"
fi

# Create application directory
echo "Creating installation directory..."
mkdir -p "$INSTALL_DIR"
mkdir -p "$APPLICATIONS_DIR"

# Copy application files
echo "Copying application files..."
cp "MobileAutomationApp" "$INSTALL_DIR/"
cp "README.md" "$INSTALL_DIR/"
chmod +x "$INSTALL_DIR/MobileAutomationApp"

# Create desktop entry
echo "Creating desktop entry..."
cat > "$APPLICATIONS_DIR/mobile-automation-app.desktop" << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=Mobile Automation App
Comment=Secure iOS & Android Testing Platform
Exec=$INSTALL_DIR/MobileAutomationApp
Icon=application-x-executable
Terminal=false
Categories=Development;Testing;
StartupNotify=true
EOF

# Create desktop shortcut
if [ -d "$DESKTOP_DIR" ]; then
    echo "Creating desktop shortcut..."
    cp "$APPLICATIONS_DIR/mobile-automation-app.desktop" "$DESKTOP_DIR/"
    chmod +x "$DESKTOP_DIR/mobile-automation-app.desktop"
fi

# Add to PATH (optional)
if [ "$EUID" -eq 0 ]; then
    echo "Adding to system PATH..."
    ln -sf "$INSTALL_DIR/MobileAutomationApp" "/usr/local/bin/mobile-automation-app" 2>/dev/null || true
else
    echo "Adding to user PATH..."
    mkdir -p "$HOME/.local/bin"
    ln -sf "$INSTALL_DIR/MobileAutomationApp" "$HOME/.local/bin/mobile-automation-app" 2>/dev/null || true
fi

# Configure firewall (if ufw is available)
if command -v ufw >/dev/null 2>&1; then
    echo "Configuring firewall..."
    sudo ufw allow 3000/tcp comment "Mobile Automation App" 2>/dev/null || true
    sudo ufw allow 8080/tcp comment "Mobile Automation iOS" 2>/dev/null || true
    sudo ufw allow 8081/tcp comment "Mobile Automation Android" 2>/dev/null || true
fi

echo ""
echo "========================================"
echo "Installation completed successfully!"
echo "========================================"
echo ""
echo "Application installed to: $INSTALL_DIR"
echo "Desktop entry created"
echo ""
echo "To start the application:"
echo "1. Click the desktop icon, or"
echo "2. Run: $INSTALL_DIR/MobileAutomationApp"
echo "3. Or run: mobile-automation-app (if in PATH)"
echo "4. Open browser to: http://127.0.0.1:3000"
echo ""
echo "Default login credentials:"
echo "Email: <EMAIL>"
echo "Password: SecureAdmin123!"
echo ""
echo "IMPORTANT: Change the default password after first login!"
echo ""
'''
        return installer_content
    
    def create_package(self, dist_package_path: Path) -> Path:
        """Create final distribution package"""
        logger.info(f"Creating package from: {dist_package_path}")
        
        if not dist_package_path.exists():
            raise FileNotFoundError(f"Distribution package not found: {dist_package_path}")
        
        # Determine platform and create appropriate package
        platform_name = platform.system().lower()
        arch = platform.machine().lower()
        
        if platform_name == "windows":
            return self._create_windows_package(dist_package_path, arch)
        elif platform_name == "darwin":
            return self._create_macos_package(dist_package_path, arch)
        elif platform_name == "linux":
            return self._create_linux_package(dist_package_path, arch)
        else:
            raise ValueError(f"Unsupported platform: {platform_name}")
    
    def _create_windows_package(self, dist_path: Path, arch: str) -> Path:
        """Create Windows ZIP package"""
        package_name = f"MobileAutomationApp-windows-{arch}.zip"
        package_path = self.packages_dir / package_name
        
        logger.info(f"Creating Windows package: {package_name}")
        
        with zipfile.ZipFile(package_path, 'w', zipfile.ZIP_DEFLATED) as zf:
            # Add all files from distribution
            for file_path in dist_path.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(dist_path)
                    zf.write(file_path, arcname)
            
            # Add installer script
            installer_script = self.create_installer_script("windows")
            zf.writestr("install.bat", installer_script)
        
        return package_path
    
    def _create_macos_package(self, dist_path: Path, arch: str) -> Path:
        """Create macOS tar.gz package"""
        package_name = f"MobileAutomationApp-macos-{arch}.tar.gz"
        package_path = self.packages_dir / package_name
        
        logger.info(f"Creating macOS package: {package_name}")
        
        with tarfile.open(package_path, 'w:gz') as tf:
            # Add all files from distribution
            for file_path in dist_path.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(dist_path)
                    tf.add(file_path, arcname)
            
            # Add installer script
            installer_script = self.create_installer_script("macos")
            installer_info = tarfile.TarInfo(name="install.sh")
            installer_info.size = len(installer_script.encode())
            installer_info.mode = 0o755
            tf.addfile(installer_info, fileobj=tarfile.io.BytesIO(installer_script.encode()))
        
        return package_path
    
    def _create_linux_package(self, dist_path: Path, arch: str) -> Path:
        """Create Linux tar.gz package"""
        package_name = f"MobileAutomationApp-linux-{arch}.tar.gz"
        package_path = self.packages_dir / package_name
        
        logger.info(f"Creating Linux package: {package_name}")
        
        with tarfile.open(package_path, 'w:gz') as tf:
            # Add all files from distribution
            for file_path in dist_path.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(dist_path)
                    tf.add(file_path, arcname)
            
            # Add installer script
            installer_script = self.create_installer_script("linux")
            installer_info = tarfile.TarInfo(name="install.sh")
            installer_info.size = len(installer_script.encode())
            installer_info.mode = 0o755
            tf.addfile(installer_info, fileobj=tarfile.io.BytesIO(installer_script.encode()))
        
        return package_path
    
    def create_all_packages(self):
        """Create packages for all available distributions"""
        logger.info("Creating packages for all available distributions...")
        
        packages_created = []
        
        # Find all distribution directories
        for dist_dir in self.dist_dir.iterdir():
            if dist_dir.is_dir() and dist_dir.name.startswith("MobileAutomationApp-"):
                try:
                    package_path = self.create_package(dist_dir)
                    packages_created.append(package_path)
                    logger.info(f"Package created: {package_path}")
                except Exception as e:
                    logger.error(f"Failed to create package for {dist_dir}: {e}")
        
        return packages_created


def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Package Mobile Automation App for distribution")
    parser.add_argument('--dist-path', type=Path, help='Path to distribution directory')
    parser.add_argument('--all', action='store_true', help='Package all available distributions')
    
    args = parser.parse_args()
    
    packager = CrossPlatformPackager()
    
    try:
        if args.all:
            packages = packager.create_all_packages()
            print(f"\n✅ Created {len(packages)} packages:")
            for package in packages:
                print(f"📦 {package}")
        elif args.dist_path:
            package = packager.create_package(args.dist_path)
            print(f"\n✅ Package created: {package}")
        else:
            print("Please specify --dist-path or --all")
            sys.exit(1)
        
        print(f"\n🚀 Packages ready for distribution!")
        
    except Exception as e:
        print(f"\n❌ Packaging failed: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
