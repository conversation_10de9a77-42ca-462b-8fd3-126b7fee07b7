"""
Authentication Gateway for Standalone Mobile Automation App
Provides unified login interface and launches iOS/Android dashboards after authentication.
"""

import os
import sys
import json
import threading
import webbrowser
import subprocess
from pathlib import Path
from typing import Dict, Optional
import logging

from flask import Flask, render_template, request, jsonify, redirect, url_for, session
from waitress import serve

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from auth.secure_auth import SecureAuthManager
from security import init_protection, check_protection, protected

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AuthenticationGateway:
    """Main authentication gateway application"""

    def __init__(self, app_data_dir: str = None):
        """Initialize authentication gateway"""
        # Initialize security protection first
        self.protection = init_protection()
        if not check_protection():
            logger.critical("Security check failed - application terminated")
            sys.exit(1)

        self.app_data_dir = app_data_dir or self._get_app_data_dir()
        self.auth_manager = SecureAuthManager(
            db_path=os.path.join(self.app_data_dir, "auth.db")
        )
        
        # Flask app setup
        self.app = Flask(__name__, 
                        template_folder='templates',
                        static_folder='static')
        self.app.secret_key = self.auth_manager.master_key
        
        # Ports for iOS and Android services
        self.ios_port = 8080
        self.android_port = 8081
        self.gateway_port = 3000
        
        # Service processes
        self.ios_process = None
        self.android_process = None
        
        self._setup_routes()
    
    def _get_app_data_dir(self) -> str:
        """Get application data directory"""
        if sys.platform == "win32":
            app_data = os.environ.get('APPDATA', os.path.expanduser('~'))
            data_dir = os.path.join(app_data, 'MobileAutomationApp')
        elif sys.platform == "darwin":
            data_dir = os.path.expanduser('~/Library/Application Support/MobileAutomationApp')
        else:  # Linux
            data_dir = os.path.expanduser('~/.mobile_automation_app')
        
        os.makedirs(data_dir, exist_ok=True)
        return data_dir
    
    def _setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/')
        def index():
            """Main login page"""
            if 'user_id' in session:
                return redirect(url_for('dashboard'))
            return render_template('login.html')
        
        @self.app.route('/login', methods=['POST'])
        @protected
        def login():
            """Handle login request"""
            try:
                data = request.get_json()
                email = data.get('email')
                password = data.get('password')
                totp_code = data.get('totp_code')
                
                if not email or not password:
                    return jsonify({'error': 'Email and password required'}), 400
                
                success, result = self.auth_manager.authenticate(email, password, totp_code)
                
                if success:
                    # Store session data
                    session['user_id'] = result['user']['id']
                    session['session_id'] = result['session']['session_id']
                    session['user_email'] = result['user']['email']
                    session['user_name'] = f"{result['user']['first_name']} {result['user']['last_name']}"
                    
                    return jsonify({
                        'success': True,
                        'user': result['user'],
                        'mfa_enabled': result['mfa_enabled'],
                        'trusted_device': result['trusted_device']
                    })
                else:
                    return jsonify(result), 401
                    
            except Exception as e:
                logger.error(f"Login error: {e}")
                return jsonify({'error': 'Login failed'}), 500
        
        @self.app.route('/register', methods=['GET', 'POST'])
        def register():
            """User registration"""
            if request.method == 'GET':
                return render_template('register.html')
            
            try:
                data = request.get_json()
                email = data.get('email')
                password = data.get('password')
                first_name = data.get('first_name', '')
                last_name = data.get('last_name', '')
                
                if not email or not password:
                    return jsonify({'error': 'Email and password required'}), 400
                
                success, result = self.auth_manager.create_user(
                    email, password, first_name, last_name
                )
                
                if success:
                    return jsonify({'success': True, 'user_id': result})
                else:
                    return jsonify({'error': result}), 400
                    
            except Exception as e:
                logger.error(f"Registration error: {e}")
                return jsonify({'error': 'Registration failed'}), 500
        
        @self.app.route('/dashboard')
        @protected
        def dashboard():
            """Main dashboard after authentication"""
            if 'user_id' not in session:
                return redirect(url_for('index'))
            
            # Check if services are running
            ios_running = self._is_service_running(self.ios_port)
            android_running = self._is_service_running(self.android_port)
            
            return render_template('dashboard.html', 
                                 user_name=session.get('user_name', 'User'),
                                 ios_port=self.ios_port,
                                 android_port=self.android_port,
                                 ios_running=ios_running,
                                 android_running=android_running)
        
        @self.app.route('/setup-2fa', methods=['GET', 'POST'])
        def setup_2fa():
            """Setup two-factor authentication"""
            if 'user_id' not in session:
                return redirect(url_for('index'))
            
            if request.method == 'GET':
                try:
                    setup_data = self.auth_manager.setup_2fa(
                        session['user_id'], 
                        session['user_email']
                    )
                    return render_template('setup_2fa.html', 
                                         qr_code=setup_data['qr_code'],
                                         backup_codes=setup_data['backup_codes'])
                except Exception as e:
                    logger.error(f"2FA setup error: {e}")
                    return "Error setting up 2FA", 500
            
            # POST - verify setup
            try:
                data = request.get_json()
                token = data.get('token')
                
                if self.auth_manager.verify_2fa_setup(session['user_id'], token):
                    return jsonify({'success': True})
                else:
                    return jsonify({'error': 'Invalid verification code'}), 400
                    
            except Exception as e:
                logger.error(f"2FA verification error: {e}")
                return jsonify({'error': 'Verification failed'}), 500
        
        @self.app.route('/start-service/<service>')
        def start_service(service):
            """Start iOS or Android service"""
            if 'user_id' not in session:
                return jsonify({'error': 'Not authenticated'}), 401
            
            try:
                if service == 'ios':
                    success = self._start_ios_service()
                elif service == 'android':
                    success = self._start_android_service()
                else:
                    return jsonify({'error': 'Invalid service'}), 400
                
                if success:
                    return jsonify({'success': True})
                else:
                    return jsonify({'error': 'Failed to start service'}), 500
                    
            except Exception as e:
                logger.error(f"Error starting {service} service: {e}")
                return jsonify({'error': 'Service start failed'}), 500
        
        @self.app.route('/open-dashboard/<service>')
        def open_dashboard(service):
            """Open iOS or Android dashboard in browser"""
            if 'user_id' not in session:
                return jsonify({'error': 'Not authenticated'}), 401
            
            try:
                if service == 'ios':
                    url = f"http://localhost:{self.ios_port}"
                elif service == 'android':
                    url = f"http://localhost:{self.android_port}"
                else:
                    return jsonify({'error': 'Invalid service'}), 400
                
                # Open in new browser tab
                webbrowser.open_new_tab(url)
                return jsonify({'success': True, 'url': url})
                
            except Exception as e:
                logger.error(f"Error opening {service} dashboard: {e}")
                return jsonify({'error': 'Failed to open dashboard'}), 500
        
        @self.app.route('/logout')
        def logout():
            """Logout user"""
            if 'session_id' in session:
                self.auth_manager.logout(session['session_id'])
            
            session.clear()
            return redirect(url_for('index'))
        
        @self.app.route('/status')
        def status():
            """Get service status"""
            return jsonify({
                'ios_running': self._is_service_running(self.ios_port),
                'android_running': self._is_service_running(self.android_port),
                'authenticated': 'user_id' in session
            })
    
    def _is_service_running(self, port: int) -> bool:
        """Check if service is running on port"""
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            return result == 0
        except Exception:
            return False
    
    def _start_ios_service(self) -> bool:
        """Start iOS automation service"""
        try:
            if self.ios_process and self.ios_process.poll() is None:
                return True  # Already running
            
            # Get path to run.py
            run_py_path = self._get_run_py_path()
            if not run_py_path:
                logger.error("Could not find run.py")
                return False
            
            # Start iOS service
            self.ios_process = subprocess.Popen([
                sys.executable, run_py_path,
                '--port', str(self.ios_port)
            ], cwd=os.path.dirname(run_py_path))
            
            # Wait a moment for service to start
            import time
            time.sleep(3)
            
            return self._is_service_running(self.ios_port)
            
        except Exception as e:
            logger.error(f"Error starting iOS service: {e}")
            return False
    
    def _start_android_service(self) -> bool:
        """Start Android automation service"""
        try:
            if self.android_process and self.android_process.poll() is None:
                return True  # Already running
            
            # Get path to run_android.py
            run_android_py_path = self._get_run_android_py_path()
            if not run_android_py_path:
                logger.error("Could not find run_android.py")
                return False
            
            # Start Android service
            self.android_process = subprocess.Popen([
                sys.executable, run_android_py_path,
                '--port', str(self.android_port)
            ], cwd=os.path.dirname(run_android_py_path))
            
            # Wait a moment for service to start
            import time
            time.sleep(3)
            
            return self._is_service_running(self.android_port)
            
        except Exception as e:
            logger.error(f"Error starting Android service: {e}")
            return False
    
    def _get_run_py_path(self) -> Optional[str]:
        """Get path to run.py"""
        # Look for run.py in various locations
        possible_paths = [
            os.path.join(os.path.dirname(__file__), '..', 'run.py'),
            os.path.join(os.path.dirname(__file__), '..', '..', 'run.py'),
            'run.py'
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return os.path.abspath(path)
        
        return None
    
    def _get_run_android_py_path(self) -> Optional[str]:
        """Get path to run_android.py"""
        # Look for run_android.py in various locations
        possible_paths = [
            os.path.join(os.path.dirname(__file__), '..', 'run_android.py'),
            os.path.join(os.path.dirname(__file__), '..', '..', 'run_android.py'),
            'run_android.py'
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return os.path.abspath(path)
        
        return None
    
    def run(self, host='127.0.0.1', port=None, debug=False):
        """Run the authentication gateway"""
        port = port or self.gateway_port
        
        logger.info(f"Starting Mobile Automation Authentication Gateway on {host}:{port}")
        logger.info(f"Open your browser to: http://{host}:{port}")
        
        if debug:
            self.app.run(host=host, port=port, debug=True)
        else:
            serve(self.app, host=host, port=port)
    
    def shutdown(self):
        """Shutdown services"""
        if self.ios_process:
            self.ios_process.terminate()
        if self.android_process:
            self.android_process.terminate()


def main():
    """Main entry point"""
    gateway = AuthenticationGateway()
    
    try:
        # Auto-open browser
        import threading
        import time
        
        def open_browser():
            time.sleep(2)
            webbrowser.open(f'http://127.0.0.1:{gateway.gateway_port}')
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # Run gateway
        gateway.run()
        
    except KeyboardInterrupt:
        logger.info("Shutting down...")
        gateway.shutdown()
    except Exception as e:
        logger.error(f"Gateway error: {e}")
        gateway.shutdown()


if __name__ == '__main__':
    main()
