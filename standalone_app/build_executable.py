#!/usr/bin/env python3
"""
Build Script for Mobile Automation App Executable
Creates secure, obfuscated executables for Windows, macOS, and Linux.
"""

import os
import sys
import shutil
import subprocess
import platform
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import obfuscation tools
from security.obfuscation import get_obfuscator


class ExecutableBuilder:
    """Builds secure executables for the mobile automation app"""
    
    def __init__(self):
        """Initialize builder"""
        self.project_root = Path(__file__).parent
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"
        self.obfuscated_dir = self.project_root / "obfuscated"
        
        # Ensure directories exist
        self.build_dir.mkdir(exist_ok=True)
        self.dist_dir.mkdir(exist_ok=True)
        self.obfuscated_dir.mkdir(exist_ok=True)
        
        self.obfuscator = get_obfuscator()
    
    def clean_build(self):
        """Clean previous build artifacts"""
        logger.info("Cleaning previous build artifacts...")
        
        dirs_to_clean = [self.build_dir, self.dist_dir, self.obfuscated_dir]
        
        for dir_path in dirs_to_clean:
            if dir_path.exists():
                shutil.rmtree(dir_path)
                dir_path.mkdir(exist_ok=True)
    
    def obfuscate_source_code(self):
        """Obfuscate source code before packaging"""
        logger.info("Obfuscating source code...")
        
        # Files to obfuscate
        source_files = [
            "main.py",
            "auth_gateway.py",
            "auth/secure_auth.py",
            "security/anti_reverse.py",
            "security/obfuscation.py"
        ]
        
        # Copy and obfuscate files
        for source_file in source_files:
            source_path = self.project_root / source_file
            if source_path.exists():
                # Create directory structure in obfuscated folder
                relative_path = Path(source_file)
                obfuscated_path = self.obfuscated_dir / relative_path
                obfuscated_path.parent.mkdir(parents=True, exist_ok=True)
                
                # Obfuscate the file
                self.obfuscator.obfuscate_file(str(source_path), str(obfuscated_path))
            else:
                logger.warning(f"Source file not found: {source_file}")
        
        # Copy non-Python files as-is
        self._copy_resources()
    
    def _copy_resources(self):
        """Copy templates, static files, and other resources"""
        logger.info("Copying resources...")
        
        # Copy templates
        templates_src = self.project_root / "templates"
        templates_dst = self.obfuscated_dir / "templates"
        if templates_src.exists():
            shutil.copytree(templates_src, templates_dst, dirs_exist_ok=True)
        
        # Copy static files if they exist
        static_src = self.project_root / "static"
        static_dst = self.obfuscated_dir / "static"
        if static_src.exists():
            shutil.copytree(static_src, static_dst, dirs_exist_ok=True)
        
        # Copy requirements.txt
        requirements_src = self.project_root / "requirements.txt"
        requirements_dst = self.obfuscated_dir / "requirements.txt"
        if requirements_src.exists():
            shutil.copy2(requirements_src, requirements_dst)
        
        # Copy __init__.py files
        init_files = [
            "auth/__init__.py",
            "security/__init__.py"
        ]
        
        for init_file in init_files:
            src_path = self.project_root / init_file
            dst_path = self.obfuscated_dir / init_file
            if src_path.exists():
                dst_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(src_path, dst_path)
    
    def create_pyinstaller_spec(self):
        """Create PyInstaller spec file with security options"""
        logger.info("Creating PyInstaller spec file...")
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# Security: Use block cipher for encryption
block_cipher = None  # Can be enabled with: pyi_crypto.PyiBlockCipher(key='your-secret-key')

# Collect data files
datas = []
datas += collect_data_files('templates')
datas += [('templates', 'templates')]

# Hidden imports for security and functionality
hiddenimports = [
    'waitress',
    'flask',
    'sqlite3',
    'bcrypt',
    'pyotp',
    'qrcode',
    'PIL',
    'cryptography',
    'jwt',
    'psutil',
    'platform',
    'threading',
    'subprocess',
    'webbrowser',
    'hashlib',
    'secrets',
    'base64',
    'zlib',
    'marshal',
    'random',
    'string',
    'time',
    'json',
    'logging'
]

a = Analysis(
    ['main.py'],
    pathex=['{self.obfuscated_dir}'],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'IPython',
        'jupyter'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# Remove duplicate entries
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='MobileAutomationApp',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # Enable UPX compression for obfuscation
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Hide console window
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None  # Add icon file path here if available
)
'''
        
        spec_file = self.obfuscated_dir / "app.spec"
        with open(spec_file, 'w') as f:
            f.write(spec_content)
        
        return spec_file
    
    def build_executable(self):
        """Build the executable using PyInstaller"""
        logger.info("Building executable with PyInstaller...")
        
        # Create spec file
        spec_file = self.create_pyinstaller_spec()
        
        # Change to obfuscated directory
        original_cwd = os.getcwd()
        os.chdir(self.obfuscated_dir)
        
        try:
            # Run PyInstaller
            cmd = [
                sys.executable, '-m', 'PyInstaller',
                '--clean',
                '--noconfirm',
                str(spec_file.name)
            ]
            
            logger.info(f"Running: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"PyInstaller failed: {result.stderr}")
                raise RuntimeError("Executable build failed")
            
            logger.info("Executable built successfully")
            
        finally:
            os.chdir(original_cwd)
    
    def apply_additional_protection(self):
        """Apply additional protection to the executable"""
        logger.info("Applying additional protection...")
        
        # Find the executable
        exe_name = "MobileAutomationApp"
        if platform.system() == "Windows":
            exe_name += ".exe"
        
        exe_path = self.obfuscated_dir / "dist" / exe_name
        
        if not exe_path.exists():
            logger.error(f"Executable not found: {exe_path}")
            return
        
        # Apply UPX compression if available
        try:
            upx_cmd = ["upx", "--best", "--lzma", str(exe_path)]
            result = subprocess.run(upx_cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("UPX compression applied successfully")
            else:
                logger.warning("UPX compression failed or not available")
                
        except FileNotFoundError:
            logger.warning("UPX not found - skipping compression")
    
    def create_distribution_package(self):
        """Create final distribution package"""
        logger.info("Creating distribution package...")
        
        # Create distribution directory
        platform_name = platform.system().lower()
        arch = platform.machine().lower()
        dist_name = f"MobileAutomationApp-{platform_name}-{arch}"
        
        final_dist_dir = self.dist_dir / dist_name
        final_dist_dir.mkdir(exist_ok=True)
        
        # Copy executable
        exe_name = "MobileAutomationApp"
        if platform.system() == "Windows":
            exe_name += ".exe"
        
        exe_src = self.obfuscated_dir / "dist" / exe_name
        exe_dst = final_dist_dir / exe_name
        
        if exe_src.exists():
            shutil.copy2(exe_src, exe_dst)
            
            # Make executable on Unix systems
            if platform.system() != "Windows":
                os.chmod(exe_dst, 0o755)
        
        # Create README
        readme_content = f"""# Mobile Automation App - Secure Edition

## Overview
This is a secure, standalone mobile automation platform for iOS and Android testing.

## Features
- 🔐 Enterprise-grade security with 2FA
- 📱 iOS automation dashboard
- 🤖 Android automation dashboard
- 🛡️ Anti-reverse engineering protection
- 🌐 Web-based interface

## Quick Start
1. Run the executable: ./{exe_name}
2. Open your browser to: http://127.0.0.1:3000
3. Login with default credentials:
   - Email: <EMAIL>
   - Password: SecureAdmin123!
4. Change the default password immediately
5. Set up two-factor authentication for enhanced security

## System Requirements
- {platform.system()} {platform.release()}
- Available ports: 3000 (main), 8080 (iOS), 8081 (Android)
- Modern web browser

## Security Features
- Two-factor authentication (TOTP)
- Hardware device fingerprinting
- Session management with automatic timeout
- Rate limiting and brute force protection
- Anti-debugging and anti-reverse engineering
- Encrypted data storage

## Support
For support and documentation, please contact your system administrator.

## Version
Built on: {platform.node()}
Platform: {platform.platform()}
Architecture: {platform.machine()}
"""
        
        readme_file = final_dist_dir / "README.md"
        with open(readme_file, 'w') as f:
            f.write(readme_content)
        
        # Create startup script for Unix systems
        if platform.system() != "Windows":
            startup_script = final_dist_dir / "start.sh"
            with open(startup_script, 'w') as f:
                f.write(f"""#!/bin/bash
# Mobile Automation App Startup Script

echo "Starting Mobile Automation App..."
echo "Platform: {platform.platform()}"
echo ""

# Check if ports are available
if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null ; then
    echo "Warning: Port 3000 is already in use"
fi

# Start the application
./{exe_name} "$@"
""")
            os.chmod(startup_script, 0o755)
        
        logger.info(f"Distribution package created: {final_dist_dir}")
        return final_dist_dir
    
    def build(self):
        """Main build process"""
        logger.info("Starting build process...")
        
        try:
            # Clean previous builds
            self.clean_build()
            
            # Obfuscate source code
            self.obfuscate_source_code()
            
            # Build executable
            self.build_executable()
            
            # Apply additional protection
            self.apply_additional_protection()
            
            # Create distribution package
            dist_package = self.create_distribution_package()
            
            logger.info("Build completed successfully!")
            logger.info(f"Distribution package: {dist_package}")
            
            return dist_package
            
        except Exception as e:
            logger.error(f"Build failed: {e}")
            raise


def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Build Mobile Automation App executable")
    parser.add_argument('--clean', action='store_true', help='Clean build artifacts only')
    
    args = parser.parse_args()
    
    builder = ExecutableBuilder()
    
    if args.clean:
        builder.clean_build()
        logger.info("Build artifacts cleaned")
        return
    
    try:
        dist_package = builder.build()
        print(f"\n✅ Build successful!")
        print(f"📦 Distribution package: {dist_package}")
        print(f"🚀 Ready for distribution!")
        
    except Exception as e:
        print(f"\n❌ Build failed: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
