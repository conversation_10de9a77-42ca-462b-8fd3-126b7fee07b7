{% extends "base.html" %}

{% block title %}Login - Mobile Automation App{% endblock %}

{% block content %}
<div class="logo">
    <h1>🔐 Mobile Automation</h1>
    <p>Secure iOS & Android Testing Platform</p>
</div>

<form id="loginForm">
    <div class="form-group">
        <label for="email">Email Address</label>
        <input type="email" id="email" name="email" required>
    </div>
    
    <div class="form-group">
        <label for="password">Password</label>
        <input type="password" id="password" name="password" required>
    </div>
    
    <div id="twoFactorSection" class="two-factor-section" style="display: none;">
        <div class="form-group">
            <label for="totpCode">Two-Factor Authentication Code</label>
            <input type="text" id="totpCode" name="totpCode" placeholder="Enter 6-digit code" maxlength="6">
            <small style="color: #666; font-size: 0.8rem;">
                Enter the code from your authenticator app or use a backup code
            </small>
        </div>
    </div>
    
    <div class="loading">
        <div class="spinner"></div>
        <p>Authenticating...</p>
    </div>
    
    <button type="submit" class="btn" id="loginBtn">Sign In</button>
</form>

<div class="link">
    <a href="/register">Don't have an account? Register here</a>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('loginForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    clearAlerts();
    showLoading(true);
    disableForm(true);
    
    const formData = {
        email: document.getElementById('email').value,
        password: document.getElementById('password').value,
        totp_code: document.getElementById('totpCode').value || null
    };
    
    try {
        const response = await fetch('/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            if (data.success) {
                showAlert('Login successful! Redirecting...', 'success');
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 1000);
            }
        } else {
            if (data.requires_2fa) {
                // Show 2FA section
                document.getElementById('twoFactorSection').style.display = 'block';
                document.getElementById('totpCode').focus();
                showAlert('Please enter your two-factor authentication code', 'info');
            } else {
                showAlert(data.error || 'Login failed');
            }
        }
    } catch (error) {
        console.error('Login error:', error);
        showAlert('Network error. Please try again.');
    } finally {
        showLoading(false);
        disableForm(false);
    }
});

// Auto-submit when 6-digit code is entered
document.getElementById('totpCode').addEventListener('input', function(e) {
    if (e.target.value.length === 6) {
        // Auto-submit after a short delay
        setTimeout(() => {
            document.getElementById('loginForm').dispatchEvent(new Event('submit'));
        }, 500);
    }
});

// Focus email field on load
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('email').focus();
});
</script>
{% endblock %}
