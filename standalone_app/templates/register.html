{% extends "base.html" %}

{% block title %}Register - Mobile Automation App{% endblock %}

{% block content %}
<div class="logo">
    <h1>📱 Create Account</h1>
    <p>Join the Mobile Automation Platform</p>
</div>

<form id="registerForm">
    <div class="form-group">
        <label for="firstName">First Name</label>
        <input type="text" id="firstName" name="firstName" required>
    </div>
    
    <div class="form-group">
        <label for="lastName">Last Name</label>
        <input type="text" id="lastName" name="lastName" required>
    </div>
    
    <div class="form-group">
        <label for="email">Email Address</label>
        <input type="email" id="email" name="email" required>
    </div>
    
    <div class="form-group">
        <label for="password">Password</label>
        <input type="password" id="password" name="password" required minlength="8">
        <small style="color: #666; font-size: 0.8rem;">
            Minimum 8 characters required
        </small>
    </div>
    
    <div class="form-group">
        <label for="confirmPassword">Confirm Password</label>
        <input type="password" id="confirmPassword" name="confirmPassword" required>
    </div>
    
    <div class="loading">
        <div class="spinner"></div>
        <p>Creating account...</p>
    </div>
    
    <button type="submit" class="btn" id="registerBtn">Create Account</button>
</form>

<div class="link">
    <a href="/">Already have an account? Sign in here</a>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('registerForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    clearAlerts();
    
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    // Validate passwords match
    if (password !== confirmPassword) {
        showAlert('Passwords do not match');
        return;
    }
    
    // Validate password strength
    if (password.length < 8) {
        showAlert('Password must be at least 8 characters long');
        return;
    }
    
    showLoading(true);
    disableForm(true);
    
    const formData = {
        first_name: document.getElementById('firstName').value,
        last_name: document.getElementById('lastName').value,
        email: document.getElementById('email').value,
        password: password
    };
    
    try {
        const response = await fetch('/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            if (data.success) {
                showAlert('Account created successfully! Please sign in.', 'success');
                setTimeout(() => {
                    window.location.href = '/';
                }, 2000);
            }
        } else {
            showAlert(data.error || 'Registration failed');
        }
    } catch (error) {
        console.error('Registration error:', error);
        showAlert('Network error. Please try again.');
    } finally {
        showLoading(false);
        disableForm(false);
    }
});

// Real-time password confirmation validation
document.getElementById('confirmPassword').addEventListener('input', function(e) {
    const password = document.getElementById('password').value;
    const confirmPassword = e.target.value;
    
    if (confirmPassword && password !== confirmPassword) {
        e.target.style.borderColor = '#e53e3e';
    } else {
        e.target.style.borderColor = '#e1e5e9';
    }
});

// Focus first name field on load
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('firstName').focus();
});
</script>
{% endblock %}
