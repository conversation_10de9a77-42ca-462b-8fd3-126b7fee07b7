{% extends "base.html" %}

{% block title %}Setup 2FA - Mobile Automation App{% endblock %}

{% block extra_css %}
<style>
    .container {
        max-width: 500px;
    }
    
    .step-indicator {
        display: flex;
        justify-content: center;
        margin-bottom: 2rem;
    }
    
    .step {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: #e2e8f0;
        color: #718096;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        margin: 0 0.5rem;
        position: relative;
    }
    
    .step.active {
        background: #667eea;
        color: white;
    }
    
    .step.completed {
        background: #48bb78;
        color: white;
    }
    
    .step:not(:last-child)::after {
        content: '';
        position: absolute;
        left: 100%;
        top: 50%;
        transform: translateY(-50%);
        width: 20px;
        height: 2px;
        background: #e2e8f0;
        z-index: -1;
    }
    
    .step-content {
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .step-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
    }
    
    .step-description {
        color: #718096;
        margin-bottom: 1.5rem;
    }
    
    .qr-section {
        background: #f7fafc;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 1.5rem 0;
        text-align: center;
    }
    
    .qr-code img {
        max-width: 200px;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        margin: 1rem 0;
    }
    
    .app-links {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin: 1rem 0;
        flex-wrap: wrap;
    }
    
    .app-link {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        background: #4299e1;
        color: white;
        text-decoration: none;
        border-radius: 6px;
        font-size: 0.9rem;
        transition: background-color 0.2s ease;
    }
    
    .app-link:hover {
        background: #3182ce;
        color: white;
    }
    
    .verification-section {
        margin: 1.5rem 0;
    }
    
    .code-input {
        text-align: center;
        font-size: 1.2rem;
        letter-spacing: 0.2em;
        font-family: monospace;
    }
    
    .backup-codes {
        background: #fff5f5;
        border: 1px solid #fed7d7;
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
    }
    
    .backup-codes h4 {
        color: #c53030;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .backup-codes-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.5rem;
        font-family: monospace;
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }
    
    .backup-code {
        background: white;
        padding: 0.5rem;
        border-radius: 4px;
        text-align: center;
        border: 1px solid #fed7d7;
    }
    
    .navigation {
        display: flex;
        justify-content: space-between;
        margin-top: 2rem;
    }
    
    .btn-secondary {
        background: #e2e8f0;
        color: #4a5568;
    }
    
    .btn-secondary:hover {
        background: #cbd5e0;
    }
    
    .success-message {
        text-align: center;
        padding: 2rem;
    }
    
    .success-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="step-indicator">
    <div class="step active" id="step1">1</div>
    <div class="step" id="step2">2</div>
    <div class="step" id="step3">3</div>
</div>

<!-- Step 1: Download App -->
<div id="stepContent1" class="step-content">
    <div class="step-title">📱 Download Authenticator App</div>
    <div class="step-description">
        First, download a two-factor authentication app on your mobile device
    </div>
    
    <div class="app-links">
        <a href="https://apps.apple.com/app/google-authenticator/id388497605" target="_blank" class="app-link">
            🍎 Google Authenticator (iOS)
        </a>
        <a href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2" target="_blank" class="app-link">
            🤖 Google Authenticator (Android)
        </a>
    </div>
    
    <p style="color: #718096; font-size: 0.9rem; margin-top: 1rem;">
        You can also use other authenticator apps like Authy, Microsoft Authenticator, or 1Password.
    </p>
    
    <button class="btn" onclick="nextStep(2)">I've Downloaded the App</button>
</div>

<!-- Step 2: Scan QR Code -->
<div id="stepContent2" class="step-content" style="display: none;">
    <div class="step-title">📷 Scan QR Code</div>
    <div class="step-description">
        Open your authenticator app and scan this QR code
    </div>
    
    <div class="qr-section">
        <div class="qr-code">
            <img src="data:image/png;base64,{{ qr_code }}" alt="QR Code for 2FA Setup">
        </div>
        <p style="color: #718096; font-size: 0.9rem;">
            Can't scan? Manually enter this code in your app:<br>
            <code style="background: #e2e8f0; padding: 0.25rem 0.5rem; border-radius: 4px; font-family: monospace;">
                {{ secret }}
            </code>
        </p>
    </div>
    
    <div class="navigation">
        <button class="btn btn-secondary" onclick="previousStep(1)">Back</button>
        <button class="btn" onclick="nextStep(3)">I've Scanned the Code</button>
    </div>
</div>

<!-- Step 3: Verify Setup -->
<div id="stepContent3" class="step-content" style="display: none;">
    <div class="step-title">✅ Verify Setup</div>
    <div class="step-description">
        Enter the 6-digit code from your authenticator app to complete setup
    </div>
    
    <form id="verifyForm" class="verification-section">
        <div class="form-group">
            <label for="verificationCode">Verification Code</label>
            <input type="text" id="verificationCode" name="verificationCode" 
                   class="code-input" placeholder="000000" maxlength="6" required>
        </div>
        
        <div class="loading">
            <div class="spinner"></div>
            <p>Verifying code...</p>
        </div>
        
        <button type="submit" class="btn">Verify & Enable 2FA</button>
    </form>
    
    <div class="backup-codes">
        <h4>⚠️ Important: Save Your Backup Codes</h4>
        <p style="font-size: 0.9rem; color: #718096; margin-bottom: 1rem;">
            These codes can be used if you lose access to your authenticator app. 
            Store them in a safe place!
        </p>
        <div class="backup-codes-grid">
            {% for code in backup_codes %}
            <div class="backup-code">{{ code }}</div>
            {% endfor %}
        </div>
    </div>
    
    <div class="navigation">
        <button class="btn btn-secondary" onclick="previousStep(2)">Back</button>
    </div>
</div>

<!-- Success Message -->
<div id="successContent" class="step-content" style="display: none;">
    <div class="success-message">
        <div class="success-icon">🎉</div>
        <div class="step-title">Two-Factor Authentication Enabled!</div>
        <div class="step-description">
            Your account is now protected with two-factor authentication.
            You'll need to enter a code from your authenticator app when signing in.
        </div>
        <button class="btn" onclick="window.location.href='/dashboard'">
            Return to Dashboard
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentStep = 1;

function nextStep(step) {
    // Hide current step
    document.getElementById(`stepContent${currentStep}`).style.display = 'none';
    document.getElementById(`step${currentStep}`).classList.remove('active');
    document.getElementById(`step${currentStep}`).classList.add('completed');
    
    // Show next step
    currentStep = step;
    document.getElementById(`stepContent${currentStep}`).style.display = 'block';
    document.getElementById(`step${currentStep}`).classList.add('active');
}

function previousStep(step) {
    // Hide current step
    document.getElementById(`stepContent${currentStep}`).style.display = 'none';
    document.getElementById(`step${currentStep}`).classList.remove('active');
    
    // Show previous step
    document.getElementById(`step${step}`).classList.remove('completed');
    currentStep = step;
    document.getElementById(`stepContent${currentStep}`).style.display = 'block';
    document.getElementById(`step${currentStep}`).classList.add('active');
}

// Handle verification form
document.getElementById('verifyForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    clearAlerts();
    showLoading(true);
    disableForm(true);
    
    const code = document.getElementById('verificationCode').value;
    
    if (code.length !== 6) {
        showAlert('Please enter a 6-digit code');
        showLoading(false);
        disableForm(false);
        return;
    }
    
    try {
        const response = await fetch('/setup-2fa', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ token: code })
        });
        
        const data = await response.json();
        
        if (response.ok && data.success) {
            // Hide current step and show success
            document.getElementById(`stepContent${currentStep}`).style.display = 'none';
            document.getElementById(`step${currentStep}`).classList.remove('active');
            document.getElementById(`step${currentStep}`).classList.add('completed');
            document.getElementById('successContent').style.display = 'block';
        } else {
            showAlert(data.error || 'Verification failed. Please try again.');
        }
    } catch (error) {
        console.error('Verification error:', error);
        showAlert('Network error. Please try again.');
    } finally {
        showLoading(false);
        disableForm(false);
    }
});

// Auto-submit when 6-digit code is entered
document.getElementById('verificationCode').addEventListener('input', function(e) {
    if (e.target.value.length === 6) {
        setTimeout(() => {
            document.getElementById('verifyForm').dispatchEvent(new Event('submit'));
        }, 500);
    }
});

// Focus verification code input when step 3 is shown
function focusVerificationInput() {
    if (currentStep === 3) {
        setTimeout(() => {
            document.getElementById('verificationCode').focus();
        }, 100);
    }
}

// Override nextStep to focus input on step 3
const originalNextStep = nextStep;
nextStep = function(step) {
    originalNextStep(step);
    if (step === 3) {
        focusVerificationInput();
    }
};
</script>
{% endblock %}
