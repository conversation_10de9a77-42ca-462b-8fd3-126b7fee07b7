{% extends "base.html" %}

{% block title %}Dashboard - Mobile Automation App{% endblock %}

{% block extra_css %}
<style>
    .container {
        max-width: 800px;
    }
    
    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e1e5e9;
    }
    
    .welcome {
        color: #333;
    }
    
    .logout-btn {
        background: #e53e3e;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        text-decoration: none;
        font-size: 0.9rem;
        cursor: pointer;
    }
    
    .logout-btn:hover {
        background: #c53030;
    }
    
    .services-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }
    
    .service-card {
        background: #f7fafc;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .service-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }
    
    .service-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }
    
    .service-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
    }
    
    .service-status {
        font-size: 0.9rem;
        margin-bottom: 1rem;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        display: inline-block;
    }
    
    .status-running {
        background: #c6f6d5;
        color: #22543d;
    }
    
    .status-stopped {
        background: #fed7d7;
        color: #c53030;
    }
    
    .service-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
    }
    
    .btn-small {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
        border-radius: 6px;
        border: none;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }
    
    .btn-start {
        background: #48bb78;
        color: white;
    }
    
    .btn-start:hover {
        background: #38a169;
    }
    
    .btn-open {
        background: #4299e1;
        color: white;
    }
    
    .btn-open:hover {
        background: #3182ce;
    }
    
    .btn-start:disabled,
    .btn-open:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
    
    .security-section {
        background: #f7fafc;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .security-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .security-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }
    
    .status-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 0.5rem;
    }
    
    .status-green {
        background: #48bb78;
    }
    
    .status-red {
        background: #e53e3e;
    }
    
    @media (max-width: 600px) {
        .services-grid {
            grid-template-columns: 1fr;
        }
        
        .header {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }
        
        .security-actions {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="header">
    <div class="welcome">
        <h2>Welcome, {{ user_name }}! 👋</h2>
        <p style="color: #666; margin-top: 0.5rem;">Mobile Automation Control Center</p>
    </div>
    <a href="/logout" class="logout-btn">Logout</a>
</div>

<div class="services-grid">
    <div class="service-card">
        <div class="service-icon">📱</div>
        <div class="service-title">iOS Automation</div>
        <div class="service-status" id="iosStatus">
            <span class="status-indicator" id="iosIndicator"></span>
            <span id="iosStatusText">Checking...</span>
        </div>
        <div class="service-actions">
            <button class="btn-small btn-start" id="startIosBtn" onclick="startService('ios')">
                Start Service
            </button>
            <button class="btn-small btn-open" id="openIosBtn" onclick="openDashboard('ios')">
                Open Dashboard
            </button>
        </div>
    </div>
    
    <div class="service-card">
        <div class="service-icon">🤖</div>
        <div class="service-title">Android Automation</div>
        <div class="service-status" id="androidStatus">
            <span class="status-indicator" id="androidIndicator"></span>
            <span id="androidStatusText">Checking...</span>
        </div>
        <div class="service-actions">
            <button class="btn-small btn-start" id="startAndroidBtn" onclick="startService('android')">
                Start Service
            </button>
            <button class="btn-small btn-open" id="openAndroidBtn" onclick="openDashboard('android')">
                Open Dashboard
            </button>
        </div>
    </div>
</div>

<div class="security-section">
    <div class="security-title">
        🔐 Security Settings
    </div>
    <div class="security-actions">
        <button class="btn-small btn-open" onclick="window.location.href='/setup-2fa'">
            Setup Two-Factor Authentication
        </button>
        <button class="btn-small btn-start" onclick="trustDevice()">
            Trust This Device
        </button>
    </div>
</div>

<div class="loading" id="actionLoading">
    <div class="spinner"></div>
    <p id="loadingText">Processing...</p>
</div>
{% endblock %}

{% block extra_js %}
<script>
let statusCheckInterval;

// Check service status on load
document.addEventListener('DOMContentLoaded', function() {
    checkStatus();
    // Check status every 10 seconds
    statusCheckInterval = setInterval(checkStatus, 10000);
});

async function checkStatus() {
    try {
        const response = await fetch('/status');
        const data = await response.json();
        
        updateServiceStatus('ios', data.ios_running);
        updateServiceStatus('android', data.android_running);
        
    } catch (error) {
        console.error('Status check error:', error);
    }
}

function updateServiceStatus(service, isRunning) {
    const statusElement = document.getElementById(`${service}Status`);
    const indicatorElement = document.getElementById(`${service}Indicator`);
    const statusTextElement = document.getElementById(`${service}StatusText`);
    const startBtn = document.getElementById(`start${service.charAt(0).toUpperCase() + service.slice(1)}Btn`);
    const openBtn = document.getElementById(`open${service.charAt(0).toUpperCase() + service.slice(1)}Btn`);
    
    if (isRunning) {
        statusElement.className = 'service-status status-running';
        indicatorElement.className = 'status-indicator status-green';
        statusTextElement.textContent = 'Running';
        startBtn.textContent = 'Restart Service';
        openBtn.disabled = false;
    } else {
        statusElement.className = 'service-status status-stopped';
        indicatorElement.className = 'status-indicator status-red';
        statusTextElement.textContent = 'Stopped';
        startBtn.textContent = 'Start Service';
        openBtn.disabled = true;
    }
}

async function startService(service) {
    const loadingElement = document.getElementById('actionLoading');
    const loadingText = document.getElementById('loadingText');
    
    clearAlerts();
    loadingText.textContent = `Starting ${service.toUpperCase()} service...`;
    loadingElement.style.display = 'block';
    
    try {
        const response = await fetch(`/start-service/${service}`);
        const data = await response.json();
        
        if (response.ok && data.success) {
            showAlert(`${service.toUpperCase()} service started successfully!`, 'success');
            // Check status after a delay
            setTimeout(checkStatus, 2000);
        } else {
            showAlert(data.error || `Failed to start ${service} service`);
        }
    } catch (error) {
        console.error(`Error starting ${service} service:`, error);
        showAlert('Network error. Please try again.');
    } finally {
        loadingElement.style.display = 'none';
    }
}

async function openDashboard(service) {
    clearAlerts();
    
    try {
        const response = await fetch(`/open-dashboard/${service}`);
        const data = await response.json();
        
        if (response.ok && data.success) {
            showAlert(`${service.toUpperCase()} dashboard opened in new tab!`, 'success');
        } else {
            showAlert(data.error || `Failed to open ${service} dashboard`);
        }
    } catch (error) {
        console.error(`Error opening ${service} dashboard:`, error);
        showAlert('Network error. Please try again.');
    }
}

async function trustDevice() {
    clearAlerts();
    
    if (confirm('Trust this device for future logins? You will not need 2FA on this device.')) {
        showAlert('Device trust feature will be implemented in the next update.', 'info');
    }
}

// Cleanup interval on page unload
window.addEventListener('beforeunload', function() {
    if (statusCheckInterval) {
        clearInterval(statusCheckInterval);
    }
});
</script>
{% endblock %}
