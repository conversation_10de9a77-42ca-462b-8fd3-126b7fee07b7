#!/bin/bash

# Mobile Automation SaaS - Authentication Fix Deployment Script
# This script fixes authentication issues between main SaaS app and iOS/Android automation services
# Run this script on the production server to deploy the authentication fixes

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Dynamic path detection
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$SCRIPT_DIR"

# Verify we're in the correct project directory by checking for saas_infrastructure
if [ ! -d "$PROJECT_DIR/saas_infrastructure" ]; then
    error "saas_infrastructure directory not found. Please run this script from the project root directory."
    error "Current directory: $PROJECT_DIR"
    error "Expected to find: $PROJECT_DIR/saas_infrastructure"
    exit 1
fi

# Configuration
BACKUP_DIR="$PROJECT_DIR/backup_$(date +%Y%m%d_%H%M%S)"
LOG_FILE="$PROJECT_DIR/deployment_$(date +%Y%m%d_%H%M%S).log"

# Redirect all output to log file as well
exec > >(tee -a "$LOG_FILE")
exec 2>&1

log "Starting Mobile Automation SaaS Authentication Fix Deployment"
log "Project Directory: $PROJECT_DIR"
log "Backup Directory: $BACKUP_DIR"
log "Log File: $LOG_FILE"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check service status
check_service() {
    local port=$1
    local service_name=$2
    
    if curl -s "http://localhost:$port/health" >/dev/null 2>&1; then
        log "$service_name is running on port $port"
        return 0
    else
        warn "$service_name is not responding on port $port"
        return 1
    fi
}

# Function to kill processes on specific ports
kill_port_processes() {
    local port=$1
    local pids=$(lsof -ti:$port 2>/dev/null || true)
    
    if [ -n "$pids" ]; then
        log "Killing processes on port $port: $pids"
        echo $pids | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
}

# Step 1: Prerequisites Check
log "Step 1: Checking prerequisites..."

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    error "This script must be run as root"
    exit 1
fi

# Verify project structure
log "Project directory: $PROJECT_DIR"
log "Checking project structure..."

# Check for key directories and files
REQUIRED_DIRS=("saas_infrastructure" "saas_infrastructure/app" "saas_infrastructure/services")
REQUIRED_FILES=("saas_infrastructure/app/saas_app.py" "saas_infrastructure/services/ios_automation_service.py" "saas_infrastructure/services/android_automation_service.py")

for dir in "${REQUIRED_DIRS[@]}"; do
    if [ ! -d "$PROJECT_DIR/$dir" ]; then
        error "Required directory not found: $PROJECT_DIR/$dir"
        exit 1
    fi
done

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$PROJECT_DIR/$file" ]; then
        error "Required file not found: $PROJECT_DIR/$file"
        exit 1
    fi
done

log "Project structure verified"
cd "$PROJECT_DIR"

# Check Python
if ! command_exists python3; then
    error "Python3 is not installed"
    exit 1
fi

# Check PostgreSQL
if ! command_exists psql; then
    log "Installing PostgreSQL..."
    apt-get update
    apt-get install -y postgresql postgresql-contrib python3-psycopg2
    systemctl enable postgresql
    systemctl start postgresql
fi

# Check if PostgreSQL is running
if ! systemctl is-active --quiet postgresql; then
    log "Starting PostgreSQL..."
    systemctl start postgresql
fi

log "Prerequisites check completed"

# Step 2: Create backup
log "Step 2: Creating backup..."
mkdir -p "$BACKUP_DIR"

# Backup current codebase (excluding backup directory itself)
mkdir -p "$BACKUP_DIR/codebase_backup"
rsync -av --exclude="backup_*" --exclude="*.log" --exclude="__pycache__" --exclude="*.pyc" "$PROJECT_DIR/" "$BACKUP_DIR/codebase_backup/"

# Backup database if it exists
if sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw mobile_automation_saas; then
    log "Backing up database..."
    sudo -u postgres pg_dump mobile_automation_saas > "$BACKUP_DIR/database_backup.sql"
fi

log "Backup completed: $BACKUP_DIR"

# Step 3: Install Python dependencies
log "Step 3: Installing Python dependencies..."

# Install system packages
apt-get install -y python3-pip python3-venv libpq-dev

# Install Python packages
pip3 install --break-system-packages \
    flask flask-jwt-extended flask-cors flask-sqlalchemy \
    psycopg2-binary sqlalchemy requests bcrypt \
    gunicorn websockets simple-websocket

log "Python dependencies installed"

# Step 4: Database setup
log "Step 4: Setting up database..."

# Create database and user if they don't exist
sudo -u postgres psql << EOF
-- Create database if it doesn't exist
SELECT 'CREATE DATABASE mobile_automation_saas'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'mobile_automation_saas')\gexec

-- Create user if it doesn't exist
DO \$\$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_user WHERE usename = 'mobile_automation_app') THEN
        CREATE USER mobile_automation_app WITH PASSWORD 'secure_password_123';
    END IF;
END
\$\$;

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE mobile_automation_saas TO mobile_automation_app;
EOF

# Connect to the database and set up schema
sudo -u postgres psql -d mobile_automation_saas << 'EOF'
-- Grant schema permissions
GRANT ALL ON SCHEMA public TO mobile_automation_app;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO mobile_automation_app;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO mobile_automation_app;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO mobile_automation_app;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO mobile_automation_app;
EOF

log "Database setup completed"

# Step 5: Initialize database schema
log "Step 5: Initializing database schema..."

# Create database initialization script
cat > /tmp/init_database.py << 'EOF'
#!/usr/bin/env python3
import os
import psycopg2
import bcrypt
from datetime import datetime

def get_db_connection():
    return psycopg2.connect(
        host='localhost',
        port='5432',
        database='mobile_automation_saas',
        user='mobile_automation_app',
        password='secure_password_123'
    )

def create_tables():
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # Create tenants table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS tenants (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            subdomain VARCHAR(100) UNIQUE NOT NULL,
            is_active BOOLEAN DEFAULT true,
            subscription_tier VARCHAR(50) DEFAULT 'basic',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            settings JSONB DEFAULT '{}'
        )
    """)
    
    # Create users table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS users (
            id SERIAL PRIMARY KEY,
            tenant_id INTEGER NOT NULL,
            email VARCHAR(255) NOT NULL,
            password_hash TEXT NOT NULL,
            first_name VARCHAR(100),
            last_name VARCHAR(100),
            role VARCHAR(50) DEFAULT 'user',
            is_active BOOLEAN DEFAULT true,
            email_verified BOOLEAN DEFAULT false,
            phone VARCHAR(20),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            last_login_at TIMESTAMP WITH TIME ZONE,
            FOREIGN KEY (tenant_id) REFERENCES tenants(id),
            UNIQUE(tenant_id, email)
        )
    """)
    
    # Create automation_sessions table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS automation_sessions (
            id SERIAL PRIMARY KEY,
            token TEXT NOT NULL,
            user_id INTEGER NOT NULL,
            tenant_id INTEGER NOT NULL,
            platform VARCHAR(20) NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
            is_active BOOLEAN DEFAULT true,
            FOREIGN KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (tenant_id) REFERENCES tenants(id)
        )
    """)
    
    # Create indexes
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_automation_sessions_token ON automation_sessions(token)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_automation_sessions_user_tenant ON automation_sessions(user_id, tenant_id)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_automation_sessions_expires ON automation_sessions(expires_at)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_tenants_subdomain ON tenants(subdomain)")
    
    conn.commit()
    
    # Insert test data
    cursor.execute("SELECT COUNT(*) FROM tenants WHERE subdomain = 'testcompany1'")
    if cursor.fetchone()[0] == 0:
        cursor.execute("""
            INSERT INTO tenants (name, subdomain, is_active, subscription_tier)
            VALUES ('Test Company 1', 'testcompany1', true, 'premium')
        """)
    
    cursor.execute("SELECT id FROM tenants WHERE subdomain = 'testcompany1'")
    tenant_id = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM users WHERE email = '<EMAIL>' AND tenant_id = %s", (tenant_id,))
    if cursor.fetchone()[0] == 0:
        password_hash = bcrypt.hashpw('testpass123'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        cursor.execute("""
            INSERT INTO users (tenant_id, email, password_hash, first_name, last_name, role, is_active, email_verified)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """, (tenant_id, '<EMAIL>', password_hash, 'Test', 'User 1', 'admin', True, True))
    
    conn.commit()
    cursor.close()
    conn.close()
    print("Database schema initialized successfully")

if __name__ == "__main__":
    create_tables()
EOF

python3 /tmp/init_database.py
rm /tmp/init_database.py

log "Database schema initialized"

# Step 6: Set up environment variables
log "Step 6: Setting up environment variables..."

cat > "$PROJECT_DIR/.env" << EOF
# Database Configuration
DATABASE_URL=postgresql://mobile_automation_app:secure_password_123@localhost:5432/mobile_automation_saas
DB_HOST=localhost
DB_PORT=5432
DB_NAME=mobile_automation_saas
DB_USER=mobile_automation_app
DB_PASSWORD=secure_password_123

# JWT Configuration
SECRET_KEY=production-secret-key-$(openssl rand -hex 32)
JWT_SECRET_KEY=jwt-production-secret-$(openssl rand -hex 32)

# Service URLs
SAAS_BASE_URL=http://localhost:5000
IOS_SERVICE_URL=http://localhost:8080
ANDROID_SERVICE_URL=http://localhost:8081

# Flask Configuration
FLASK_ENV=production
FLASK_DEBUG=false

# Service Ports
SAAS_PORT=5000
IOS_SERVICE_PORT=8080
ANDROID_SERVICE_PORT=8081
EOF

log "Environment variables configured"

# Step 7: Stop existing services
log "Step 7: Stopping existing services..."

kill_port_processes 5000
kill_port_processes 8080
kill_port_processes 8081

# Kill any existing Python processes related to the project
pkill -f "saas_app.py" || true
pkill -f "ios_automation_service.py" || true
pkill -f "android_automation_service.py" || true

sleep 3

log "Existing services stopped"

# Step 8: Create service startup scripts
log "Step 8: Creating service startup scripts..."

# Create main SaaS service startup script
cat > "$PROJECT_DIR/start_saas_service.py" << 'EOF'
#!/usr/bin/env python3
import os
import sys
from pathlib import Path

# Get the directory where this script is located
script_dir = Path(__file__).parent.resolve()
project_root = script_dir

# Add project root to Python path
sys.path.insert(0, str(project_root))

# Load environment variables
env_file = project_root / '.env'
if env_file.exists():
    with open(env_file, 'r') as f:
        for line in f:
            if '=' in line and not line.startswith('#'):
                key, value = line.strip().split('=', 1)
                os.environ[key] = value

# Verify saas_infrastructure exists
saas_dir = project_root / 'saas_infrastructure'
if not saas_dir.exists():
    print(f"Error: saas_infrastructure directory not found at {saas_dir}")
    sys.exit(1)

# Import and run the SaaS application
try:
    print("Importing SaaSApp...")
    from saas_infrastructure.app.saas_app import SaaSApp

    if __name__ == "__main__":
        print("Creating SaaSApp instance...")
        app_instance = SaaSApp()

        # Debug: Print registered routes
        print("Registered routes:")
        automation_routes = []
        for rule in app_instance.app.url_map.iter_rules():
            route_info = f"  {rule.rule} [{', '.join(rule.methods)}]"
            print(route_info)
            if 'automation' in rule.rule:
                automation_routes.append(rule.rule)

        print(f"Found {len(automation_routes)} automation routes:")
        for route in automation_routes:
            print(f"  ✅ {route}")

        if not automation_routes:
            print("❌ WARNING: No automation routes found!")

        port = int(os.environ.get('SAAS_PORT', 5000))
        print(f"Starting SaaS application on port {port}")
        print(f"Project root: {project_root}")
        app_instance.app.run(host='0.0.0.0', port=port, debug=False)
except ImportError as e:
    print(f"Import error: {e}")
    print(f"Python path: {sys.path}")
    print(f"Current working directory: {os.getcwd()}")
    sys.exit(1)
except Exception as e:
    print(f"Error starting SaaS application: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
EOF

# Create iOS service startup script
cat > "$PROJECT_DIR/start_ios_service.py" << 'EOF'
#!/usr/bin/env python3
import os
import sys
from pathlib import Path

# Get the directory where this script is located
script_dir = Path(__file__).parent.resolve()
project_root = script_dir

# Add project root to Python path
sys.path.insert(0, str(project_root))

# Load environment variables
env_file = project_root / '.env'
if env_file.exists():
    with open(env_file, 'r') as f:
        for line in f:
            if '=' in line and not line.startswith('#'):
                key, value = line.strip().split('=', 1)
                os.environ[key] = value

# Verify saas_infrastructure exists
saas_dir = project_root / 'saas_infrastructure'
if not saas_dir.exists():
    print(f"Error: saas_infrastructure directory not found at {saas_dir}")
    sys.exit(1)

# Import and run the iOS automation service
try:
    from saas_infrastructure.services.ios_automation_service import iOSAutomationService

    if __name__ == "__main__":
        service = iOSAutomationService()
        port = int(os.environ.get('IOS_SERVICE_PORT', 8080))
        print(f"Starting iOS automation service on port {port}")
        print(f"Project root: {project_root}")
        service.app.run(host='0.0.0.0', port=port, debug=False)
except ImportError as e:
    print(f"Import error: {e}")
    print(f"Python path: {sys.path}")
    print(f"Current working directory: {os.getcwd()}")
    sys.exit(1)
EOF

# Create Android service startup script
cat > "$PROJECT_DIR/start_android_service.py" << 'EOF'
#!/usr/bin/env python3
import os
import sys
from pathlib import Path

# Get the directory where this script is located
script_dir = Path(__file__).parent.resolve()
project_root = script_dir

# Add project root to Python path
sys.path.insert(0, str(project_root))

# Load environment variables
env_file = project_root / '.env'
if env_file.exists():
    with open(env_file, 'r') as f:
        for line in f:
            if '=' in line and not line.startswith('#'):
                key, value = line.strip().split('=', 1)
                os.environ[key] = value

# Verify saas_infrastructure exists
saas_dir = project_root / 'saas_infrastructure'
if not saas_dir.exists():
    print(f"Error: saas_infrastructure directory not found at {saas_dir}")
    sys.exit(1)

# Import and run the Android automation service
try:
    from saas_infrastructure.services.android_automation_service import AndroidAutomationService

    if __name__ == "__main__":
        service = AndroidAutomationService()
        port = int(os.environ.get('ANDROID_SERVICE_PORT', 8081))
        print(f"Starting Android automation service on port {port}")
        print(f"Project root: {project_root}")
        service.app.run(host='0.0.0.0', port=port, debug=False)
except ImportError as e:
    print(f"Import error: {e}")
    print(f"Python path: {sys.path}")
    print(f"Current working directory: {os.getcwd()}")
    sys.exit(1)
EOF

# Make scripts executable
chmod +x "$PROJECT_DIR/start_saas_service.py"
chmod +x "$PROJECT_DIR/start_ios_service.py"
chmod +x "$PROJECT_DIR/start_android_service.py"

log "Service startup scripts created"

# Step 9: Start services
log "Step 9: Starting services..."

# Start main SaaS service
log "Starting main SaaS service..."
cd "$PROJECT_DIR"

# Create log directory if it doesn't exist
mkdir -p "$PROJECT_DIR/logs"

nohup python3 start_saas_service.py > "$PROJECT_DIR/logs/saas_service.log" 2>&1 &
SAAS_PID=$!
echo $SAAS_PID > "$PROJECT_DIR/saas_service.pid"

# Wait for SaaS service to start
sleep 10

# Check if SaaS service is running
if check_service 5000 "Main SaaS Service"; then
    log "Main SaaS service started successfully (PID: $SAAS_PID)"
else
    error "Failed to start main SaaS service"
    cat "$PROJECT_DIR/logs/saas_service.log" | tail -20
    exit 1
fi

# Start iOS automation service
log "Starting iOS automation service..."
nohup python3 start_ios_service.py > "$PROJECT_DIR/logs/ios_service.log" 2>&1 &
IOS_PID=$!
echo $IOS_PID > "$PROJECT_DIR/ios_service.pid"

# Wait for iOS service to start
sleep 5

# Check if iOS service is running
if check_service 8080 "iOS Automation Service"; then
    log "iOS automation service started successfully (PID: $IOS_PID)"
else
    warn "iOS automation service may have issues"
    cat "$PROJECT_DIR/logs/ios_service.log" | tail -10
fi

# Start Android automation service
log "Starting Android automation service..."
nohup python3 start_android_service.py > "$PROJECT_DIR/logs/android_service.log" 2>&1 &
ANDROID_PID=$!
echo $ANDROID_PID > "$PROJECT_DIR/android_service.pid"

# Wait for Android service to start
sleep 5

# Check if Android service is running
if check_service 8081 "Android Automation Service"; then
    log "Android automation service started successfully (PID: $ANDROID_PID)"
else
    warn "Android automation service may have issues"
    cat "$PROJECT_DIR/logs/android_service.log" | tail -10
fi

log "All services started"

# Step 9.5: Apply route registration fix
log "Step 9.5: Applying route registration fix..."

# Stop services to apply the fix
log "Stopping services to apply route registration fix..."
if [ -f "$PROJECT_DIR/saas_service.pid" ]; then
    SAAS_PID=$(cat "$PROJECT_DIR/saas_service.pid")
    if kill -0 $SAAS_PID 2>/dev/null; then
        kill $SAAS_PID
        log "Stopped SaaS service (PID: $SAAS_PID)"
    fi
fi

if [ -f "$PROJECT_DIR/ios_service.pid" ]; then
    IOS_PID=$(cat "$PROJECT_DIR/ios_service.pid")
    if kill -0 $IOS_PID 2>/dev/null; then
        kill $IOS_PID
        log "Stopped iOS service (PID: $IOS_PID)"
    fi
fi

if [ -f "$PROJECT_DIR/android_service.pid" ]; then
    ANDROID_PID=$(cat "$PROJECT_DIR/android_service.pid")
    if kill -0 $ANDROID_PID 2>/dev/null; then
        kill $ANDROID_PID
        log "Stopped Android service (PID: $ANDROID_PID)"
    fi
fi

# Wait for services to stop
sleep 3

# Restart services with the fix
log "Restarting services with route registration fix..."

# Start main SaaS service
log "Starting main SaaS service..."
nohup python3 start_saas_service.py > "$PROJECT_DIR/logs/saas_service.log" 2>&1 &
SAAS_PID=$!
echo $SAAS_PID > "$PROJECT_DIR/saas_service.pid"

# Wait for SaaS service to start
sleep 8

# Check if SaaS service is running
if check_service 5000 "Main SaaS Service"; then
    log "Main SaaS service restarted successfully (PID: $SAAS_PID)"
else
    error "Main SaaS service failed to restart"
    cat "$PROJECT_DIR/logs/saas_service.log" | tail -20
    exit 1
fi

# Start iOS automation service
log "Starting iOS automation service..."
nohup python3 start_ios_service.py > "$PROJECT_DIR/logs/ios_service.log" 2>&1 &
IOS_PID=$!
echo $IOS_PID > "$PROJECT_DIR/ios_service.pid"

# Wait for iOS service to start
sleep 5

# Check if iOS service is running
if check_service 8080 "iOS Automation Service"; then
    log "iOS automation service restarted successfully (PID: $IOS_PID)"
else
    warn "iOS automation service may have issues"
    cat "$PROJECT_DIR/logs/ios_service.log" | tail -10
fi

# Start Android automation service
log "Starting Android automation service..."
nohup python3 start_android_service.py > "$PROJECT_DIR/logs/android_service.log" 2>&1 &
ANDROID_PID=$!
echo $ANDROID_PID > "$PROJECT_DIR/android_service.pid"

# Wait for Android service to start
sleep 5

# Check if Android service is running
if check_service 8081 "Android Automation Service"; then
    log "Android automation service restarted successfully (PID: $ANDROID_PID)"
else
    warn "Android automation service may have issues"
    cat "$PROJECT_DIR/logs/android_service.log" | tail -10
fi

log "All services restarted with route registration fix"

# Step 10: Test route accessibility
log "Step 10: Testing route accessibility..."

# Wait a bit more for services to fully initialize
sleep 5

# Test health endpoint
if curl -s -f http://localhost:5000/health > /dev/null; then
    log "✅ Health endpoint accessible"
else
    warn "❌ Health endpoint not accessible"
fi

# Test automation routes (should return 401 without auth, not 404)
log "Testing automation route registration..."

ios_response=$(curl -s -o /dev/null -w "%{http_code}" -X POST http://localhost:5000/api/automation/ios/access)
android_response=$(curl -s -o /dev/null -w "%{http_code}" -X POST http://localhost:5000/api/automation/android/access)

if [ "$ios_response" = "404" ]; then
    warn "❌ iOS automation route not found (404) - Route registration issue!"
elif [ "$ios_response" = "401" ] || [ "$ios_response" = "403" ]; then
    log "✅ iOS automation route accessible (returns $ios_response - auth required)"
else
    log "ℹ️  iOS automation route returns: $ios_response"
fi

if [ "$android_response" = "404" ]; then
    warn "❌ Android automation route not found (404) - Route registration issue!"
elif [ "$android_response" = "401" ] || [ "$android_response" = "403" ]; then
    log "✅ Android automation route accessible (returns $android_response - auth required)"
else
    log "ℹ️  Android automation route returns: $android_response"
fi

# Step 11: Test authentication flow
log "Step 11: Testing authentication flow..."

# Create authentication test script
cat > /tmp/test_auth_flow.py << 'EOF'
#!/usr/bin/env python3
import requests
import json
import time

def test_authentication_flow():
    base_url = "http://localhost:5000"

    print("Testing authentication flow...")

    # Test 1: Login
    print("1. Testing login...")
    login_response = requests.post(f"{base_url}/api/auth/login",
                                 headers={
                                     'Content-Type': 'application/json',
                                     'X-Tenant-Subdomain': 'testcompany1'
                                 },
                                 json={
                                     'email': '<EMAIL>',
                                     'password': 'testpass123'
                                 })

    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code} - {login_response.text}")
        return False

    login_data = login_response.json()
    if not login_data.get('access_token'):
        print("❌ No access token in login response")
        return False

    print("✅ Login successful")
    access_token = login_data['access_token']

    # Test 2: iOS automation access
    print("2. Testing iOS automation access...")
    ios_response = requests.post(f"{base_url}/api/automation/ios/access",
                               headers={
                                   'Content-Type': 'application/json',
                                   'Authorization': f'Bearer {access_token}',
                                   'X-Tenant-Subdomain': 'testcompany1'
                               })

    if ios_response.status_code != 200:
        print(f"❌ iOS access failed: {ios_response.status_code} - {ios_response.text}")
        return False

    ios_data = ios_response.json()
    if not ios_data.get('access_url'):
        print("❌ No iOS access URL in response")
        return False

    print("✅ iOS access URL generated")

    # Test 3: iOS automation page
    print("3. Testing iOS automation page...")
    ios_url = ios_data['access_url']
    ios_page_response = requests.get(ios_url, allow_redirects=True)

    if ios_page_response.status_code == 200:
        print("✅ iOS automation page accessible")
    else:
        print(f"❌ iOS automation page failed: {ios_page_response.status_code}")
        return False

    # Test 4: Android automation access
    print("4. Testing Android automation access...")
    android_response = requests.post(f"{base_url}/api/automation/android/access",
                                   headers={
                                       'Content-Type': 'application/json',
                                       'Authorization': f'Bearer {access_token}',
                                       'X-Tenant-Subdomain': 'testcompany1'
                                   })

    if android_response.status_code != 200:
        print(f"❌ Android access failed: {android_response.status_code} - {android_response.text}")
        return False

    android_data = android_response.json()
    if not android_data.get('access_url'):
        print("❌ No Android access URL in response")
        return False

    print("✅ Android access URL generated")

    # Test 5: Android automation page
    print("5. Testing Android automation page...")
    android_url = android_data['access_url']
    android_page_response = requests.get(android_url, allow_redirects=True)

    if android_page_response.status_code == 200:
        print("✅ Android automation page accessible")
    else:
        print(f"❌ Android automation page failed: {android_page_response.status_code}")
        return False

    print("🎉 All authentication tests passed!")
    return True

if __name__ == "__main__":
    # Wait a bit for services to fully start
    time.sleep(5)

    success = test_authentication_flow()
    if success:
        print("\n✅ Authentication flow is working correctly!")
        exit(0)
    else:
        print("\n❌ Authentication flow has issues!")
        exit(1)
EOF

# Run authentication test
python3 /tmp/test_auth_flow.py
AUTH_TEST_RESULT=$?

if [ $AUTH_TEST_RESULT -eq 0 ]; then
    log "Authentication flow test passed"
else
    warn "Authentication flow test failed - check service logs"
fi

rm /tmp/test_auth_flow.py

# Step 11: Clean up temporary files
log "Step 11: Cleaning up temporary files..."

# Remove all temporary diagnostic and debug scripts
TEMP_FILES=(
    "test_auth_issue.py"
    "fix_authentication.py"
    "debug_auth_service.py"
    "fix_user_credentials.py"
    "check_database.py"
    "fix_database_schema.py"
    "simple_fix.py"
    "install_deps.py"
    "fix_authentication_flow.py"
    "comprehensive_fix.py"
    "manual_service_startup.py"
    "quick_fix_services.py"
)

for file in "${TEMP_FILES[@]}"; do
    if [ -f "$PROJECT_DIR/$file" ]; then
        log "Removing temporary file: $file"
        rm "$PROJECT_DIR/$file"
    fi
done

# Remove any __pycache__ directories
find "$PROJECT_DIR" -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

log "Temporary files cleaned up"

# Step 12: Create systemd services for production
log "Step 12: Creating systemd services..."

# Create systemd service for main SaaS app
cat > /etc/systemd/system/mobile-automation-saas.service << EOF
[Unit]
Description=Mobile Automation SaaS Main Application
After=network.target postgresql.service
Requires=postgresql.service

[Service]
Type=simple
User=root
WorkingDirectory=$PROJECT_DIR
Environment=PYTHONPATH=$PROJECT_DIR
ExecStart=/usr/bin/python3 $PROJECT_DIR/start_saas_service.py
Restart=always
RestartSec=10
StandardOutput=append:$PROJECT_DIR/logs/saas_service.log
StandardError=append:$PROJECT_DIR/logs/saas_service.log

[Install]
WantedBy=multi-user.target
EOF

# Create systemd service for iOS automation
cat > /etc/systemd/system/mobile-automation-ios.service << EOF
[Unit]
Description=Mobile Automation iOS Service
After=network.target mobile-automation-saas.service
Requires=mobile-automation-saas.service

[Service]
Type=simple
User=root
WorkingDirectory=$PROJECT_DIR
Environment=PYTHONPATH=$PROJECT_DIR
ExecStart=/usr/bin/python3 $PROJECT_DIR/start_ios_service.py
Restart=always
RestartSec=10
StandardOutput=append:$PROJECT_DIR/logs/ios_service.log
StandardError=append:$PROJECT_DIR/logs/ios_service.log

[Install]
WantedBy=multi-user.target
EOF

# Create systemd service for Android automation
cat > /etc/systemd/system/mobile-automation-android.service << EOF
[Unit]
Description=Mobile Automation Android Service
After=network.target mobile-automation-saas.service
Requires=mobile-automation-saas.service

[Service]
Type=simple
User=root
WorkingDirectory=$PROJECT_DIR
Environment=PYTHONPATH=$PROJECT_DIR
ExecStart=/usr/bin/python3 $PROJECT_DIR/start_android_service.py
Restart=always
RestartSec=10
StandardOutput=append:$PROJECT_DIR/logs/android_service.log
StandardError=append:$PROJECT_DIR/logs/android_service.log

[Install]
WantedBy=multi-user.target
EOF

# Reload systemd and enable services
systemctl daemon-reload
systemctl enable mobile-automation-saas.service
systemctl enable mobile-automation-ios.service
systemctl enable mobile-automation-android.service

log "Systemd services created and enabled"

# Step 13: Create management scripts
log "Step 13: Creating management scripts..."

# Create service management script
cat > "$PROJECT_DIR/manage_services.sh" << 'EOF'
#!/bin/bash

case "$1" in
    start)
        echo "Starting Mobile Automation services..."
        systemctl start mobile-automation-saas
        sleep 5
        systemctl start mobile-automation-ios
        systemctl start mobile-automation-android
        echo "Services started"
        ;;
    stop)
        echo "Stopping Mobile Automation services..."
        systemctl stop mobile-automation-android
        systemctl stop mobile-automation-ios
        systemctl stop mobile-automation-saas
        echo "Services stopped"
        ;;
    restart)
        echo "Restarting Mobile Automation services..."
        $0 stop
        sleep 3
        $0 start
        ;;
    status)
        echo "Mobile Automation Services Status:"
        echo "=================================="
        systemctl status mobile-automation-saas --no-pager -l
        echo ""
        systemctl status mobile-automation-ios --no-pager -l
        echo ""
        systemctl status mobile-automation-android --no-pager -l
        ;;
    logs)
        echo "Recent logs for all services:"
        echo "============================="
        journalctl -u mobile-automation-saas -n 20 --no-pager
        echo ""
        journalctl -u mobile-automation-ios -n 20 --no-pager
        echo ""
        journalctl -u mobile-automation-android -n 20 --no-pager
        ;;
    test)
        echo "Testing authentication flow..."
        python3 -c "
import requests
import json

try:
    # Test login
    response = requests.post('http://localhost:5000/api/auth/login',
                           headers={'Content-Type': 'application/json', 'X-Tenant-Subdomain': 'testcompany1'},
                           json={'email': '<EMAIL>', 'password': 'testpass123'})

    if response.status_code == 200:
        token = response.json().get('access_token')
        print('✅ Login successful')

        # Test iOS access
        ios_response = requests.post('http://localhost:5000/api/automation/ios/access',
                                   headers={'Authorization': f'Bearer {token}'})
        if ios_response.status_code == 200:
            print('✅ iOS automation access working')
        else:
            print('❌ iOS automation access failed')

        # Test Android access
        android_response = requests.post('http://localhost:5000/api/automation/android/access',
                                       headers={'Authorization': f'Bearer {token}'})
        if android_response.status_code == 200:
            print('✅ Android automation access working')
        else:
            print('❌ Android automation access failed')
    else:
        print('❌ Login failed')

except Exception as e:
    print(f'❌ Test failed: {e}')
"
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|logs|test}"
        exit 1
        ;;
esac
EOF

chmod +x "$PROJECT_DIR/manage_services.sh"

log "Management scripts created"

# Step 14: Final verification
log "Step 14: Final verification..."

# Check all services are running
sleep 5

SERVICES_OK=true

if ! check_service 5000 "Main SaaS Service"; then
    error "Main SaaS service is not running"
    SERVICES_OK=false
fi

if ! check_service 8080 "iOS Automation Service"; then
    warn "iOS automation service is not running"
fi

if ! check_service 8081 "Android Automation Service"; then
    warn "Android automation service is not running"
fi

# Display final status
log "Deployment completed!"
log "===================="
log "Services Status:"
log "- Main SaaS Application: http://localhost:5000"
log "- iOS Automation Service: http://localhost:8080"
log "- Android Automation Service: http://localhost:8081"
log ""
log "Access URLs:"
log "- Dashboard: http://testcompany1.localhost:5000"
log "- Or by IP: http://$(hostname -I | awk '{print $1}'):5000"
log ""
log "Management Commands:"
log "- Start services: $PROJECT_DIR/manage_services.sh start"
log "- Stop services: $PROJECT_DIR/manage_services.sh stop"
log "- Check status: $PROJECT_DIR/manage_services.sh status"
log "- View logs: $PROJECT_DIR/manage_services.sh logs"
log "- Test auth: $PROJECT_DIR/manage_services.sh test"
log ""
log "Log Files:"
log "- Deployment: $LOG_FILE"
log "- SaaS Service: $PROJECT_DIR/logs/saas_service.log"
log "- iOS Service: $PROJECT_DIR/logs/ios_service.log"
log "- Android Service: $PROJECT_DIR/logs/android_service.log"
log ""
log "Backup Location: $BACKUP_DIR"

if [ "$SERVICES_OK" = true ]; then
    log "🎉 Deployment successful! Authentication flow should now work correctly."
    exit 0
else
    error "⚠️  Deployment completed with some issues. Check service logs."
    exit 1
fi
