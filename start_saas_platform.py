#!/usr/bin/env python3
"""
Startup script for Mobile Automation SaaS Platform
Starts the main SaaS application and automation services
"""

import os
import sys
import time
import signal
import logging
import subprocess
import threading
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SaaSPlatformManager:
    def __init__(self):
        self.processes = {}
        self.running = False
        self.base_path = Path(__file__).parent
        
    def start_main_saas_app(self):
        """Start the main SaaS application"""
        try:
            logger.info("Starting main SaaS application...")
            
            # Set environment variables
            env = os.environ.copy()
            env.update({
                'FLASK_APP': 'saas_infrastructure/app/saas_app.py',
                'FLASK_ENV': os.environ.get('FLASK_ENV', 'production'),
                'SAAS_PORT': os.environ.get('SAAS_PORT', '5000')
            })
            
            # Start the main SaaS app
            saas_script = self.base_path / 'saas_infrastructure' / 'app' / 'saas_app.py'
            process = subprocess.Popen(
                [sys.executable, str(saas_script)],
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes['saas_app'] = process
            
            # Monitor output
            threading.Thread(
                target=self._monitor_output,
                args=('saas_app', process),
                daemon=True
            ).start()
            
            logger.info(f"Main SaaS application started (PID: {process.pid})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start main SaaS application: {e}")
            return False
    
    def start_automation_services(self):
        """Start the automation services using service manager"""
        try:
            logger.info("Starting automation services...")
            
            # Set environment variables
            env = os.environ.copy()
            env.update({
                'IOS_SERVICE_PORT': os.environ.get('IOS_SERVICE_PORT', '8080'),
                'ANDROID_SERVICE_PORT': os.environ.get('ANDROID_SERVICE_PORT', '8081'),
                'FLASK_ENV': os.environ.get('FLASK_ENV', 'production')
            })
            
            # Start the service manager
            service_manager_script = self.base_path / 'saas_infrastructure' / 'services' / 'service_manager.py'
            process = subprocess.Popen(
                [sys.executable, str(service_manager_script), 'start', '--daemon'],
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes['automation_services'] = process
            
            # Monitor output
            threading.Thread(
                target=self._monitor_output,
                args=('automation_services', process),
                daemon=True
            ).start()
            
            logger.info(f"Automation services started (PID: {process.pid})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start automation services: {e}")
            return False
    
    def start_all(self):
        """Start all platform components"""
        logger.info("Starting Mobile Automation SaaS Platform...")
        
        # Start main SaaS application first
        if not self.start_main_saas_app():
            logger.error("Failed to start main SaaS application")
            return False
        
        # Wait a moment for the main app to initialize
        time.sleep(3)
        
        # Start automation services
        if not self.start_automation_services():
            logger.error("Failed to start automation services")
            self.stop_all()
            return False
        
        self.running = True
        logger.info("Mobile Automation SaaS Platform started successfully!")
        
        # Print access information
        self._print_access_info()
        
        return True
    
    def stop_all(self):
        """Stop all platform components"""
        logger.info("Stopping Mobile Automation SaaS Platform...")
        
        for name, process in self.processes.items():
            try:
                logger.info(f"Stopping {name} (PID: {process.pid})")
                process.terminate()
                
                # Wait for graceful shutdown
                try:
                    process.wait(timeout=10)
                    logger.info(f"{name} stopped gracefully")
                except subprocess.TimeoutExpired:
                    logger.warning(f"Force killing {name}")
                    process.kill()
                    process.wait()
                    
            except Exception as e:
                logger.error(f"Error stopping {name}: {e}")
        
        self.processes.clear()
        self.running = False
        logger.info("Mobile Automation SaaS Platform stopped")
    
    def _monitor_output(self, name, process):
        """Monitor process output"""
        try:
            while process.poll() is None:
                output = process.stdout.readline()
                if output:
                    logger.info(f"[{name}] {output.strip()}")
                
                error = process.stderr.readline()
                if error:
                    logger.error(f"[{name}] {error.strip()}")
                    
        except Exception as e:
            logger.error(f"Error monitoring {name} output: {e}")
    
    def _print_access_info(self):
        """Print access information for the platform"""
        saas_port = os.environ.get('SAAS_PORT', '5000')
        ios_port = os.environ.get('IOS_SERVICE_PORT', '8080')
        android_port = os.environ.get('ANDROID_SERVICE_PORT', '8081')
        
        print("\n" + "="*60)
        print("Mobile Automation SaaS Platform - Access Information")
        print("="*60)
        print(f"Main SaaS Dashboard: http://localhost:{saas_port}")
        print(f"iOS Automation Service: http://localhost:{ios_port}")
        print(f"Android Automation Service: http://localhost:{android_port}")
        print("\nTo access tenant dashboards:")
        print(f"http://localhost:{saas_port}/<tenant-subdomain>/dashboard")
        print("\nTo stop the platform, press Ctrl+C")
        print("="*60 + "\n")
    
    def health_check(self):
        """Check health of all components"""
        import requests
        
        results = {}
        
        # Check main SaaS app
        try:
            saas_port = os.environ.get('SAAS_PORT', '5000')
            response = requests.get(f'http://localhost:{saas_port}/health', timeout=5)
            results['saas_app'] = {
                'status': 'healthy' if response.status_code == 200 else 'unhealthy',
                'status_code': response.status_code
            }
        except Exception as e:
            results['saas_app'] = {'status': 'unreachable', 'error': str(e)}
        
        # Check automation services
        for service, port in [('ios_automation', '8080'), ('android_automation', '8081')]:
            try:
                port = os.environ.get(f'{service.upper()}_SERVICE_PORT', port)
                response = requests.get(f'http://localhost:{port}/health', timeout=5)
                results[service] = {
                    'status': 'healthy' if response.status_code == 200 else 'unhealthy',
                    'status_code': response.status_code
                }
            except Exception as e:
                results[service] = {'status': 'unreachable', 'error': str(e)}
        
        return results
    
    def run_forever(self):
        """Run platform indefinitely"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, shutting down...")
            self.stop_all()
            sys.exit(0)
        
        # Register signal handlers
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Start all components
        if not self.start_all():
            logger.error("Failed to start platform")
            sys.exit(1)
        
        try:
            while self.running:
                time.sleep(30)
                
                # Check if any processes have died
                for name, process in list(self.processes.items()):
                    if process.poll() is not None:
                        logger.error(f"Process {name} has died (exit code: {process.returncode})")
                        # Optionally restart the process
                        
        except KeyboardInterrupt:
            logger.info("Keyboard interrupt received")
        finally:
            self.stop_all()

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Mobile Automation SaaS Platform Manager')
    parser.add_argument('command', choices=['start', 'stop', 'health', 'run'], 
                       help='Command to execute')
    
    args = parser.parse_args()
    
    manager = SaaSPlatformManager()
    
    if args.command == 'start':
        manager.start_all()
        
    elif args.command == 'stop':
        manager.stop_all()
        
    elif args.command == 'health':
        health = manager.health_check()
        for component, status in health.items():
            print(f"{component}: {status}")
            
    elif args.command == 'run':
        manager.run_forever()

if __name__ == '__main__':
    main()
