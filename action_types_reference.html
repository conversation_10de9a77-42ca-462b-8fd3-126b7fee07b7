<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile App Automation Tool - Action Types Reference</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.2em;
        }
        .platform-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
            margin-left: 8px;
        }
        .ios-badge {
            background-color: #007AFF;
            color: white;
        }
        .android-badge {
            background-color: #3DDC84;
            color: white;
        }
        .both-badge {
            background-color: #6C757D;
            color: white;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 14px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #34495e;
            color: white;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        tr:hover {
            background-color: #e3f2fd;
        }
        .action-type {
            font-weight: bold;
            color: #2c3e50;
        }
        .method-code {
            font-family: 'Courier New', monospace;
            background-color: #f1f2f6;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 0.9em;
        }
        .driver-method {
            font-family: 'Courier New', monospace;
            background-color: #e8f5e8;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 0.85em;
            color: #2d5016;
        }
        .legend {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .legend h3 {
            margin-top: 0;
            color: #495057;
        }
        .search-container {
            margin-bottom: 20px;
            text-align: center;
        }
        .search-input {
            padding: 10px;
            font-size: 16px;
            border: 2px solid #ddd;
            border-radius: 5px;
            width: 300px;
            max-width: 100%;
        }
        .search-input:focus {
            outline: none;
            border-color: #007bff;
        }
        .category-header {
            background-color: #3498db !important;
            color: white;
            font-size: 1.1em;
            text-align: center;
        }
        .deprecated {
            opacity: 0.6;
            text-decoration: line-through;
        }
        .new-feature {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Mobile App Automation Tool</h1>
        <div class="subtitle">Action Types Reference Documentation</div>
        
        <div class="legend">
            <h3>Platform Support Legend</h3>
            <span class="platform-badge ios-badge">iOS</span> iOS only &nbsp;&nbsp;
            <span class="platform-badge android-badge">Android</span> Android only &nbsp;&nbsp;
            <span class="platform-badge both-badge">Both</span> Cross-platform support
        </div>

        <div class="search-container">
            <input type="text" class="search-input" id="searchInput" placeholder="Search action types, methods, or drivers...">
        </div>

        <table id="actionTable">
            <thead>
                <tr>
                    <th style="width: 20%">Action Type</th>
                    <th style="width: 25%">Sub-type/Method</th>
                    <th style="width: 35%">Driver/Tool Methods Used</th>
                    <th style="width: 20%">Platform Support</th>
                </tr>
            </thead>
            <tbody>
                <!-- Basic Actions -->
                <tr class="category-header">
                    <td colspan="4"><strong>BASIC INTERACTION ACTIONS</strong></td>
                </tr>
                <tr>
                    <td class="action-type">Tap</td>
                    <td>Coordinate-based</td>
                    <td><span class="driver-method">Appium TouchAction.tap()</span>, <span class="driver-method">W3C Actions API</span>, <span class="driver-method">AirTest touch() (fallback)</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Tap</td>
                    <td>Image-based</td>
                    <td><span class="driver-method">AirTest Template + wait()</span>, <span class="driver-method">OpenCV template matching</span>, <span class="driver-method">Appium TouchAction.tap()</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Tap</td>
                    <td>Locator-based</td>
                    <td><span class="driver-method">Appium driver.find_element()</span>, <span class="driver-method">WebElement.click()</span>, <span class="driver-method">UIAutomator2 (Android fallback)</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Double Tap</td>
                    <td>Coordinate-based</td>
                    <td><span class="driver-method">Appium TouchAction.tap().tap()</span>, <span class="driver-method">Multiple W3C Actions</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Swipe</td>
                    <td>Coordinate-based</td>
                    <td><span class="driver-method">Appium TouchAction.press().move_to().release()</span>, <span class="driver-method">W3C Actions API</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Swipe</td>
                    <td>Element-based</td>
                    <td><span class="driver-method">Appium driver.swipe()</span>, <span class="driver-method">Element bounds calculation</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>

                <!-- Text Input Actions -->
                <tr class="category-header">
                    <td colspan="4"><strong>TEXT INPUT ACTIONS</strong></td>
                </tr>
                <tr>
                    <td class="action-type">Input Text</td>
                    <td>Direct input</td>
                    <td><span class="driver-method">Appium driver.send_keys()</span>, <span class="driver-method">WebElement.send_keys()</span>, <span class="driver-method">ADB shell input (Android)</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Input Text</td>
                    <td>iOS keyboard</td>
                    <td><span class="driver-method">XCUITest mobile:typeText</span>, <span class="driver-method">iOS keyboard element interaction</span></td>
                    <td><span class="platform-badge ios-badge">iOS</span></td>
                </tr>
                <tr class="new-feature">
                    <td class="action-type">Clear Text</td>
                    <td>Android CTRL+A + DELETE</td>
                    <td><span class="driver-method">ADB shell input keyevent KEYCODE_A + CTRL</span>, <span class="driver-method">ADB shell input keyevent KEYCODE_DEL</span></td>
                    <td><span class="platform-badge android-badge">Android</span></td>
                </tr>
                <tr>
                    <td class="action-type">Tap and Type</td>
                    <td>Combined action</td>
                    <td><span class="driver-method">Appium TouchAction + send_keys()</span>, <span class="driver-method">Element focus + text input</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>

                <!-- Image Recognition Actions -->
                <tr class="category-header">
                    <td colspan="4"><strong>IMAGE RECOGNITION ACTIONS</strong></td>
                </tr>
                <tr>
                    <td class="action-type">Click Image</td>
                    <td>Template matching</td>
                    <td><span class="driver-method">AirTest Template + exists()</span>, <span class="driver-method">OpenCV template matching</span>, <span class="driver-method">AirTest touch()</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Double Click Image</td>
                    <td>Template matching</td>
                    <td><span class="driver-method">AirTest Template + exists()</span>, <span class="driver-method">Multiple AirTest touch()</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Tap If Image Exists</td>
                    <td>Conditional image tap</td>
                    <td><span class="driver-method">AirTest Template + wait()</span>, <span class="driver-method">Conditional Appium TouchAction</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Take Screenshot</td>
                    <td>Screen capture</td>
                    <td><span class="driver-method">Appium driver.get_screenshot_as_file()</span>, <span class="driver-method">AirTest snapshot()</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>

                <!-- Text Recognition Actions -->
                <tr class="category-header">
                    <td colspan="4"><strong>TEXT RECOGNITION ACTIONS</strong></td>
                </tr>
                <tr>
                    <td class="action-type">Tap on Text</td>
                    <td>XPath text search</td>
                    <td><span class="driver-method">Appium XPath text() functions</span>, <span class="driver-method">WebElement.click()</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Tap on Text</td>
                    <td>OCR-based</td>
                    <td><span class="driver-method">AirTest text() recognition</span>, <span class="driver-method">Tesseract OCR</span>, <span class="driver-method">AirTest touch()</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Tap If Text Exists</td>
                    <td>Conditional text tap</td>
                    <td><span class="driver-method">Appium XPath text search</span>, <span class="driver-method">AirTest text() fallback</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>

                <!-- App Management Actions -->
                <tr class="category-header">
                    <td colspan="4"><strong>APP MANAGEMENT ACTIONS</strong></td>
                </tr>
                <tr>
                    <td class="action-type">Launch App</td>
                    <td>Bundle/Package launch</td>
                    <td><span class="driver-method">Appium driver.activate_app()</span>, <span class="driver-method">ADB shell am start (Android)</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Terminate App</td>
                    <td>App termination</td>
                    <td><span class="driver-method">Appium driver.terminate_app()</span>, <span class="driver-method">ADB shell am force-stop (Android)</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Restart App</td>
                    <td>App restart</td>
                    <td><span class="driver-method">Appium terminate + activate</span>, <span class="driver-method">ADB shell commands (Android)</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Uninstall App</td>
                    <td>App removal</td>
                    <td><span class="driver-method">Appium driver.remove_app()</span>, <span class="driver-method">ADB shell pm uninstall (Android)</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>

                <!-- Wait Actions -->
                <tr class="category-header">
                    <td colspan="4"><strong>WAIT & TIMING ACTIONS</strong></td>
                </tr>
                <tr>
                    <td class="action-type">Wait</td>
                    <td>Fixed time delay</td>
                    <td><span class="driver-method">Python time.sleep()</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Wait Till</td>
                    <td>Element presence</td>
                    <td><span class="driver-method">Selenium WebDriverWait</span>, <span class="driver-method">Expected Conditions</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Wait Element</td>
                    <td>Element visibility</td>
                    <td><span class="driver-method">Appium WebDriverWait</span>, <span class="driver-method">EC.visibility_of_element_located</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Swipe Till Visible</td>
                    <td>Scroll until element</td>
                    <td><span class="driver-method">Appium swipe() + find_element()</span>, <span class="driver-method">Iterative scroll operations</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>

                <!-- Platform-Specific Actions -->
                <tr class="category-header">
                    <td colspan="4"><strong>PLATFORM-SPECIFIC ACTIONS</strong></td>
                </tr>
                <tr>
                    <td class="action-type">iOS Functions</td>
                    <td>Home button</td>
                    <td><span class="driver-method">XCUITest mobile:pressButton</span>, <span class="driver-method">AirTest iOS home()</span></td>
                    <td><span class="platform-badge ios-badge">iOS</span></td>
                </tr>
                <tr>
                    <td class="action-type">iOS Functions</td>
                    <td>Lock/Unlock device</td>
                    <td><span class="driver-method">XCUITest mobile:lock</span>, <span class="driver-method">AirTest iOS lock/unlock()</span></td>
                    <td><span class="platform-badge ios-badge">iOS</span></td>
                </tr>
                <tr>
                    <td class="action-type">Android Functions</td>
                    <td>Back button</td>
                    <td><span class="driver-method">Appium driver.back()</span>, <span class="driver-method">ADB shell input keyevent KEYCODE_BACK</span></td>
                    <td><span class="platform-badge android-badge">Android</span></td>
                </tr>
                <tr>
                    <td class="action-type">Device Back</td>
                    <td>Navigation back</td>
                    <td><span class="driver-method">Appium driver.back()</span>, <span class="driver-method">ADB shell input keyevent 4</span></td>
                    <td><span class="platform-badge android-badge">Android</span></td>
                </tr>

                <!-- Conditional Actions -->
                <tr class="category-header">
                    <td colspan="4"><strong>CONDITIONAL & FLOW CONTROL</strong></td>
                </tr>
                <tr>
                    <td class="action-type">If-Then Steps</td>
                    <td>Element existence</td>
                    <td><span class="driver-method">Appium find_element()</span>, <span class="driver-method">Conditional action execution</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">If-Then Steps</td>
                    <td>Text/Image conditions</td>
                    <td><span class="driver-method">AirTest Template/text recognition</span>, <span class="driver-method">Conditional branching</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Multi Step</td>
                    <td>Sequential execution</td>
                    <td><span class="driver-method">Action Factory pattern</span>, <span class="driver-method">Sequential action execution</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Repeat Steps</td>
                    <td>Loop execution</td>
                    <td><span class="driver-method">Python loop + Action Factory</span>, <span class="driver-method">Iterative action execution</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr class="new-feature">
                    <td class="action-type">Cleanup Steps</td>
                    <td>Failure cleanup</td>
                    <td><span class="driver-method">Conditional execution on test failure</span>, <span class="driver-method">Action Factory cleanup</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>

                <!-- Data & Validation Actions -->
                <tr class="category-header">
                    <td colspan="4"><strong>DATA & VALIDATION ACTIONS</strong></td>
                </tr>
                <tr>
                    <td class="action-type">Get Value</td>
                    <td>Element text/attribute</td>
                    <td><span class="driver-method">WebElement.text</span>, <span class="driver-method">WebElement.get_attribute()</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Compare Value</td>
                    <td>Value validation</td>
                    <td><span class="driver-method">String comparison</span>, <span class="driver-method">Element value extraction</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Check If Exists</td>
                    <td>Element presence</td>
                    <td><span class="driver-method">Appium find_element() with timeout</span>, <span class="driver-method">Exception handling</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Get Param / Set Param</td>
                    <td>Variable management</td>
                    <td><span class="driver-method">Database parameter storage</span>, <span class="driver-method">Session variable management</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>

                <!-- Utility Actions -->
                <tr class="category-header">
                    <td colspan="4"><strong>UTILITY & LOGGING ACTIONS</strong></td>
                </tr>
                <tr>
                    <td class="action-type">Info</td>
                    <td>Logging with env vars</td>
                    <td><span class="driver-method">Python logging</span>, <span class="driver-method">Environment variable parsing</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Hook Action</td>
                    <td>Custom script execution</td>
                    <td><span class="driver-method">Python exec()</span>, <span class="driver-method">Custom code execution</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Random Data</td>
                    <td>Test data generation</td>
                    <td><span class="driver-method">Python random/faker libraries</span>, <span class="driver-method">Data generation algorithms</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>

                <!-- Advanced Features -->
                <tr class="category-header">
                    <td colspan="4"><strong>ADVANCED FEATURES</strong></td>
                </tr>
                <tr class="new-feature">
                    <td class="action-type">QR/Barcode Scan</td>
                    <td>Code recognition</td>
                    <td><span class="driver-method">ZXing library</span>, <span class="driver-method">Appium screenshot + decode</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr class="new-feature">
                    <td class="action-type">Healenium Integration</td>
                    <td>Self-healing locators</td>
                    <td><span class="driver-method">Healenium proxy</span>, <span class="driver-method">AI-powered locator recovery</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
                <tr>
                    <td class="action-type">Fallback Mechanisms</td>
                    <td>60s timeout + retries</td>
                    <td><span class="driver-method">UIAutomator2 fallback (Android)</span>, <span class="driver-method">Coordinate/Image fallbacks</span>, <span class="driver-method">AirTest as last resort</span></td>
                    <td><span class="platform-badge both-badge">Both</span></td>
                </tr>
            </tbody>
        </table>
    </div>

    <script>
        // Search functionality
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const table = document.getElementById('actionTable');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) { // Skip header row
                const row = rows[i];
                if (row.classList.contains('category-header')) {
                    row.style.display = ''; // Always show category headers
                    continue;
                }
                
                const cells = row.getElementsByTagName('td');
                let found = false;
                
                for (let j = 0; j < cells.length; j++) {
                    if (cells[j].textContent.toLowerCase().includes(searchTerm)) {
                        found = true;
                        break;
                    }
                }
                
                row.style.display = found ? '' : 'none';
            }
        });

        // Add copy functionality for code elements
        document.querySelectorAll('.driver-method').forEach(element => {
            element.style.cursor = 'pointer';
            element.title = 'Click to copy';
            element.addEventListener('click', function() {
                navigator.clipboard.writeText(this.textContent).then(() => {
                    const original = this.textContent;
                    this.textContent = 'Copied!';
                    setTimeout(() => {
                        this.textContent = original;
                    }, 1000);
                });
            });
        });
    </script>
</body>
</html>
