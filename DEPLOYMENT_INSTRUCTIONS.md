# Mobile Automation SaaS - Authentication Fix Deployment Instructions

## Overview

This document provides step-by-step instructions for deploying the authentication fix for the Mobile Automation SaaS platform on a production server. The fix resolves the "Error accessing iOS automation: Authentication required" issue that occurs when users try to access iOS/Android automation interfaces from the tenant dashboard.

## Problem Description

Users were experiencing authentication failures when:
1. Logging into the tenant dashboard successfully
2. Clicking the iOS or Android automation buttons
3. Getting "Authentication required" errors instead of accessing the automation interfaces

The root cause was improper JWT token validation between the main SaaS application (port 5000) and the automation services (iOS on port 8080, Android on port 8081).

## Prerequisites

### Server Requirements
- **Operating System**: Ubuntu 20.04 LTS or newer (or compatible Linux distribution)
- **RAM**: Minimum 4GB, recommended 8GB
- **Storage**: Minimum 20GB free space
- **Network**: Internet access for package installation
- **Permissions**: Root access to the server

### Required Software (will be installed by the script)
- Python 3.8+
- PostgreSQL 12+
- Git
- Essential build tools

## Deployment Steps

### Step 1: Connect to Server

```bash
# SSH into your production server as root
ssh root@your-server-ip

# Or if using a non-root user with sudo access
ssh your-username@your-server-ip
sudo su -
```

### Step 2: Navigate to Project Directory

```bash
# Navigate to your project directory (adjust path as needed)
cd /path/to/your/MobileApp-AutoTest

# Or if the project is in a different location, find it:
find / -name "saas_infrastructure" -type d 2>/dev/null | head -1 | xargs dirname

# Verify you're in the correct directory
ls -la
# You should see saas_infrastructure/ directory and other project files
```

**Note**: The deployment script automatically detects the project directory based on where it's located, so you don't need to use hardcoded paths. Just ensure you run the script from the project root directory.

### Step 3: Execute Deployment Script

```bash
# Ensure you're in the project root directory (where saas_infrastructure/ exists)
pwd
ls -la | grep saas_infrastructure

# Make the deployment script executable
chmod +x deploy_authentication_fix.sh

# Run the deployment script
./deploy_authentication_fix.sh
```

**Important**: The script uses dynamic path detection and must be run from the project root directory. It will automatically:
- Detect the correct project paths
- Verify the project structure
- Create all necessary directories and files
- Work regardless of the absolute path location

The script will automatically:
- Check and install prerequisites
- Create a backup of current system
- Set up PostgreSQL database
- Install Python dependencies
- Initialize database schema
- Configure environment variables
- Start all services
- Test authentication flow
- Clean up temporary files
- Create systemd services for production
- Set up management scripts

### Step 4: Monitor Deployment Progress

The script provides colored output showing progress:
- 🟢 **Green**: Successful operations
- 🟡 **Yellow**: Warnings (non-critical)
- 🔴 **Red**: Errors (critical)
- 🔵 **Blue**: Information

Watch for any error messages and note the log file location shown at the beginning.

### Step 5: Verify Deployment

After the script completes, verify the deployment:

```bash
# Check service status
./manage_services.sh status

# Test authentication flow
./manage_services.sh test

# View recent logs
./manage_services.sh logs
```

### Step 6: Access the Application

1. **Local Access** (from server):
   ```
   http://localhost:5000
   ```

2. **Remote Access** (from browser):
   ```
   http://your-server-ip:5000
   ```

3. **Tenant-specific Access**:
   ```
   http://testcompany1.your-server-ip:5000
   ```

### Step 7: Test Complete Authentication Flow

1. **Login to Dashboard**:
   - Email: `<EMAIL>`
   - Password: `testpass123`
   - Tenant: `testcompany1`

2. **Test iOS Automation**:
   - Click "iOS Automation" button in dashboard
   - Should open iOS automation interface without authentication errors

3. **Test Android Automation**:
   - Click "Android Automation" button in dashboard
   - Should open Android automation interface without authentication errors

## Service Management

### Using the Management Script

```bash
# Start all services
./manage_services.sh start

# Stop all services
./manage_services.sh stop

# Restart all services
./manage_services.sh restart

# Check service status
./manage_services.sh status

# View recent logs
./manage_services.sh logs

# Test authentication flow
./manage_services.sh test
```

### Using Systemd (Alternative)

```bash
# Individual service control
systemctl start mobile-automation-saas
systemctl start mobile-automation-ios
systemctl start mobile-automation-android

# Check status
systemctl status mobile-automation-saas
systemctl status mobile-automation-ios
systemctl status mobile-automation-android

# View logs
journalctl -u mobile-automation-saas -f
journalctl -u mobile-automation-ios -f
journalctl -u mobile-automation-android -f
```

## Configuration Files

### Environment Variables (.env)
Located at: `/root/MobileApp-AutoTest/.env`

Key configurations:
- Database connection settings
- JWT secret keys
- Service URLs and ports
- Flask environment settings

### Service Startup Scripts
- `start_saas_service.py` - Main SaaS application
- `start_ios_service.py` - iOS automation service
- `start_android_service.py` - Android automation service

### Systemd Services
- `/etc/systemd/system/mobile-automation-saas.service`
- `/etc/systemd/system/mobile-automation-ios.service`
- `/etc/systemd/system/mobile-automation-android.service`

## Log Files

### Deployment Logs
- Deployment log: `PROJECT_DIR/deployment_YYYYMMDD_HHMMSS.log`

### Service Logs
- Main SaaS: `PROJECT_DIR/logs/saas_service.log`
- iOS Service: `PROJECT_DIR/logs/ios_service.log`
- Android Service: `PROJECT_DIR/logs/android_service.log`

**Note**: `PROJECT_DIR` refers to your actual project directory path, which is automatically detected by the deployment script.

### Systemd Logs
```bash
# View live logs
journalctl -u mobile-automation-saas -f
journalctl -u mobile-automation-ios -f
journalctl -u mobile-automation-android -f

# View recent logs
journalctl -u mobile-automation-saas -n 50
```

## Database Information

### Connection Details
- **Host**: localhost
- **Port**: 5432
- **Database**: mobile_automation_saas
- **User**: mobile_automation_app
- **Password**: secure_password_123

### Key Tables
- `tenants` - Tenant/company information
- `users` - User accounts
- `automation_sessions` - JWT session tokens for automation services

### Database Access
```bash
# Connect to database
sudo -u postgres psql -d mobile_automation_saas

# Or using application user
psql -h localhost -U mobile_automation_app -d mobile_automation_saas
```

## Troubleshooting

### Common Issues

#### 1. Services Not Starting
```bash
# Check logs for errors
./manage_services.sh logs

# Check individual service logs
tail -f /var/log/saas_service.log
tail -f /var/log/ios_service.log
tail -f /var/log/android_service.log
```

#### 2. Database Connection Issues
```bash
# Check PostgreSQL status
systemctl status postgresql

# Test database connection
psql -h localhost -U mobile_automation_app -d mobile_automation_saas -c "SELECT 1;"
```

#### 3. Port Conflicts
```bash
# Check what's using the ports
lsof -i :5000
lsof -i :8080
lsof -i :8081

# Kill conflicting processes
./manage_services.sh stop
pkill -f "python.*saas"
```

#### 4. Authentication Still Failing
```bash
# Test authentication flow
./manage_services.sh test

# Check JWT secret keys are consistent
grep JWT_SECRET_KEY /root/MobileApp-AutoTest/.env

# Verify database schema
psql -h localhost -U mobile_automation_app -d mobile_automation_saas -c "\dt"
```

### Recovery Procedures

#### Restore from Backup
```bash
# Stop services
./manage_services.sh stop

# Restore database
sudo -u postgres psql -d mobile_automation_saas < /root/backup_YYYYMMDD_HHMMSS/database_backup.sql

# Restore codebase
cp -r /root/backup_YYYYMMDD_HHMMSS/codebase_backup/* /root/MobileApp-AutoTest/

# Restart services
./manage_services.sh start
```

#### Reset Database
```bash
# Stop services
./manage_services.sh stop

# Drop and recreate database
sudo -u postgres psql -c "DROP DATABASE IF EXISTS mobile_automation_saas;"
sudo -u postgres psql -c "CREATE DATABASE mobile_automation_saas OWNER mobile_automation_app;"

# Re-run deployment script
./deploy_authentication_fix.sh
```

## Security Considerations

### Production Hardening
1. **Change Default Passwords**: Update database passwords in `.env` file
2. **Firewall Configuration**: Restrict access to ports 5000, 8080, 8081
3. **SSL/TLS**: Configure HTTPS with proper certificates
4. **User Management**: Create proper user accounts instead of using test credentials

### Network Security
```bash
# Configure firewall (example with ufw)
ufw allow 22/tcp    # SSH
ufw allow 5000/tcp  # Main SaaS
ufw allow 8080/tcp  # iOS Service
ufw allow 8081/tcp  # Android Service
ufw enable
```

## Support and Maintenance

### Regular Maintenance
- Monitor log files for errors
- Check disk space usage
- Update system packages regularly
- Backup database regularly

### Performance Monitoring
```bash
# Check system resources
htop
df -h
free -h

# Monitor service performance
./manage_services.sh status
```

### Backup Strategy
```bash
# Create regular backups
mkdir -p /root/backups/$(date +%Y%m%d)
pg_dump -h localhost -U mobile_automation_app mobile_automation_saas > /root/backups/$(date +%Y%m%d)/database.sql
tar -czf /root/backups/$(date +%Y%m%d)/codebase.tar.gz /root/MobileApp-AutoTest
```

## Contact Information

For technical support or issues with this deployment:
1. Check the troubleshooting section above
2. Review log files for specific error messages
3. Verify all prerequisites are met
4. Ensure network connectivity and firewall settings

---

**Deployment Script**: `deploy_authentication_fix.sh`
**Last Updated**: 2025-01-03
**Version**: 1.0
