#!/bin/bash

# Quick deployment script for updating the SaaS application
SERVER="*************"
USER="root"
REMOTE_PATH="/opt/mobile-automation-saas"

echo "Deploying updates to Mobile Automation SaaS..."

# Copy the updated files
echo "Copying saas_app.py..."
scp saas_infrastructure/app/saas_app.py ${USER}@${SERVER}:${REMOTE_PATH}/saas_infrastructure/app/

echo "Copying login.html..."
scp saas_infrastructure/app/templates/login.html ${USER}@${SERVER}:${REMOTE_PATH}/saas_infrastructure/app/templates/

# Restart the application
echo "Restarting application..."
ssh ${USER}@${SERVER} "supervisorctl restart mobile-automation-saas"

echo "Deployment complete!"
echo "Testing application..."
sleep 3

# Test the application
curl -s http://*************:5000/api/tenant/info -H "X-Tenant-Subdomain: testcompany1" | jq .

echo "Application is running!"
