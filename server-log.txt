chmod +x ./kill_and_restart_services.sh && ./kill_and_restart_services.sh
🔧 Killing existing processes and restarting services...
==================================================
🧹 Cleaning up existing processes...
🐍 Killing related Python processes...
✅ Python processes cleaned up
🔍 Checking port 5000...
Found processes on port 5000: 8523
Killing process 8523...
✅ Cleaned up port 5000
🔍 Checking port 8080...
✅ Port 8080 is free
🔍 Checking port 8081...
✅ Port 8081 is free
🔍 Verifying ports are free...
❌ Port 5000 is still in use!
COMMAND  PID     USER   FD   TYPE DEVICE SIZE/OFF NODE NAME
python  8848 www-data    5u  IPv4  41112      0t0  TCP *:5000 (LISTEN)
✅ Port 8080 is free
✅ Port 8081 is free
==================================================
🚀 Ready to start services! Run one of these:
   python3 quick_fix_services.py
   python3 manual_service_startup.py
==================================================
python quick_fix_services.py
🔧 Quick Fix for SaaS Platform Services
==================================================
Loading environment from .env
Set DATABASE_URL: postgresql://mobile_automation_app:***@localhost:5432/mobile_automation_saas
Killing existing processes...
Killed process 8848 on port 5000
Creating corrected startup scripts...
Created start_main_saas.py
Created start_ios_service.py
Created start_android_service.py
Starting services sequentially...
Starting Main SaaS App...
❌ Main SaaS App failed to start
STDOUT: Creating SaaS application...
Starting SaaS app on port 5000
 * Serving Flask app 'saas_infrastructure.app.saas_app'
 * Debug mode: off

STDERR: INFO:saas_infrastructure.app.saas_app:Using DATABASE_URL: postgresql://mobile_automation_app:secure_password...
INFO:saas_infrastructure.app.dashboard_integration:Setting up dashboard blueprints...
INFO:saas_infrastructure.app.dashboard_integration:iOS dashboard blueprint created
INFO:saas_infrastructure.app.dashboard_integration:Android dashboard blueprint created
INFO:saas_infrastructure.app.dashboard_integration:Dashboard routes setup completed
INFO:saas_infrastructure.app.dashboard_integration:iOS dashboard blueprint registered
INFO:saas_infrastructure.app.dashboard_integration:Android dashboard blueprint registered
INFO:saas_infrastructure.app.dashboard_integration:Dashboard blueprints setup completed successfully
INFO:saas_infrastructure.app.saas_app:Dashboard integration setup completed
INFO:saas_infrastructure.app.saas_app:Template adapter setup completed
INFO:saas_infrastructure.app.saas_app:Device manager setup completed
WARNING:werkzeug:WebSocket transport not available. Install simple-websocket for improved performance.
Address already in use
Port 5000 is in use by another program. Either identify and stop that program, or start the server with a different port.

Starting iOS Service...
❌ iOS Service failed to start
STDOUT: Creating iOS automation service...
Error starting iOS service: No module named 'websockets'

STDERR: INFO:saas_infrastructure.services.ios_automation_service:Database connection established
INFO:utils.global_values_db:Using global values database at: /home/<USER>/MobileApp-AutoTest/app/data/global_values.db
INFO:utils.global_values_db:Global values database initialized successfully
INFO:utils.directory_paths_db:Directory paths and environments database initialized/verified
INFO:utils.directory_paths_db:Directory paths and environments database initialized/verified
INFO:config:Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
INFO:config:Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
INFO:config:Using database path for SCREENSHOTS: /home/<USER>/MobileApp-AutoTest/screenshots
INFO:config:Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
INFO:config:Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
INFO:config:Using database path for RESULTS: /home/<USER>/MobileApp-AutoTest/reports/suites
INFO:config:Using database path for RECORDINGS: /home/<USER>/MobileApp-AutoTest/recordings
INFO:config:Using database path for TEMP_FILES: /home/<USER>/MobileApp-AutoTest/temp
INFO:config:Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
INFO:utils.global_values_db:Using global values from config.py
INFO:utils.global_values_db:Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
INFO:utils.healenium_config:Loaded Healenium configuration: enabled=True
WARNING:appium_device_controller:TouchAction not available in this Appium Python Client version - using W3C Actions fallback
INFO:AppiumDeviceController:Successfully imported Airtest library.
INFO:utils.database:=== UPDATING TEST_STEPS TABLE SCHEMA ===
INFO:utils.database:Test_steps table schema updated successfully
INFO:utils.database:=== UPDATING SCREENSHOTS TABLE SCHEMA ===
INFO:utils.database:Screenshots table schema updated successfully
INFO:utils.database:=== UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
INFO:utils.database:step_idx column already exists in execution_tracking table
INFO:utils.database:action_type column already exists in execution_tracking table
INFO:utils.database:action_params column already exists in execution_tracking table
INFO:utils.database:action_id column already exists in execution_tracking table
INFO:utils.database:Successfully updated execution_tracking table schema
INFO:utils.database:Database initialized successfully
INFO:utils.database:Checking initial database state...
INFO:utils.database:Database state: 0 suites, 0 cases, 12750 steps, 0 screenshots, 0 tracking entries
INFO:app:Using directories from config.py:
INFO:app:  - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
INFO:app:  - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
INFO:app:  - SCREENSHOTS_DIR: /home/<USER>/MobileApp-AutoTest/screenshots
[2025-08-03 10:21:16,046] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-08-03 10:21:16,049] WARNING in appium_device_controller: Appium server check failed: HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x70433fcb8320>: Failed to establish a new connection: [Errno 111] Connection refused'))
[2025-08-03 10:21:16,049] INFO in appium_device_controller: Checking for existing Appium and iproxy processes...
[2025-08-03 10:21:16,057] INFO in appium_device_controller: Attempted to kill Appium processes
[2025-08-03 10:21:16,066] INFO in appium_device_controller: Attempted to kill iproxy processes (default ports only)
[2025-08-03 10:21:18,068] INFO in appium_device_controller: No Appium server detected. Starting a new one...
[2025-08-03 10:21:18,068] INFO in appium_device_controller: Local Appium not found, using global installation
[2025-08-03 10:21:18,069] ERROR in appium_device_controller: Error checking/installing global drivers: [Errno 2] No such file or directory: 'appium'
[2025-08-03 10:21:18,069] INFO in appium_device_controller: Enabling inspector plugin if available
[2025-08-03 10:21:18,069] INFO in appium_device_controller: Appium server output will be logged to: /home/<USER>/MobileApp-AutoTest/appium_server.log
[2025-08-03 10:21:18,070] ERROR in appium_device_controller: Error ensuring Appium server: [Errno 2] No such file or directory: 'appium'
[2025-08-03 10:21:18,071] ERROR in appium_device_controller: Traceback (most recent call last):
  File "/home/<USER>/MobileApp-AutoTest/app/utils/appium_device_controller.py", line 607, in _ensure_appium_server
    appium_process = subprocess.Popen(
                     ^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/subprocess.py", line 1026, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "/usr/lib/python3.12/subprocess.py", line 1955, in _execute_child
    raise child_exception_type(errno_num, err_msg, err_filename)
FileNotFoundError: [Errno 2] No such file or directory: 'appium'

Traceback (most recent call last):
  File "/home/<USER>/MobileApp-AutoTest/start_ios_service.py", line 14, in <module>
    service = create_ios_automation_service()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/MobileApp-AutoTest/saas_infrastructure/services/ios_automation_service.py", line 372, in create_ios_automation_service
    return iOSAutomationService(config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/MobileApp-AutoTest/saas_infrastructure/services/ios_automation_service.py", line 34, in __init__
    self.setup_device_bridge_integration()
  File "/home/<USER>/MobileApp-AutoTest/saas_infrastructure/services/ios_automation_service.py", line 188, in setup_device_bridge_integration
    from bridge_client import create_bridge_client
  File "/home/<USER>/MobileApp-AutoTest/saas_infrastructure/services/bridge_client.py", line 10, in <module>
    import websockets
ModuleNotFoundError: No module named 'websockets'

Starting Android Service...
❌ Android Service failed to start
STDOUT: Creating Android automation service...
Error starting Android service: No module named 'websockets'

STDERR: INFO:saas_infrastructure.services.android_automation_service:Database connection established
WARNING:appium_device_controller:TouchAction not available in this Appium Python Client version - using W3C Actions fallback
INFO:AppiumDeviceController:Successfully imported Airtest library.
INFO:utils.directory_paths_db:Directory paths and environments database initialized/verified
INFO:utils.directory_paths_db:Directory paths and environments database initialized/verified
INFO:config:Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
INFO:config:Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
INFO:config:Using database path for SCREENSHOTS: /home/<USER>/MobileApp-AutoTest/screenshots
INFO:config:Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
INFO:config:Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
INFO:config:Using database path for RESULTS: /home/<USER>/MobileApp-AutoTest/reports/suites
INFO:config:Using database path for RECORDINGS: /home/<USER>/MobileApp-AutoTest/recordings
INFO:config:Using database path for TEMP_FILES: /home/<USER>/MobileApp-AutoTest/temp
INFO:config:Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
INFO:utils.database:=== UPDATING TEST_STEPS TABLE SCHEMA ===
INFO:utils.database:Test_steps table schema updated successfully
INFO:utils.database:=== UPDATING SCREENSHOTS TABLE SCHEMA ===
INFO:utils.database:Screenshots table schema updated successfully
INFO:utils.database:=== UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
INFO:utils.database:step_idx column already exists in execution_tracking table
INFO:utils.database:action_type column already exists in execution_tracking table
INFO:utils.database:action_params column already exists in execution_tracking table
INFO:utils.database:action_id column already exists in execution_tracking table
INFO:utils.database:Successfully updated execution_tracking table schema
INFO:utils.database:Database initialized successfully
INFO:utils.database:Checking initial database state...
INFO:utils.database:Database state: 0 suites, 0 cases, 12750 steps, 0 screenshots, 0 tracking entries
INFO:app_android.utils.directory_paths_db:Directory paths and environments database initialized/verified
INFO:app:Using directories from config.py:
INFO:app:  - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
INFO:app:  - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
INFO:app:  - SCREENSHOTS_DIR: /home/<USER>/MobileApp-AutoTest/screenshots
[2025-08-03 10:21:24,004] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-08-03 10:21:24,004] INFO in database: Test_steps table schema updated successfully
[2025-08-03 10:21:24,004] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-08-03 10:21:24,005] INFO in database: Screenshots table schema updated successfully
[2025-08-03 10:21:24,005] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-08-03 10:21:24,005] INFO in database: step_idx column already exists in execution_tracking table
[2025-08-03 10:21:24,005] INFO in database: action_type column already exists in execution_tracking table
[2025-08-03 10:21:24,005] INFO in database: action_params column already exists in execution_tracking table
[2025-08-03 10:21:24,005] INFO in database: action_id column already exists in execution_tracking table
[2025-08-03 10:21:24,005] INFO in database: Successfully updated execution_tracking table schema
[2025-08-03 10:21:24,005] INFO in database: Database initialized successfully
[2025-08-03 10:21:24,005] INFO in database: Checking initial database state...
[2025-08-03 10:21:24,007] INFO in database: Database state: 0 suites, 0 cases, 12750 steps, 0 screenshots, 0 tracking entries
[2025-08-03 10:21:24,012] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-08-03 10:21:24,013] INFO in database: Test_steps table schema updated successfully
[2025-08-03 10:21:24,013] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-08-03 10:21:24,013] INFO in database: Screenshots table schema updated successfully
[2025-08-03 10:21:24,013] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-08-03 10:21:24,013] INFO in database: step_idx column already exists in execution_tracking table
[2025-08-03 10:21:24,013] INFO in database: action_type column already exists in execution_tracking table
[2025-08-03 10:21:24,013] INFO in database: action_params column already exists in execution_tracking table
[2025-08-03 10:21:24,013] INFO in database: action_id column already exists in execution_tracking table
[2025-08-03 10:21:24,014] INFO in database: Successfully updated execution_tracking table schema
[2025-08-03 10:21:24,014] INFO in database: Database initialized successfully
[2025-08-03 10:21:24,014] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-08-03 10:21:24,014] INFO in database: step_idx column already exists in execution_tracking table
[2025-08-03 10:21:24,014] INFO in database: action_type column already exists in execution_tracking table
[2025-08-03 10:21:24,014] INFO in database: action_params column already exists in execution_tracking table
[2025-08-03 10:21:24,014] INFO in database: action_id column already exists in execution_tracking table
[2025-08-03 10:21:24,014] INFO in database: Successfully updated execution_tracking table schema
[2025-08-03 10:21:24,014] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-08-03 10:21:24,014] INFO in database: Screenshots table schema updated successfully
[2025-08-03 10:21:24,014] INFO in database: === CLEARING EXECUTION TRACKING TABLE ===
[2025-08-03 10:21:24,015] INFO in database: Found 0 records in execution_tracking table before clearing
[2025-08-03 10:21:24,018] INFO in database: Successfully cleared execution_tracking table. Removed 0 records.
[2025-08-03 10:21:24,041] INFO in global_values_db: Using global values database at: /home/<USER>/MobileApp-AutoTest/app/data/global_values.db
[2025-08-03 10:21:24,042] INFO in global_values_db: Global values database initialized successfully
[2025-08-03 10:21:24,042] INFO in global_values_db: Using global values from config.py
[2025-08-03 10:21:24,042] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 3, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
[2025-08-03 10:21:24,062] INFO in global_values_db: Using global values database at: /home/<USER>/MobileApp-AutoTest/app/data/global_values.db
[2025-08-03 10:21:24,062] INFO in global_values_db: Global values database initialized successfully
[2025-08-03 10:21:24,062] INFO in global_values_db: Using global values from config.py
[2025-08-03 10:21:24,062] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
[2025-08-03 10:21:24,063] INFO in healenium_config: Loaded Healenium configuration: enabled=True
[2025-08-03 10:21:24,063] WARNING in appium_device_controller: TouchAction not available in this Appium Python Client version - using W3C Actions fallback
[2025-08-03 10:21:24,063] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-08-03 10:21:24,066] WARNING in appium_device_controller: Appium server check failed: HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x76eb88482660>: Failed to establish a new connection: [Errno 111] Connection refused'))
[2025-08-03 10:21:24,066] INFO in appium_device_controller: Checking for existing Appium and iproxy processes...
[2025-08-03 10:21:24,075] INFO in appium_device_controller: Attempted to kill Appium processes
[2025-08-03 10:21:24,084] INFO in appium_device_controller: Attempted to kill iproxy processes (default ports only)
[2025-08-03 10:21:26,086] INFO in appium_device_controller: No Appium server detected. Starting a new one...
[2025-08-03 10:21:26,086] INFO in appium_device_controller: Local Appium not found, using global installation
[2025-08-03 10:21:26,087] ERROR in appium_device_controller: Error checking/installing global drivers: [Errno 2] No such file or directory: 'appium'
[2025-08-03 10:21:26,087] INFO in appium_device_controller: Enabling inspector plugin if available
[2025-08-03 10:21:26,087] INFO in appium_device_controller: Appium server output will be logged to: /home/<USER>/MobileApp-AutoTest/appium_server.log
[2025-08-03 10:21:26,087] ERROR in appium_device_controller: Error ensuring Appium server: [Errno 2] No such file or directory: 'appium'
[2025-08-03 10:21:26,089] ERROR in appium_device_controller: Traceback (most recent call last):
  File "/home/<USER>/MobileApp-AutoTest/app/utils/appium_device_controller.py", line 607, in _ensure_appium_server
    appium_process = subprocess.Popen(
                     ^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/subprocess.py", line 1026, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "/usr/lib/python3.12/subprocess.py", line 1955, in _execute_child
    raise child_exception_type(errno_num, err_msg, err_filename)
FileNotFoundError: [Errno 2] No such file or directory: 'appium'

Traceback (most recent call last):
  File "/home/<USER>/MobileApp-AutoTest/start_android_service.py", line 14, in <module>
    service = create_android_automation_service()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/MobileApp-AutoTest/saas_infrastructure/services/android_automation_service.py", line 372, in create_android_automation_service
    return AndroidAutomationService(config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/MobileApp-AutoTest/saas_infrastructure/services/android_automation_service.py", line 34, in __init__
    self.setup_device_bridge_integration()
  File "/home/<USER>/MobileApp-AutoTest/saas_infrastructure/services/android_automation_service.py", line 188, in setup_device_bridge_integration
    from bridge_client import create_bridge_client
  File "/home/<USER>/MobileApp-AutoTest/saas_infrastructure/services/bridge_client.py", line 10, in <module>
    import websockets
ModuleNotFoundError: No module named 'websockets'

Testing service health...
✅ Main SaaS is healthy
❌ iOS Service is not responding: HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7589de56f380>: Failed to establish a new connection: [Errno 111] Connection refused'))
❌ Android Service is not responding: HTTPConnectionPool(host='localhost', port=8081): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7589de56fb60>: Failed to establish a new connection: [Errno 111] Connection refused'))
==================================================
❌ Only 0/3 services started successfully