<div class="container-fluid mt-3">
    <!-- Database Tools Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="bi bi-database"></i> Database Tools</h4>
                    <small>Browse and manage SQLite database files</small>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            <label for="databaseSelect" class="form-label fw-bold">Select Database:</label>
                        </div>
                        <div class="col-md-6">
                            <select class="form-select" id="databaseSelect">
                                <option selected disabled>Scanning for databases...</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-success" id="navigateDatabaseBtn" disabled>
                                <i class="bi bi-box-arrow-up-right"></i> Navigate Database
                            </button>
                            <button class="btn btn-secondary" id="refreshDatabasesBtn">
                                <i class="bi bi-arrow-repeat"></i> Refresh
                            </button>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i> 
                            This tool uses sqlite-web to provide a browser-based interface for viewing and editing SQLite databases. 
                            Click "Navigate Database" to open the selected database in a new browser tab.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Compare Tools Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0"><i class="bi bi-images"></i> Image Compare</h4>
                    <small>Compare baseline and new images for visual regression testing</small>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="baselineImagesPath" class="form-label fw-bold">Baseline Images Folder:</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="baselineImagesPath" placeholder="Enter baseline images folder path">
                                <button class="btn btn-outline-secondary" type="button" id="validateBaselineBtn">
                                    <i class="bi bi-check-circle"></i> Validate
                                </button>
                            </div>
                            <div id="baselineValidationResult" class="mt-2"></div>
                        </div>
                        <div class="col-md-6">
                            <label for="newImagesPath" class="form-label fw-bold">New Images Folder:</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="newImagesPath" placeholder="Enter new images folder path">
                                <button class="btn btn-outline-secondary" type="button" id="validateNewBtn">
                                    <i class="bi bi-check-circle"></i> Validate
                                </button>
                            </div>
                            <div id="newValidationResult" class="mt-2"></div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <button class="btn btn-primary btn-lg me-3" id="compareImagesBtn" disabled>
                                <i class="bi bi-arrow-left-right"></i> Compare Images
                            </button>
                            <button class="btn btn-success btn-lg" id="exportComparisonBtn" disabled>
                                <i class="bi bi-download"></i> Export
                            </button>
                            <div class="mt-3">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle"></i> 
                                    This tool compares images from baseline and new folders and generates an HTML report showing differences. 
                                    Both folders must exist and contain images to proceed with comparison.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Locators Repository Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0"><i class="bi bi-search"></i> Locators Repository</h4>
                    <small>View and analyze all locators used across test cases</small>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <p class="mb-0">
                                <i class="bi bi-info-circle"></i>
                                This tool scans all test case files and generates a comprehensive report of all locators used in your automation tests.
                                The report includes locator types, values, platforms, and usage statistics.
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <button class="btn btn-info btn-lg" id="showLocatorsBtn">
                                <i class="bi bi-list-ul"></i> Show Locators Used
                            </button>
                        </div>
                    </div>
                    <div class="mt-3" id="locatorsStatus" style="display: none;">
                        <div class="alert alert-info">
                            <i class="bi bi-hourglass-split"></i>
                            <span id="locatorsStatusText">Scanning test cases and generating report...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tools JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function () {
    const databaseSelect = document.getElementById('databaseSelect');
    const navigateDatabaseBtn = document.getElementById('navigateDatabaseBtn');
    const refreshDatabasesBtn = document.getElementById('refreshDatabasesBtn');
    const baselineImagesPath = document.getElementById('baselineImagesPath');
    const newImagesPath = document.getElementById('newImagesPath');
    const validateBaselineBtn = document.getElementById('validateBaselineBtn');
    const validateNewBtn = document.getElementById('validateNewBtn');
    const compareImagesBtn = document.getElementById('compareImagesBtn');
    const exportComparisonBtn = document.getElementById('exportComparisonBtn');
    const baselineValidationResult = document.getElementById('baselineValidationResult');
    const newValidationResult = document.getElementById('newValidationResult');
    const showLocatorsBtn = document.getElementById('showLocatorsBtn');
    const locatorsStatus = document.getElementById('locatorsStatus');
    const locatorsStatusText = document.getElementById('locatorsStatusText');

    let baselineValid = false;
    let newValid = false;

    // --- Toast Helper ---
    function showToast(message, type = 'info') {
        if (window.showToast) {
            window.showToast(message, type);
        } else {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `${message} <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>`;
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 5000);
        }
    }

    // --- API Helper ---
    async function apiCall(url, method = 'GET', body = null) {
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
            },
        };
        if (body) {
            options.body = JSON.stringify(body);
        }
        try {
            const response = await fetch(url, options);
            const responseData = await response.json(); 
            if (!response.ok) {
                const errorMsg = responseData.error || `API Error: ${response.status}`;
                showToast(errorMsg, 'error');
                console.error('API Error:', response.status, responseData);
                return { success: false, data: responseData, status: response.status }; 
            }
            return { success: true, data: responseData, status: response.status };
        } catch (error) {
            showToast('Network or server error: ' + error.message, 'error');
            console.error('Fetch Error:', error);
            return { success: false, error: error.message, data: { error: error.message} };
        }
    }

    // --- Database Tools ---
    async function scanDatabases() {
        databaseSelect.innerHTML = '<option disabled>Scanning for databases...</option>';
        navigateDatabaseBtn.disabled = true;

        const result = await apiCall('/api/tools/scan-databases');
        if (result.success && result.data.databases) {
            databaseSelect.innerHTML = '<option value="" disabled selected>Select a database</option>';
            result.data.databases.forEach(db => {
                const option = document.createElement('option');
                option.value = db.path;
                option.textContent = `${db.name} (${db.size})`;
                databaseSelect.appendChild(option);
            });
            
            if (result.data.databases.length === 0) {
                databaseSelect.innerHTML = '<option disabled>No databases found</option>';
            }
        } else {
            databaseSelect.innerHTML = '<option disabled>Error scanning databases</option>';
        }
    }

    databaseSelect.addEventListener('change', function() {
        navigateDatabaseBtn.disabled = !this.value;
    });

    // Prevent multiple event listeners
    navigateDatabaseBtn.removeEventListener('click', navigateDatabaseBtn._clickHandler);

    navigateDatabaseBtn._clickHandler = async function() {
        const selectedDb = databaseSelect.value;
        if (!selectedDb) return;

        // Prevent multiple clicks
        if (navigateDatabaseBtn.disabled) return;
        navigateDatabaseBtn.disabled = true;

        try {
            const result = await apiCall('/api/tools/open-database', 'POST', { database_path: selectedDb });
            if (result.success && result.data.url) {
                window.open(result.data.url, '_blank');
                showToast('Database browser opened in new tab', 'success');
            } else {
                showToast('Failed to open database: ' + (result.data?.error || 'Unknown error'), 'error');
            }
        } catch (error) {
            showToast('Error opening database: ' + error.message, 'error');
        } finally {
            // Re-enable button after a short delay
            setTimeout(() => {
                navigateDatabaseBtn.disabled = !databaseSelect.value;
            }, 1000);
        }
    };

    navigateDatabaseBtn.addEventListener('click', navigateDatabaseBtn._clickHandler);

    refreshDatabasesBtn.addEventListener('click', scanDatabases);

    // --- Image Compare Tools ---
    async function validateFolder(path, resultElement) {
        if (!path.trim()) {
            resultElement.innerHTML = '<small class="text-muted">Enter a folder path</small>';
            return false;
        }

        const result = await apiCall('/api/tools/validate-folder', 'POST', { folder_path: path.trim() });
        if (result.success) {
            if (result.data.exists) {
                resultElement.innerHTML = `<small class="text-success"><i class="bi bi-check-circle"></i> Valid folder (${result.data.file_count} files)</small>`;
                return true;
            } else {
                resultElement.innerHTML = '<small class="text-danger"><i class="bi bi-x-circle"></i> Folder does not exist</small>';
                return false;
            }
        } else {
            resultElement.innerHTML = '<small class="text-danger"><i class="bi bi-exclamation-triangle"></i> Error validating folder</small>';
            return false;
        }
    }

    validateBaselineBtn.addEventListener('click', async function() {
        baselineValid = await validateFolder(baselineImagesPath.value, baselineValidationResult);
        updateCompareButton();
    });

    validateNewBtn.addEventListener('click', async function() {
        newValid = await validateFolder(newImagesPath.value, newValidationResult);
        updateCompareButton();
    });

    function updateCompareButton() {
        compareImagesBtn.disabled = !(baselineValid && newValid);
        exportComparisonBtn.disabled = !(baselineValid && newValid);
    }

    compareImagesBtn.addEventListener('click', async function() {
        if (!baselineValid || !newValid) return;

        // Show loading state
        const originalText = compareImagesBtn.innerHTML;
        compareImagesBtn.disabled = true;
        compareImagesBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Processing...';

        showToast('Starting image comparison...', 'info');

        try {
            const result = await apiCall('/api/tools/compare-images', 'POST', {
                baseline_folder: baselineImagesPath.value.trim(),
                new_folder: newImagesPath.value.trim()
            });

            if (result.success && result.data.report_url) {
                window.open(result.data.report_url, '_blank');
                showToast('Image comparison report opened in new tab', 'success');
            } else {
                showToast('Failed to generate comparison report: ' + (result.data?.error || 'Unknown error'), 'error');
            }
        } catch (error) {
            showToast('Error during image comparison: ' + error.message, 'error');
        } finally {
            // Restore button state
            compareImagesBtn.disabled = false;
            compareImagesBtn.innerHTML = originalText;
        }
    });

    exportComparisonBtn.addEventListener('click', async function() {
        if (!baselineValid || !newValid) return;

        // Show loading state
        const originalText = exportComparisonBtn.innerHTML;
        exportComparisonBtn.disabled = true;
        exportComparisonBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Exporting...';

        showToast('Preparing export package...', 'info');

        try {
            const result = await apiCall('/api/tools/export-comparison', 'POST', {
                baseline_folder: baselineImagesPath.value.trim(),
                new_folder: newImagesPath.value.trim()
            });

            if (result.success && result.data.download_url) {
                // Create a temporary link to download the file
                const link = document.createElement('a');
                link.href = result.data.download_url;
                link.download = result.data.filename || 'image_comparison_export.zip';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                showToast('Export package downloaded successfully', 'success');
            } else {
                showToast('Failed to create export package: ' + (result.data?.error || 'Unknown error'), 'error');
            }
        } catch (error) {
            showToast('Error during export: ' + error.message, 'error');
        } finally {
            // Restore button state
            exportComparisonBtn.disabled = false;
            exportComparisonBtn.innerHTML = originalText;
        }
    });

    // Locators Repository functionality
    showLocatorsBtn.addEventListener('click', async function() {
        const originalText = showLocatorsBtn.innerHTML;

        try {
            // Show status
            locatorsStatus.style.display = 'block';
            locatorsStatusText.textContent = 'Scanning test cases and generating report...';

            // Disable button
            showLocatorsBtn.disabled = true;
            showLocatorsBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Generating...';

            const result = await apiCall('/api/tools/generate-locators-report', 'POST');

            if (result.success && result.data.url) {
                window.open(result.data.url, '_blank');
                showToast('Locators report opened in new tab', 'success');
                locatorsStatusText.textContent = `Report generated successfully! Found ${result.data.total_locators || 0} locators across ${result.data.total_test_cases || 0} test cases.`;
            } else {
                showToast('Failed to generate locators report: ' + (result.data?.error || 'Unknown error'), 'error');
                locatorsStatus.style.display = 'none';
            }
        } catch (error) {
            showToast('Error generating locators report: ' + error.message, 'error');
            locatorsStatus.style.display = 'none';
        } finally {
            // Restore button state
            showLocatorsBtn.disabled = false;
            showLocatorsBtn.innerHTML = originalText;
        }
    });

    // Initialize
    scanDatabases();
});
</script>
