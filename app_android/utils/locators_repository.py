import os
import json
import sqlite3
import logging
from datetime import datetime
from pathlib import Path
from .database import get_db_path
from .directory_utils import get_test_cases_directory

logger = logging.getLogger(__name__)

class LocatorsRepository:
    """
    Utility class for managing locators repository functionality.
    Scans test case JSON files and extracts locator information.
    """
    
    def __init__(self):
        self.db_path = get_db_path()
        
    def get_platform_from_test_case(self, test_case_data):
        """
        Determine platform from test case data.
        
        Args:
            test_case_data: Dictionary containing test case data
            
        Returns:
            str: Platform name ('iOS', 'Android', or 'Both')
        """
        # Check for platform-specific action types
        ios_actions = ['iosFunctions']
        android_actions = ['androidFunctions']
        
        has_ios = False
        has_android = False
        
        for action in test_case_data.get('actions', []):
            action_type = action.get('type', '')
            if action_type in ios_actions:
                has_ios = True
            elif action_type in android_actions:
                has_android = True
                
        if has_ios and has_android:
            return 'Both'
        elif has_ios:
            return 'iOS'
        elif has_android:
            return 'Android'
        else:
            # Default to Both if no platform-specific actions found
            return 'Both'
    
    def extract_locators_from_action(self, action, test_case_name, test_case_id, platform):
        """
        Extract locator information from a single action.
        
        Args:
            action: Dictionary containing action data
            test_case_name: Name of the test case
            test_case_id: UUID of the test case
            platform: Platform name
            
        Returns:
            list: List of locator dictionaries
        """
        locators = []
        action_id = action.get('action_id', '')
        
        # Action types that can contain locators
        locator_action_types = [
            'tap', 'doubleTap', 'clickElement', 'waitTill', 'getValue', 
            'compareValue', 'ifThenSteps', 'conditionalTap'
        ]
        
        action_type = action.get('type', '')
        
        if action_type not in locator_action_types:
            return locators
            
        # Extract primary locator
        locator_type = action.get('locator_type')
        locator_value = action.get('locator_value')
        
        if locator_type and locator_value:
            locators.append({
                'test_case_name': test_case_name,
                'test_case_id': test_case_id,
                'action_id': action_id,
                'locator_type': locator_type,
                'locator_value': locator_value,
                'platform': platform
            })
        
        # Extract fallback locators
        fallback_locators = action.get('fallback_locators', [])
        for fallback in fallback_locators:
            fallback_type = fallback.get('locator_type')
            fallback_value = fallback.get('locator_value')
            
            if fallback_type and fallback_value:
                locators.append({
                    'test_case_name': test_case_name,
                    'test_case_id': test_case_id,
                    'action_id': action_id,
                    'locator_type': fallback_type,
                    'locator_value': fallback_value,
                    'platform': platform
                })
        
        # Extract locators from If-Then conditions
        if action_type == 'ifThenSteps':
            # Check if condition
            if_condition = action.get('if_condition', {})
            condition_locator_type = if_condition.get('locator_type')
            condition_locator_value = if_condition.get('locator_value')
            
            if condition_locator_type and condition_locator_value:
                locators.append({
                    'test_case_name': test_case_name,
                    'test_case_id': test_case_id,
                    'action_id': action_id,
                    'locator_type': condition_locator_type,
                    'locator_value': condition_locator_value,
                    'platform': platform
                })
            
            # Check then action
            then_action = action.get('then_action', {})
            then_locator_type = then_action.get('locator_type')
            then_locator_value = then_action.get('locator_value')
            
            if then_locator_type and then_locator_value:
                locators.append({
                    'test_case_name': test_case_name,
                    'test_case_id': test_case_id,
                    'action_id': action_id,
                    'locator_type': then_locator_type,
                    'locator_value': then_locator_value,
                    'platform': platform
                })
        
        return locators
    
    def scan_test_case_file(self, file_path):
        """
        Scan a single test case JSON file and extract locators.
        
        Args:
            file_path: Path to the test case JSON file
            
        Returns:
            list: List of locator dictionaries
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                test_case_data = json.load(f)
            
            # Get test case information
            test_case_name = os.path.splitext(os.path.basename(file_path))[0]
            test_case_id = test_case_data.get('test_case_id')  # UUID if available
            platform = self.get_platform_from_test_case(test_case_data)
            
            all_locators = []
            
            # Extract locators from all actions
            for action in test_case_data.get('actions', []):
                locators = self.extract_locators_from_action(
                    action, test_case_name, test_case_id, platform
                )
                all_locators.extend(locators)
            
            return all_locators
            
        except Exception as e:
            logger.error(f"Error scanning test case file {file_path}: {str(e)}")
            return []
    
    def scan_all_test_cases(self):
        """
        Scan all test case JSON files in the configured directory.
        
        Returns:
            list: List of all locator dictionaries
        """
        test_cases_dir = get_test_cases_directory()
        all_locators = []
        
        if not os.path.exists(test_cases_dir):
            logger.warning(f"Test cases directory does not exist: {test_cases_dir}")
            return all_locators
        
        # Scan all JSON files in the directory
        for filename in os.listdir(test_cases_dir):
            if filename.endswith('.json') and not filename.endswith('.bak'):
                file_path = os.path.join(test_cases_dir, filename)
                locators = self.scan_test_case_file(file_path)
                all_locators.extend(locators)
        
        logger.info(f"Scanned {len(all_locators)} locators from test cases directory")
        return all_locators
    
    def upsert_locator(self, locator_data):
        """
        Insert or update a locator in the database.
        
        Args:
            locator_data: Dictionary containing locator information
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            current_time = datetime.now().isoformat()
            
            # Check if locator already exists
            cursor.execute('''
                SELECT id FROM locators_repository 
                WHERE test_case_name = ? AND action_id = ? AND locator_type = ? AND locator_value = ?
            ''', (
                locator_data['test_case_name'],
                locator_data['action_id'],
                locator_data['locator_type'],
                locator_data['locator_value']
            ))
            
            existing = cursor.fetchone()
            
            if existing:
                # Update existing record
                cursor.execute('''
                    UPDATE locators_repository 
                    SET test_case_id = ?, platform = ?, last_used_date = ?
                    WHERE id = ?
                ''', (
                    locator_data.get('test_case_id'),
                    locator_data['platform'],
                    current_time,
                    existing[0]
                ))
            else:
                # Insert new record
                cursor.execute('''
                    INSERT INTO locators_repository 
                    (test_case_name, test_case_id, action_id, locator_type, locator_value, platform, created_date, last_used_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    locator_data['test_case_name'],
                    locator_data.get('test_case_id'),
                    locator_data['action_id'],
                    locator_data['locator_type'],
                    locator_data['locator_value'],
                    locator_data['platform'],
                    current_time,
                    current_time
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error upserting locator: {str(e)}")
            if 'conn' in locals():
                conn.close()
    
    def update_locators_repository(self):
        """
        Scan all test cases and update the locators repository.
        
        Returns:
            dict: Summary of the update operation
        """
        try:
            all_locators = self.scan_all_test_cases()
            
            # Upsert all locators
            for locator in all_locators:
                self.upsert_locator(locator)
            
            # Get summary statistics
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT COUNT(*) FROM locators_repository')
            total_locators = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(DISTINCT test_case_name) FROM locators_repository')
            total_test_cases = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(DISTINCT locator_type) FROM locators_repository')
            total_locator_types = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'success': True,
                'total_locators': total_locators,
                'total_test_cases': total_test_cases,
                'total_locator_types': total_locator_types,
                'scanned_locators': len(all_locators)
            }
            
        except Exception as e:
            logger.error(f"Error updating locators repository: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_all_locators(self):
        """
        Get all locators from the repository.
        
        Returns:
            list: List of locator dictionaries
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT test_case_name, test_case_id, action_id, locator_type, 
                       locator_value, platform, created_date, last_used_date
                FROM locators_repository
                ORDER BY test_case_name, action_id, locator_type
            ''')
            
            rows = cursor.fetchall()
            conn.close()
            
            locators = []
            for row in rows:
                locators.append({
                    'test_case_name': row[0],
                    'test_case_id': row[1],
                    'action_id': row[2],
                    'locator_type': row[3],
                    'locator_value': row[4],
                    'platform': row[5],
                    'created_date': row[6],
                    'last_used_date': row[7]
                })
            
            return locators
            
        except Exception as e:
            logger.error(f"Error getting locators: {str(e)}")
            return []

# Create singleton instance
locators_repository = LocatorsRepository()
