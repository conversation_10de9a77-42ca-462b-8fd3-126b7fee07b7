import os
import glob
import subprocess
import threading
import time
import logging
from pathlib import Path
from flask import Blueprint, request, jsonify
import sqlite3
from utils.locators_report_generator import locators_report_generator

logger = logging.getLogger(__name__)

tools_bp = Blueprint('tools', __name__, url_prefix='/api/tools')

# Global variable to track sqlite-web processes
sqlite_web_processes = {}

@tools_bp.route('/scan-databases', methods=['GET'])
def scan_databases():
    """Scan for SQLite database files in the app directory"""
    try:
        # Get the app directory
        app_dir = Path(__file__).resolve().parent.parent
        
        # Common database file patterns
        db_patterns = [
            '**/*.db',
            '**/*.sqlite',
            '**/*.sqlite3'
        ]
        
        databases = []
        
        for pattern in db_patterns:
            for db_path in app_dir.glob(pattern):
                if db_path.is_file():
                    try:
                        # Get file size
                        size = db_path.stat().st_size
                        size_str = format_file_size(size)
                        
                        # Verify it's actually a SQLite database
                        if is_sqlite_database(str(db_path)):
                            databases.append({
                                'name': db_path.name,
                                'path': str(db_path),
                                'relative_path': str(db_path.relative_to(app_dir)),
                                'size': size_str
                            })
                    except Exception as e:
                        logger.warning(f"Error processing database file {db_path}: {e}")
        
        # Sort by name
        databases.sort(key=lambda x: x['name'])
        
        return jsonify({
            'success': True,
            'databases': databases,
            'count': len(databases)
        })
        
    except Exception as e:
        logger.error(f"Error scanning databases: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@tools_bp.route('/open-database', methods=['POST'])
def open_database():
    """Open a SQLite database using sqlite-web"""
    try:
        data = request.get_json()
        database_path = data.get('database_path')
        
        if not database_path:
            return jsonify({
                'success': False,
                'error': 'Database path is required'
            }), 400
        
        if not os.path.exists(database_path):
            return jsonify({
                'success': False,
                'error': 'Database file does not exist'
            }), 404
        
        # Find an available port starting from 8090
        port = find_available_port(8090)
        if not port:
            return jsonify({
                'success': False,
                'error': 'No available ports found'
            }), 500
        
        # Start sqlite-web process
        try:
            # Kill any existing process for this database
            if database_path in sqlite_web_processes:
                try:
                    sqlite_web_processes[database_path].terminate()
                    sqlite_web_processes[database_path].wait(timeout=5)
                except:
                    pass

            # Find sqlite-web executable - check virtual environment first
            sqlite_web_cmd = 'sqlite_web'  # Note: executable is sqlite_web not sqlite-web

            # Check if we're in a virtual environment
            venv_path = os.environ.get('VIRTUAL_ENV')

            if venv_path:
                venv_sqlite_web = os.path.join(venv_path, 'bin', 'sqlite_web')
                if os.path.exists(venv_sqlite_web):
                    sqlite_web_cmd = venv_sqlite_web
            else:
                # Try to find venv relative to the app directory
                app_dir = Path(__file__).resolve().parent.parent.parent
                venv_sqlite_web = app_dir / 'venv' / 'bin' / 'sqlite_web'
                if venv_sqlite_web.exists():
                    sqlite_web_cmd = str(venv_sqlite_web)

            # Start new sqlite-web process
            cmd = [sqlite_web_cmd, database_path, '--host', '127.0.0.1', '--port', str(port)]
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Store the process
            sqlite_web_processes[database_path] = process
            
            # Give it a moment to start
            time.sleep(2)
            
            # Check if process is still running
            if process.poll() is None:
                url = f"http://127.0.0.1:{port}"
                return jsonify({
                    'success': True,
                    'url': url,
                    'port': port,
                    'database': os.path.basename(database_path)
                })
            else:
                # Process failed to start
                stdout, stderr = process.communicate()
                return jsonify({
                    'success': False,
                    'error': f'Failed to start sqlite-web: {stderr}'
                }), 500
                
        except FileNotFoundError as e:
            logger.error(f"sqlite-web not found: {e}")
            return jsonify({
                'success': False,
                'error': 'sqlite-web is not installed. Please install it using: pip install sqlite-web'
            }), 500
        except Exception as e:
            logger.error(f"Error starting sqlite-web: {e}")
            logger.error(f"Command attempted: {cmd}")
            return jsonify({
                'success': False,
                'error': f'Error starting sqlite-web: {str(e)}'
            }), 500
        
    except Exception as e:
        logger.error(f"Error opening database: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@tools_bp.route('/validate-folder', methods=['POST'])
def validate_folder():
    """Validate if a folder exists and count files in it"""
    try:
        data = request.get_json()
        folder_path = data.get('folder_path')
        
        if not folder_path:
            return jsonify({
                'success': False,
                'error': 'Folder path is required'
            }), 400
        
        folder_path = Path(folder_path)
        
        if not folder_path.exists():
            return jsonify({
                'success': True,
                'exists': False,
                'file_count': 0
            })
        
        if not folder_path.is_dir():
            return jsonify({
                'success': False,
                'error': 'Path exists but is not a directory'
            }), 400
        
        # Count image files
        image_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff']
        file_count = 0
        
        for ext in image_extensions:
            file_count += len(list(folder_path.glob(f'*{ext}')))
            file_count += len(list(folder_path.glob(f'*{ext.upper()}')))
        
        return jsonify({
            'success': True,
            'exists': True,
            'file_count': file_count
        })
        
    except Exception as e:
        logger.error(f"Error validating folder: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@tools_bp.route('/compare-images', methods=['POST'])
def compare_images():
    """Generate image comparison report"""
    try:
        data = request.get_json()
        baseline_folder = data.get('baseline_folder')
        new_folder = data.get('new_folder')
        
        if not baseline_folder or not new_folder:
            return jsonify({
                'success': False,
                'error': 'Both baseline_folder and new_folder are required'
            }), 400
        
        baseline_path = Path(baseline_folder)
        new_path = Path(new_folder)
        
        if not baseline_path.exists() or not baseline_path.is_dir():
            return jsonify({
                'success': False,
                'error': 'Baseline folder does not exist or is not a directory'
            }), 400
        
        if not new_path.exists() or not new_path.is_dir():
            return jsonify({
                'success': False,
                'error': 'New folder does not exist or is not a directory'
            }), 400
        
        # Get the image comparison script path
        app_dir = Path(__file__).resolve().parent.parent.parent
        comparison_script = app_dir / 'image_comparison' / 'generate_html_report.py'
        
        if not comparison_script.exists():
            return jsonify({
                'success': False,
                'error': 'Image comparison script not found'
            }), 500
        
        # Create output directory in temp folder
        from utils.file_utils import get_temp_subdirectory
        output_dir = Path(get_temp_subdirectory('image_comparison')) / 'report'
        output_dir.mkdir(exist_ok=True)
        
        # Run the comparison script
        try:
            cmd = [
                'python',
                str(comparison_script),
                '--baseline', str(baseline_path),
                '--new', str(new_path),
                '--output', str(output_dir)
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            if result.returncode == 0:
                # Find the generated report
                report_file = output_dir / 'report.html'
                if report_file.exists():
                    # Start a simple HTTP server to serve the report
                    port = find_available_port(8100)
                    if port:
                        # Start server in background
                        start_report_server(str(output_dir), port)
                        url = f"http://127.0.0.1:{port}/report.html"
                        
                        return jsonify({
                            'success': True,
                            'report_url': url,
                            'port': port
                        })
                    else:
                        return jsonify({
                            'success': False,
                            'error': 'No available ports for report server'
                        }), 500
                else:
                    return jsonify({
                        'success': False,
                        'error': 'Report file was not generated'
                    }), 500
            else:
                return jsonify({
                    'success': False,
                    'error': f'Image comparison failed: {result.stderr}'
                }), 500
                
        except subprocess.TimeoutExpired:
            return jsonify({
                'success': False,
                'error': 'Image comparison timed out'
            }), 500
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'Error running image comparison: {str(e)}'
            }), 500
        
    except Exception as e:
        logger.error(f"Error comparing images: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# Helper functions
def format_file_size(size_bytes):
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def is_sqlite_database(file_path):
    """Check if a file is a valid SQLite database"""
    try:
        conn = sqlite3.connect(file_path)
        conn.execute("SELECT name FROM sqlite_master WHERE type='table';")
        conn.close()
        return True
    except:
        return False

def find_available_port(start_port):
    """Find an available port starting from start_port"""
    import socket
    
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('127.0.0.1', port))
                return port
        except OSError:
            continue
    return None

def start_report_server(directory, port):
    """Start a simple HTTP server to serve the report"""
    import http.server
    import socketserver
    import threading
    
    def run_server():
        os.chdir(directory)
        handler = http.server.SimpleHTTPRequestHandler
        with socketserver.TCPServer(("127.0.0.1", port), handler) as httpd:
            httpd.serve_forever()
    
    thread = threading.Thread(target=run_server, daemon=True)
    thread.start()

@tools_bp.route('/export-comparison', methods=['POST'])
def export_comparison():
    """Export image comparison results as a zip file"""
    try:
        data = request.get_json()
        baseline_folder = data.get('baseline_folder', '').strip()
        new_folder = data.get('new_folder', '').strip()

        if not baseline_folder or not new_folder:
            return jsonify({
                'success': False,
                'data': {'error': 'Both baseline and new folders are required'}
            }), 400

        # Validate folders exist
        if not os.path.exists(baseline_folder):
            return jsonify({
                'success': False,
                'data': {'error': f'Baseline folder does not exist: {baseline_folder}'}
            }), 400

        if not os.path.exists(new_folder):
            return jsonify({
                'success': False,
                'data': {'error': f'New folder does not exist: {new_folder}'}
            }), 400

        # Get temp directory for output
        from utils.file_utils import get_temp_subdirectory
        output_dir = Path(get_temp_subdirectory('image_comparison')) / 'export'
        output_dir.mkdir(exist_ok=True)

        # Generate comparison report first
        report_dir = Path(get_temp_subdirectory('image_comparison')) / 'report'
        report_dir.mkdir(exist_ok=True)

        # Run image comparison
        baseline_path = Path(baseline_folder)
        new_path = Path(new_folder)

        # Get all image files from both directories
        baseline_images = []
        new_images = []

        for ext in ['*.png', '*.jpg', '*.jpeg']:
            baseline_images.extend(baseline_path.glob(ext))
            baseline_images.extend(baseline_path.glob(ext.upper()))
            new_images.extend(new_path.glob(ext))
            new_images.extend(new_path.glob(ext.upper()))

        if not baseline_images and not new_images:
            return jsonify({
                'success': False,
                'data': {'error': 'No images found in the specified folders'}
            }), 400

        # Create comparison report
        import subprocess
        import sys

        # Use the existing image comparison script
        comparison_script = app_dir / 'image_comparison' / 'generate_html_report.py'
        if comparison_script.exists():
            try:
                result = subprocess.run([
                    sys.executable, str(comparison_script),
                    '--baseline', str(baseline_path),
                    '--new', str(new_path),
                    '--output', str(report_dir)
                ], capture_output=True, text=True, timeout=300)

                if result.returncode != 0:
                    logger.error(f"Image comparison script failed: {result.stderr}")
            except subprocess.TimeoutExpired:
                logger.error("Image comparison script timed out")
            except Exception as e:
                logger.error(f"Error running comparison script: {e}")

        # Create zip file
        import zipfile
        from datetime import datetime

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        zip_filename = f'image_comparison_export_{timestamp}.zip'
        zip_path = output_dir / zip_filename

        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Add HTML report if it exists
            html_report = report_dir / 'comparison_report.html'
            if html_report.exists():
                zipf.write(html_report, 'comparison_report.html')

            # Add all baseline images
            baseline_folder_name = 'baseline_images'
            for img_path in baseline_images:
                arcname = f'{baseline_folder_name}/{img_path.name}'
                zipf.write(img_path, arcname)

            # Add all new images
            new_folder_name = 'new_images'
            for img_path in new_images:
                arcname = f'{new_folder_name}/{img_path.name}'
                zipf.write(img_path, arcname)

            # Add any diff images from the report directory
            diff_images = list(report_dir.glob('*_diff.png'))
            if diff_images:
                for diff_img in diff_images:
                    arcname = f'diff_images/{diff_img.name}'
                    zipf.write(diff_img, arcname)

        # Create download URL
        download_url = f'/api/tools/download-export/{zip_filename}'

        logger.info(f"Export package created: {zip_path}")

        return jsonify({
            'success': True,
            'data': {
                'download_url': download_url,
                'filename': zip_filename,
                'message': f'Export package created with {len(baseline_images)} baseline and {len(new_images)} new images'
            }
        })

    except Exception as e:
        logger.error(f"Error creating export package: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'data': {'error': str(e)}
        }), 500

@tools_bp.route('/download-export/<filename>', methods=['GET'])
def download_export(filename):
    """Download the exported zip file"""
    try:
        from utils.file_utils import get_temp_subdirectory
        export_dir = Path(get_temp_subdirectory('image_comparison')) / 'export'
        file_path = export_dir / filename

        if not file_path.exists():
            return jsonify({'error': 'File not found'}), 404

        return send_file(
            file_path,
            as_attachment=True,
            download_name=filename,
            mimetype='application/zip'
        )

    except Exception as e:
        logger.error(f"Error downloading export file: {str(e)}")
        return jsonify({'error': str(e)}), 500

@tools_bp.route('/generate-locators-report', methods=['POST'])
def generate_locators_report():
    """Generate HTML report for locators repository"""
    try:
        # Generate the report
        result = locators_report_generator.generate_report()

        if not result.get('success'):
            return jsonify({
                'success': False,
                'error': result.get('error', 'Failed to generate report')
            }), 500

        # Start a simple HTTP server to serve the report
        import http.server
        import socketserver
        import threading
        import socket

        # Find an available port
        def find_available_port(start_port=8090):
            for port in range(start_port, start_port + 100):
                try:
                    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                        s.bind(('127.0.0.1', port))
                        return port
                except OSError:
                    continue
            return None

        port = find_available_port()
        if not port:
            return jsonify({
                'success': False,
                'error': 'No available ports found'
            }), 500

        # Create a custom handler that serves our specific file
        class ReportHandler(http.server.SimpleHTTPRequestHandler):
            def __init__(self, *args, **kwargs):
                self.report_file = result['file_path']
                super().__init__(*args, **kwargs)

            def do_GET(self):
                if self.path == '/' or self.path == '/index.html':
                    try:
                        with open(self.report_file, 'rb') as f:
                            content = f.read()

                        self.send_response(200)
                        self.send_header('Content-type', 'text/html')
                        self.send_header('Content-length', len(content))
                        self.end_headers()
                        self.wfile.write(content)
                    except Exception as e:
                        self.send_error(404, f"Report not found: {str(e)}")
                else:
                    self.send_error(404, "File not found")

        # Start the server in a separate thread
        def start_server():
            with socketserver.TCPServer(("127.0.0.1", port), ReportHandler) as httpd:
                httpd.timeout = 300  # 5 minutes timeout
                httpd.serve_forever()

        server_thread = threading.Thread(target=start_server, daemon=True)
        server_thread.start()

        # Give the server a moment to start
        time.sleep(1)

        url = f"http://127.0.0.1:{port}"

        return jsonify({
            'success': True,
            'url': url,
            'port': port,
            'total_locators': result.get('total_locators', 0),
            'total_test_cases': result.get('total_test_cases', 0),
            'total_locator_types': result.get('total_locator_types', 0),
            'scanned_locators': result.get('scanned_locators', 0)
        })

    except Exception as e:
        logger.error(f"Error generating locators report: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
