#!/usr/bin/env python3
"""
Comprehensive Authentication Flow Test for SaaS Platform
Tests the complete end-to-end authentication workflow
"""

import requests
import time
import json
import sys
import subprocess
import signal
import os
from urllib.parse import urlparse, parse_qs

class AuthenticationFlowTester:
    def __init__(self):
        self.base_url = "http://localhost:5000"
        self.ios_url = "http://localhost:8080"
        self.android_url = "http://localhost:8081"
        self.test_tenant = "testcompany1"
        self.test_user = {
            "email": "<EMAIL>",
            "password": "testpass123"
        }
        self.session = requests.Session()
        self.auth_token = None
        
    def print_status(self, message, status="INFO"):
        colors = {
            "INFO": "\033[0;32m",
            "WARNING": "\033[1;33m", 
            "ERROR": "\033[0;31m",
            "SUCCESS": "\033[0;32m"
        }
        reset = "\033[0m"
        print(f"{colors.get(status, '')}{message}{reset}")
    
    def test_service_health(self):
        """Test if all services are running"""
        self.print_status("🏥 Testing Service Health...", "INFO")
        
        services = [
            ("Main SaaS App", self.base_url),
            ("iOS Service", self.ios_url),
            ("Android Service", self.android_url)
        ]
        
        all_healthy = True
        for name, url in services:
            try:
                response = requests.get(f"{url}/health", timeout=5)
                if response.status_code == 200:
                    self.print_status(f"✅ {name} is healthy", "SUCCESS")
                else:
                    self.print_status(f"❌ {name} health check failed: {response.status_code}", "ERROR")
                    all_healthy = False
            except Exception as e:
                self.print_status(f"❌ {name} is not responding: {e}", "ERROR")
                all_healthy = False
        
        return all_healthy
    
    def test_tenant_info(self):
        """Test tenant information endpoint"""
        self.print_status("🏢 Testing Tenant Information...", "INFO")
        
        try:
            headers = {'X-Tenant-Subdomain': self.test_tenant}
            response = requests.get(f"{self.base_url}/api/tenant/info", headers=headers, timeout=5)
            
            if response.status_code == 200:
                tenant_data = response.json()
                self.print_status(f"✅ Tenant info retrieved: {tenant_data.get('name', 'Unknown')}", "SUCCESS")
                return True
            else:
                self.print_status(f"❌ Tenant info failed: {response.status_code}", "ERROR")
                return False
                
        except Exception as e:
            self.print_status(f"❌ Tenant info error: {e}", "ERROR")
            return False
    
    def test_authentication(self):
        """Test user authentication"""
        self.print_status("🔐 Testing User Authentication...", "INFO")
        
        try:
            # Set tenant header
            headers = {
                'X-Tenant-Subdomain': self.test_tenant,
                'Content-Type': 'application/json'
            }
            
            # Attempt login
            login_data = {
                "email": self.test_user["email"],
                "password": self.test_user["password"]
            }
            
            response = requests.post(
                f"{self.base_url}/api/auth/login",
                headers=headers,
                json=login_data,
                timeout=10
            )
            
            if response.status_code == 200:
                auth_data = response.json()
                self.auth_token = auth_data.get('access_token')
                self.print_status("✅ User authentication successful", "SUCCESS")
                return True
            else:
                self.print_status(f"❌ Authentication failed: {response.status_code} - {response.text}", "ERROR")
                return False
                
        except Exception as e:
            self.print_status(f"❌ Authentication error: {e}", "ERROR")
            return False
    
    def test_automation_access_generation(self):
        """Test automation access URL generation"""
        self.print_status("🔗 Testing Automation Access Generation...", "INFO")
        
        if not self.auth_token:
            self.print_status("❌ No auth token available", "ERROR")
            return False
        
        headers = {
            'X-Tenant-Subdomain': self.test_tenant,
            'Authorization': f'Bearer {self.auth_token}',
            'Content-Type': 'application/json'
        }
        
        # Test iOS access
        try:
            response = requests.post(
                f"{self.base_url}/api/automation/ios/access",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                ios_data = response.json()
                ios_access_url = ios_data.get('access_url')
                ios_session_token = ios_data.get('session_token')
                
                self.print_status("✅ iOS access URL generated", "SUCCESS")
                self.print_status(f"   URL: {ios_access_url}", "INFO")
                
                # Test the access URL
                if self.test_automation_service_access(ios_access_url, "iOS"):
                    self.print_status("✅ iOS automation access working", "SUCCESS")
                else:
                    self.print_status("❌ iOS automation access failed", "ERROR")
                    
            else:
                self.print_status(f"❌ iOS access generation failed: {response.status_code}", "ERROR")
                
        except Exception as e:
            self.print_status(f"❌ iOS access generation error: {e}", "ERROR")
        
        # Test Android access
        try:
            response = requests.post(
                f"{self.base_url}/api/automation/android/access",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                android_data = response.json()
                android_access_url = android_data.get('access_url')
                android_session_token = android_data.get('session_token')
                
                self.print_status("✅ Android access URL generated", "SUCCESS")
                self.print_status(f"   URL: {android_access_url}", "INFO")
                
                # Test the access URL
                if self.test_automation_service_access(android_access_url, "Android"):
                    self.print_status("✅ Android automation access working", "SUCCESS")
                else:
                    self.print_status("❌ Android automation access failed", "ERROR")
                    
            else:
                self.print_status(f"❌ Android access generation failed: {response.status_code}", "ERROR")
                
        except Exception as e:
            self.print_status(f"❌ Android access generation error: {e}", "ERROR")
        
        return True
    
    def test_automation_service_access(self, access_url, platform):
        """Test accessing automation service with session token"""
        try:
            # Parse the URL to extract the token
            parsed_url = urlparse(access_url)
            query_params = parse_qs(parsed_url.query)
            token = query_params.get('token', [None])[0]
            
            if not token:
                self.print_status(f"❌ No token found in {platform} access URL", "ERROR")
                return False
            
            # Test accessing the automation service
            response = requests.get(access_url, timeout=10, allow_redirects=False)
            
            # Check if we get a redirect (which means token was accepted)
            if response.status_code in [200, 302]:
                self.print_status(f"✅ {platform} service accepted session token", "SUCCESS")
                return True
            else:
                self.print_status(f"❌ {platform} service rejected session token: {response.status_code}", "ERROR")
                return False
                
        except Exception as e:
            self.print_status(f"❌ {platform} service access error: {e}", "ERROR")
            return False
    
    def test_device_bridge_api(self):
        """Test device bridge API endpoints"""
        self.print_status("🌉 Testing Device Bridge API...", "INFO")
        
        if not self.auth_token:
            self.print_status("❌ No auth token available", "ERROR")
            return False
        
        headers = {
            'X-Tenant-Subdomain': self.test_tenant,
            'Authorization': f'Bearer {self.auth_token}',
            'Content-Type': 'application/json'
        }
        
        try:
            # Test get devices endpoint
            response = requests.get(
                f"{self.base_url}/api/devices",
                headers=headers,
                params={'platform': 'ios'},
                timeout=10
            )
            
            if response.status_code == 200:
                devices_data = response.json()
                device_count = len(devices_data.get('devices', []))
                self.print_status(f"✅ Device bridge API working - Found {device_count} iOS devices", "SUCCESS")
                return True
            else:
                self.print_status(f"❌ Device bridge API failed: {response.status_code}", "ERROR")
                return False
                
        except Exception as e:
            self.print_status(f"❌ Device bridge API error: {e}", "ERROR")
            return False
    
    def run_complete_test(self):
        """Run the complete authentication flow test"""
        self.print_status("🚀 Starting Complete Authentication Flow Test", "INFO")
        self.print_status("=" * 60, "INFO")
        
        tests = [
            ("Service Health", self.test_service_health),
            ("Tenant Information", self.test_tenant_info),
            ("User Authentication", self.test_authentication),
            ("Automation Access Generation", self.test_automation_access_generation),
            ("Device Bridge API", self.test_device_bridge_api)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            self.print_status(f"\n📋 Running: {test_name}", "INFO")
            try:
                if test_func():
                    passed += 1
                    self.print_status(f"✅ {test_name} PASSED", "SUCCESS")
                else:
                    self.print_status(f"❌ {test_name} FAILED", "ERROR")
            except Exception as e:
                self.print_status(f"❌ {test_name} ERROR: {e}", "ERROR")
        
        self.print_status("=" * 60, "INFO")
        self.print_status(f"🎯 Test Results: {passed}/{total} tests passed", "INFO")
        
        if passed == total:
            self.print_status("🎉 ALL TESTS PASSED! Authentication flow is working correctly.", "SUCCESS")
            return True
        else:
            self.print_status(f"⚠️  {total - passed} tests failed. Please check the issues above.", "WARNING")
            return False

def main():
    """Main function"""
    print("🧪 SaaS Platform Authentication Flow Tester")
    print("=" * 60)
    
    tester = AuthenticationFlowTester()
    success = tester.run_complete_test()
    
    if success:
        print("\n🎉 Authentication flow is ready for production!")
        sys.exit(0)
    else:
        print("\n❌ Authentication flow needs fixes before deployment.")
        sys.exit(1)

if __name__ == "__main__":
    main()
