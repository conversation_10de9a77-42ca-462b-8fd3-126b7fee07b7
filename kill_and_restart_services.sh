#!/bin/bash

echo "🔧 Killing existing processes and restarting services..."
echo "=================================================="

# Function to print colored output
print_status() {
    local message="$1"
    local status="$2"
    
    case $status in
        "SUCCESS")
            echo -e "\033[0;32m$message\033[0m"
            ;;
        "ERROR")
            echo -e "\033[0;31m$message\033[0m"
            ;;
        "WARNING")
            echo -e "\033[1;33m$message\033[0m"
            ;;
        *)
            echo -e "\033[0;32m$message\033[0m"
            ;;
    esac
}

# Kill processes on specific ports
kill_port_processes() {
    local port=$1
    print_status "🔍 Checking port $port..." "INFO"
    
    # Find processes using the port
    local pids=$(lsof -ti:$port 2>/dev/null)
    
    if [ -n "$pids" ]; then
        print_status "Found processes on port $port: $pids" "WARNING"
        for pid in $pids; do
            print_status "Killing process $pid..." "WARNING"
            kill -TERM $pid 2>/dev/null
            sleep 2
            # Force kill if still running
            if kill -0 $pid 2>/dev/null; then
                print_status "Force killing process $pid..." "WARNING"
                kill -KILL $pid 2>/dev/null
            fi
        done
        print_status "✅ Cleaned up port $port" "SUCCESS"
    else
        print_status "✅ Port $port is free" "SUCCESS"
    fi
}

# Kill Python processes that might be related
kill_python_processes() {
    print_status "🐍 Killing related Python processes..." "INFO"
    
    # Kill processes with specific patterns
    pkill -f "start_saas_platform.py" 2>/dev/null
    pkill -f "saas_app.py" 2>/dev/null
    pkill -f "ios_automation_service.py" 2>/dev/null
    pkill -f "android_automation_service.py" 2>/dev/null
    pkill -f "start_main_app.py" 2>/dev/null
    pkill -f "start_ios_service.py" 2>/dev/null
    pkill -f "start_android_service.py" 2>/dev/null
    
    sleep 3
    print_status "✅ Python processes cleaned up" "SUCCESS"
}

# Check if lsof is available
if ! command -v lsof &> /dev/null; then
    print_status "⚠️  lsof not found, installing..." "WARNING"
    if command -v apt-get &> /dev/null; then
        apt-get update && apt-get install -y lsof
    elif command -v yum &> /dev/null; then
        yum install -y lsof
    else
        print_status "❌ Cannot install lsof, using alternative method" "ERROR"
    fi
fi

# Kill existing processes
print_status "🧹 Cleaning up existing processes..." "INFO"
kill_python_processes

# Kill processes on target ports
kill_port_processes 5000
kill_port_processes 8080
kill_port_processes 8081

# Wait a moment
sleep 2

# Verify ports are free
print_status "🔍 Verifying ports are free..." "INFO"
for port in 5000 8080 8081; do
    if lsof -ti:$port >/dev/null 2>&1; then
        print_status "❌ Port $port is still in use!" "ERROR"
        lsof -i:$port
    else
        print_status "✅ Port $port is free" "SUCCESS"
    fi
done

print_status "=================================================="
print_status "🚀 Ready to start services! Run one of these:"
print_status "   python3 quick_fix_services.py"
print_status "   python3 manual_service_startup.py"
print_status "=================================================="
