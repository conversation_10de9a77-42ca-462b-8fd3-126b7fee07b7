# Server Deployment Commands
# Copy and paste these commands into your server terminal after SSH login

# 1. Navigate to project directory
cd /path/to/your/MobileApp-AutoTest

# 2. Make deployment script executable
chmod +x deploy_authentication_updates.sh

# 3. Run the deployment script
./deploy_authentication_updates.sh

# 4. Set up the database
source venv/bin/activate
python3 setup_server_database.py

# 5. Start the platform
python3 start_saas_platform.py run

# 6. In a new terminal, test the authentication flow
python3 test_complete_authentication_flow.py

# Alternative: Run all commands in sequence
#!/bin/bash
cd /path/to/your/MobileApp-AutoTest && \
chmod +x deploy_authentication_updates.sh && \
./deploy_authentication_updates.sh && \
source venv/bin/activate && \
python3 setup_server_database.py && \
echo "Setup complete! Now run: python3 start_saas_platform.py run"
