#!/usr/bin/env python3
"""
Deployment Validation Tests for Mobile Automation SaaS Platform
Tests the deployed infrastructure and application functionality
"""

import os
import sys
import json
import time
import requests
import psycopg2
import pytest
import logging
from pathlib import Path
import subprocess
import socket
from urllib.parse import urlparse

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DeploymentValidator:
    def __init__(self, config_file="deployment_config.json"):
        self.config_file = config_file
        self.config = self.load_config()
        self.base_url = f"https://{self.config['cloudflare']['domain']}"
        
    def load_config(self):
        """Load deployment configuration"""
        config_path = Path(__file__).parent.parent / "deployment" / self.config_file
        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        with open(config_path, 'r') as f:
            return json.load(f)
    
    def test_database_connection(self):
        """Test PostgreSQL database connectivity"""
        logger.info("Testing database connection...")
        
        db_config = self.config["database"]
        
        try:
            conn = psycopg2.connect(
                host=db_config["host"],
                port=db_config["port"],
                database=db_config["name"],
                user=db_config["user"],
                password=db_config["password"]
            )
            
            cursor = conn.cursor()
            cursor.execute("SELECT version();")
            version = cursor.fetchone()
            logger.info(f"Database version: {version[0]}")
            
            # Test tenant isolation
            cursor.execute("SELECT COUNT(*) FROM tenants;")
            tenant_count = cursor.fetchone()[0]
            logger.info(f"Tenant count: {tenant_count}")
            
            cursor.close()
            conn.close()
            
            return True
            
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return False
    
    def test_application_health(self):
        """Test application health endpoint"""
        logger.info("Testing application health...")
        
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            
            if response.status_code == 200:
                health_data = response.json()
                logger.info(f"Application health: {health_data}")
                return health_data.get("status") == "healthy"
            else:
                logger.error(f"Health check failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Health check request failed: {e}")
            return False
    
    def test_authentication_endpoints(self):
        """Test authentication functionality"""
        logger.info("Testing authentication endpoints...")
        
        # Test registration endpoint
        try:
            register_data = {
                "email": "<EMAIL>",
                "password": "TestPassword123!",
                "company_name": "Test Company",
                "subscription_tier": "starter"
            }
            
            response = requests.post(
                f"{self.base_url}/api/auth/register",
                json=register_data,
                timeout=10
            )
            
            if response.status_code in [200, 201, 409]:  # 409 if user already exists
                logger.info("Registration endpoint working")
            else:
                logger.error(f"Registration failed: {response.status_code}")
                return False
            
            # Test login endpoint
            login_data = {
                "email": "<EMAIL>",
                "password": "TestPassword123!"
            }
            
            response = requests.post(
                f"{self.base_url}/api/auth/login",
                json=login_data,
                timeout=10
            )
            
            if response.status_code == 200:
                token_data = response.json()
                if "access_token" in token_data:
                    logger.info("Login endpoint working")
                    return token_data["access_token"]
                else:
                    logger.error("No access token in login response")
                    return False
            else:
                logger.error(f"Login failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Authentication test failed: {e}")
            return False
    
    def test_websocket_connectivity(self):
        """Test WebSocket connectivity for device bridges"""
        logger.info("Testing WebSocket connectivity...")
        
        try:
            import socketio
            
            sio = socketio.Client()
            connected = False
            
            @sio.event
            def connect():
                nonlocal connected
                connected = True
                logger.info("WebSocket connected successfully")
            
            @sio.event
            def disconnect():
                logger.info("WebSocket disconnected")
            
            # Connect to bridge namespace
            sio.connect(f"{self.base_url}", namespaces=['/bridge'])
            
            # Wait for connection
            time.sleep(2)
            
            sio.disconnect()
            
            return connected
            
        except Exception as e:
            logger.error(f"WebSocket test failed: {e}")
            return False
    
    def test_api_endpoints(self, access_token):
        """Test API endpoints with authentication"""
        logger.info("Testing API endpoints...")
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        try:
            # Test tenant info endpoint
            response = requests.get(
                f"{self.base_url}/api/tenant/info",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                tenant_info = response.json()
                logger.info(f"Tenant info: {tenant_info}")
            else:
                logger.error(f"Tenant info failed: {response.status_code}")
                return False
            
            # Test device list endpoint
            response = requests.get(
                f"{self.base_url}/api/devices",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                devices = response.json()
                logger.info(f"Device count: {len(devices)}")
            else:
                logger.error(f"Device list failed: {response.status_code}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"API endpoint test failed: {e}")
            return False
    
    def test_nginx_configuration(self):
        """Test Nginx configuration and reverse proxy"""
        logger.info("Testing Nginx configuration...")
        
        try:
            # Test rate limiting
            responses = []
            for i in range(15):  # Exceed rate limit
                response = requests.get(f"{self.base_url}/api/health", timeout=5)
                responses.append(response.status_code)
            
            # Should get some 429 (Too Many Requests) responses
            rate_limited = any(status == 429 for status in responses)
            if rate_limited:
                logger.info("Rate limiting is working")
            else:
                logger.warning("Rate limiting may not be configured properly")
            
            # Test security headers
            response = requests.get(f"{self.base_url}/", timeout=10)
            headers = response.headers
            
            security_headers = [
                'X-Frame-Options',
                'X-Content-Type-Options',
                'X-XSS-Protection',
                'Strict-Transport-Security'
            ]
            
            missing_headers = []
            for header in security_headers:
                if header not in headers:
                    missing_headers.append(header)
            
            if missing_headers:
                logger.warning(f"Missing security headers: {missing_headers}")
            else:
                logger.info("Security headers are configured")
            
            return True
            
        except Exception as e:
            logger.error(f"Nginx test failed: {e}")
            return False
    
    def test_ssl_certificate(self):
        """Test SSL certificate validity"""
        logger.info("Testing SSL certificate...")
        
        try:
            import ssl
            import socket
            
            domain = self.config['cloudflare']['domain']
            context = ssl.create_default_context()
            
            with socket.create_connection((domain, 443), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=domain) as ssock:
                    cert = ssock.getpeercert()
                    
                    logger.info(f"SSL certificate subject: {cert['subject']}")
                    logger.info(f"SSL certificate issuer: {cert['issuer']}")
                    logger.info(f"SSL certificate expires: {cert['notAfter']}")
                    
                    return True
                    
        except Exception as e:
            logger.error(f"SSL certificate test failed: {e}")
            return False
    
    def test_cloudflare_tunnel(self):
        """Test Cloudflare tunnel connectivity"""
        logger.info("Testing Cloudflare tunnel...")
        
        try:
            # Check if cloudflared is running
            result = subprocess.run(
                ["pgrep", "-f", "cloudflared"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                logger.info("Cloudflared process is running")
                
                # Test tunnel connectivity
                response = requests.get(f"{self.base_url}/health", timeout=10)
                if response.status_code == 200:
                    logger.info("Tunnel connectivity confirmed")
                    return True
                else:
                    logger.error("Tunnel not responding properly")
                    return False
            else:
                logger.error("Cloudflared process not found")
                return False
                
        except Exception as e:
            logger.error(f"Cloudflare tunnel test failed: {e}")
            return False
    
    def test_system_resources(self):
        """Test system resource usage"""
        logger.info("Testing system resources...")
        
        try:
            # Check disk space
            result = subprocess.run(
                ["df", "-h", "/"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                disk_info = result.stdout.split('\n')[1].split()
                disk_usage = disk_info[4].rstrip('%')
                logger.info(f"Disk usage: {disk_usage}%")
                
                if int(disk_usage) > 90:
                    logger.warning("High disk usage detected")
            
            # Check memory usage
            result = subprocess.run(
                ["free", "-h"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                memory_info = result.stdout.split('\n')[1].split()
                logger.info(f"Memory usage: {memory_info[2]} / {memory_info[1]}")
            
            # Check application process
            result = subprocess.run(
                ["pgrep", "-f", "mobile-automation-saas"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                logger.info("Application process is running")
                return True
            else:
                logger.error("Application process not found")
                return False
                
        except Exception as e:
            logger.error(f"System resource test failed: {e}")
            return False
    
    def run_all_tests(self):
        """Run all deployment validation tests"""
        logger.info("Starting deployment validation tests...")
        
        test_results = {}
        
        # Database connectivity
        test_results['database'] = self.test_database_connection()
        
        # Application health
        test_results['health'] = self.test_application_health()
        
        # Authentication
        access_token = self.test_authentication_endpoints()
        test_results['auth'] = bool(access_token)
        
        # API endpoints (if authentication worked)
        if access_token:
            test_results['api'] = self.test_api_endpoints(access_token)
        else:
            test_results['api'] = False
        
        # WebSocket connectivity
        test_results['websocket'] = self.test_websocket_connectivity()
        
        # Nginx configuration
        test_results['nginx'] = self.test_nginx_configuration()
        
        # SSL certificate
        test_results['ssl'] = self.test_ssl_certificate()
        
        # Cloudflare tunnel
        test_results['tunnel'] = self.test_cloudflare_tunnel()
        
        # System resources
        test_results['system'] = self.test_system_resources()
        
        # Summary
        passed_tests = sum(test_results.values())
        total_tests = len(test_results)
        
        logger.info(f"\nDeployment Validation Results:")
        logger.info(f"Passed: {passed_tests}/{total_tests} tests")
        
        for test_name, result in test_results.items():
            status = "✓ PASS" if result else "✗ FAIL"
            logger.info(f"  {test_name}: {status}")
        
        if passed_tests == total_tests:
            logger.info("\n🎉 All tests passed! Deployment is successful.")
            return True
        else:
            logger.error(f"\n❌ {total_tests - passed_tests} tests failed. Please check the deployment.")
            return False

def main():
    """Main function for running deployment validation"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Validate SaaS platform deployment')
    parser.add_argument('--config', help='Configuration file path')
    parser.add_argument('--test', choices=[
        'database', 'health', 'auth', 'api', 'websocket', 
        'nginx', 'ssl', 'tunnel', 'system', 'all'
    ], default='all', help='Specific test to run')
    
    args = parser.parse_args()
    
    validator = DeploymentValidator(args.config or "deployment_config.json")
    
    if args.test == 'all':
        success = validator.run_all_tests()
        sys.exit(0 if success else 1)
    else:
        # Run specific test
        test_method = getattr(validator, f'test_{args.test}')
        result = test_method()
        print(f"Test {args.test}: {'PASS' if result else 'FAIL'}")
        sys.exit(0 if result else 1)

if __name__ == "__main__":
    main()
