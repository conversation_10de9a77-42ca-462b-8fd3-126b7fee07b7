#!/bin/bash

# Mobile Automation SaaS Platform - Startup Script
# This script starts the SaaS application with proper environment setup

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_DIR="$(dirname "$SCRIPT_DIR")"

log_info "Starting Mobile Automation SaaS Platform..."
log_info "Application directory: $APP_DIR"

# Check if virtual environment exists
if [ ! -d "$APP_DIR/venv" ]; then
    log_error "Virtual environment not found at $APP_DIR/venv"
    log_error "Please run the deployment script first"
    exit 1
fi

# Check if .env file exists
if [ ! -f "$APP_DIR/.env" ]; then
    log_error "Environment file not found at $APP_DIR/.env"
    log_error "Please run the deployment script first"
    exit 1
fi

# Change to application directory
cd "$APP_DIR"

# Activate virtual environment
source venv/bin/activate

# Load environment variables
export $(cat .env | grep -v '^#' | xargs)

# Check database connectivity
log_info "Checking database connectivity..."
python3 -c "
import psycopg2
import os
try:
    conn = psycopg2.connect(os.environ['DATABASE_URL'])
    conn.close()
    print('Database connection successful')
except Exception as e:
    print(f'Database connection failed: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    log_error "Database connection failed"
    exit 1
fi

log_success "Database connection verified"

# Start the application
log_info "Starting SaaS application..."
log_info "Access the application at: http://localhost:5000"
log_info "Test users:"
log_info "  - <EMAIL> / testpass123 (Test Company 1)"
log_info "  - <EMAIL> / testpass456 (Test Company 2)"
log_info ""
log_info "Press Ctrl+C to stop the application"
log_info ""

# Run the application
python3 -m saas_infrastructure.app.saas_app
