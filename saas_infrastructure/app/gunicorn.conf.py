# Gunicorn configuration for Mobile Automation SaaS Platform
import os
import multiprocessing

# Server socket
bind = f"0.0.0.0:{os.environ.get('PORT', '5000')}"
backlog = 2048

# Worker processes
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "eventlet"  # Required for SocketIO support
worker_connections = 1000
timeout = 30
keepalive = 2

# Restart workers after this many requests, to help prevent memory leaks
max_requests = 1000
max_requests_jitter = 50

# Logging
accesslog = "/var/log/mobile-automation-saas-access.log"
errorlog = "/var/log/mobile-automation-saas-error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'

# Process naming
proc_name = "mobile-automation-saas"

# Server mechanics
daemon = False  # Supervisor will handle daemonization
pidfile = "/var/run/mobile-automation-saas.pid"
user = "www-data"
group = "www-data"
tmp_upload_dir = None

# SSL (if needed in future)
# keyfile = "/path/to/keyfile"
# certfile = "/path/to/certfile"

# Environment
raw_env = [
    f"ENVIRONMENT={os.environ.get('ENVIRONMENT', 'production')}",
    f"FLASK_ENV={os.environ.get('FLASK_ENV', 'production')}",
    f"DATABASE_URL={os.environ.get('DATABASE_URL', 'postgresql://saas_test_user:test_password_123@localhost:5432/mobile_automation_saas_test')}",
]

# Preload application for better performance
preload_app = True

# Enable automatic worker restarts
reload = False  # Set to True for development

# Worker timeout
graceful_timeout = 30

# Maximum number of pending connections
listen = 1024
