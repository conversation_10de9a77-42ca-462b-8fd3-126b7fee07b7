"""
Enhanced Two-Factor Authentication System
Provides TOTP-based 2FA with QR code generation, backup codes, and secure secret storage.
"""

import os
import json
import secrets
import hashlib
import base64
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Tuple
import logging

import pyotp
import qrcode
from qrcode.image.pil import PilImage
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import bcrypt
from flask import g
from sqlalchemy import text

logger = logging.getLogger(__name__)


class TwoFactorAuthManager:
    """Manages two-factor authentication for users"""
    
    def __init__(self, db_session, encryption_key: Optional[str] = None):
        """
        Initialize 2FA manager
        
        Args:
            db_session: Database session
            encryption_key: Key for encrypting secrets (will generate if not provided)
        """
        self.db = db_session
        self.totp_window = 2  # Allow 2 time windows (60 seconds each)
        
        # Initialize encryption
        if encryption_key:
            self.fernet = Fernet(encryption_key.encode())
        else:
            # Generate encryption key from environment or create new one
            key = os.environ.get('MFA_ENCRYPTION_KEY')
            if not key:
                # Generate a new key (in production, this should be stored securely)
                key = Fernet.generate_key().decode()
                logger.warning("Generated new MFA encryption key. Store this securely!")
            self.fernet = Fernet(key.encode())
    
    def generate_totp_secret(self) -> str:
        """Generate a new TOTP secret"""
        return pyotp.random_base32()
    
    def setup_totp(self, user_id: str, user_email: str) -> Dict:
        """
        Set up TOTP for a user
        
        Args:
            user_id: User ID
            user_email: User email for QR code
            
        Returns:
            Dict containing secret, QR code data, and backup codes
        """
        try:
            # Generate secret
            secret = self.generate_totp_secret()
            
            # Create TOTP instance
            totp = pyotp.TOTP(secret)
            
            # Generate QR code
            provisioning_uri = totp.provisioning_uri(
                name=user_email,
                issuer_name="Mobile Automation SaaS"
            )
            
            # Create QR code image
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(provisioning_uri)
            qr.make(fit=True)
            
            # Convert QR code to base64 for web display
            img = qr.make_image(fill_color="black", back_color="white")
            import io
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            qr_code_data = base64.b64encode(buffer.getvalue()).decode()
            
            # Generate backup codes
            backup_codes = self.generate_backup_codes()
            
            # Encrypt secret and backup codes
            secret_encrypted = self.fernet.encrypt(secret.encode()).decode()
            backup_codes_encrypted = self.fernet.encrypt(
                json.dumps(backup_codes).encode()
            ).decode()
            
            # Store in database (not enabled yet)
            self.db.execute(text("""
                INSERT INTO user_mfa (tenant_id, user_id, mfa_type, secret_encrypted, backup_codes_encrypted, enabled)
                VALUES (:tenant_id, :user_id, 'totp', :secret_encrypted, :backup_codes_encrypted, false)
                ON CONFLICT (tenant_id, user_id, mfa_type) 
                DO UPDATE SET 
                    secret_encrypted = :secret_encrypted,
                    backup_codes_encrypted = :backup_codes_encrypted,
                    enabled = false,
                    updated_at = NOW()
            """), {
                "tenant_id": g.tenant_id,
                "user_id": user_id,
                "secret_encrypted": secret_encrypted,
                "backup_codes_encrypted": backup_codes_encrypted
            })
            self.db.commit()
            
            return {
                "secret": secret,
                "qr_code": qr_code_data,
                "backup_codes": backup_codes,
                "provisioning_uri": provisioning_uri
            }
            
        except Exception as e:
            logger.error(f"Error setting up TOTP for user {user_id}: {e}")
            self.db.rollback()
            raise
    
    def verify_totp_setup(self, user_id: str, token: str) -> bool:
        """
        Verify TOTP setup and enable MFA
        
        Args:
            user_id: User ID
            token: TOTP token to verify
            
        Returns:
            True if verification successful
        """
        try:
            cursor = self.db.execute(text("""
                SELECT secret_encrypted FROM user_mfa
                WHERE tenant_id = :tenant_id AND user_id = :user_id AND mfa_type = 'totp'
            """), {"tenant_id": g.tenant_id, "user_id": user_id})
            
            result = cursor.fetchone()
            if not result:
                return False
            
            # Decrypt secret
            secret = self.fernet.decrypt(result.secret_encrypted.encode()).decode()
            totp = pyotp.TOTP(secret)
            
            # Verify token
            if totp.verify(token, valid_window=self.totp_window):
                # Enable MFA
                self.db.execute(text("""
                    UPDATE user_mfa 
                    SET enabled = true, verified_at = NOW(), updated_at = NOW()
                    WHERE tenant_id = :tenant_id AND user_id = :user_id AND mfa_type = 'totp'
                """), {"tenant_id": g.tenant_id, "user_id": user_id})
                self.db.commit()
                
                # Log MFA enablement
                self.log_security_event(
                    user_id=user_id,
                    action="mfa_enabled",
                    details={"method": "totp"}
                )
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error verifying TOTP setup for user {user_id}: {e}")
            self.db.rollback()
            return False
    
    def verify_totp_token(self, user_id: str, token: str) -> bool:
        """
        Verify TOTP token for authentication
        
        Args:
            user_id: User ID
            token: TOTP token to verify
            
        Returns:
            True if token is valid
        """
        try:
            cursor = self.db.execute(text("""
                SELECT secret_encrypted, backup_codes_encrypted FROM user_mfa
                WHERE tenant_id = :tenant_id AND user_id = :user_id AND mfa_type = 'totp' AND enabled = true
            """), {"tenant_id": g.tenant_id, "user_id": user_id})
            
            result = cursor.fetchone()
            if not result:
                return False
            
            # Check if it's a backup code first
            if self._verify_backup_code(user_id, result.backup_codes_encrypted, token):
                return True
            
            # Verify TOTP token
            secret = self.fernet.decrypt(result.secret_encrypted.encode()).decode()
            totp = pyotp.TOTP(secret)
            
            if totp.verify(token, valid_window=self.totp_window):
                # Update last used timestamp
                self.db.execute(text("""
                    UPDATE user_mfa 
                    SET last_used_at = NOW()
                    WHERE tenant_id = :tenant_id AND user_id = :user_id AND mfa_type = 'totp'
                """), {"tenant_id": g.tenant_id, "user_id": user_id})
                self.db.commit()
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error verifying TOTP token for user {user_id}: {e}")
            return False
    
    def generate_backup_codes(self, count: int = 10) -> List[str]:
        """Generate backup codes for 2FA recovery"""
        codes = []
        for _ in range(count):
            # Generate 8-character alphanumeric code
            code = ''.join(secrets.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789') for _ in range(8))
            codes.append(code)
        return codes
    
    def _verify_backup_code(self, user_id: str, backup_codes_encrypted: str, code: str) -> bool:
        """Verify and consume a backup code"""
        try:
            if not backup_codes_encrypted:
                return False
            
            # Decrypt backup codes
            backup_codes = json.loads(
                self.fernet.decrypt(backup_codes_encrypted.encode()).decode()
            )
            
            # Check if code exists
            if code.upper() in backup_codes:
                # Remove the used code
                backup_codes.remove(code.upper())
                
                # Re-encrypt and update
                new_backup_codes_encrypted = self.fernet.encrypt(
                    json.dumps(backup_codes).encode()
                ).decode()
                
                self.db.execute(text("""
                    UPDATE user_mfa 
                    SET backup_codes_encrypted = :backup_codes_encrypted, last_used_at = NOW()
                    WHERE tenant_id = :tenant_id AND user_id = :user_id AND mfa_type = 'totp'
                """), {
                    "tenant_id": g.tenant_id,
                    "user_id": user_id,
                    "backup_codes_encrypted": new_backup_codes_encrypted
                })
                self.db.commit()
                
                # Log backup code usage
                self.log_security_event(
                    user_id=user_id,
                    action="backup_code_used",
                    details={"remaining_codes": len(backup_codes)}
                )
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error verifying backup code for user {user_id}: {e}")
            return False
    
    def is_mfa_enabled(self, user_id: str) -> bool:
        """Check if MFA is enabled for a user"""
        try:
            cursor = self.db.execute(text("""
                SELECT enabled FROM user_mfa
                WHERE tenant_id = :tenant_id AND user_id = :user_id AND mfa_type = 'totp'
            """), {"tenant_id": g.tenant_id, "user_id": user_id})
            
            result = cursor.fetchone()
            return result and result.enabled
            
        except Exception as e:
            logger.error(f"Error checking MFA status for user {user_id}: {e}")
            return False
    
    def disable_mfa(self, user_id: str) -> bool:
        """Disable MFA for a user"""
        try:
            self.db.execute(text("""
                UPDATE user_mfa 
                SET enabled = false, updated_at = NOW()
                WHERE tenant_id = :tenant_id AND user_id = :user_id AND mfa_type = 'totp'
            """), {"tenant_id": g.tenant_id, "user_id": user_id})
            self.db.commit()
            
            # Log MFA disablement
            self.log_security_event(
                user_id=user_id,
                action="mfa_disabled",
                details={"method": "totp"}
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error disabling MFA for user {user_id}: {e}")
            self.db.rollback()
            return False
    
    def log_security_event(self, user_id: str, action: str, details: Dict = None, success: bool = True):
        """Log security events for audit purposes"""
        try:
            from flask import request
            
            self.db.execute(text("""
                INSERT INTO security_audit_log 
                (tenant_id, user_id, action, ip_address, user_agent, details, success)
                VALUES (:tenant_id, :user_id, :action, :ip_address, :user_agent, :details, :success)
            """), {
                "tenant_id": g.tenant_id,
                "user_id": user_id,
                "action": action,
                "ip_address": request.remote_addr if request else None,
                "user_agent": request.headers.get('User-Agent') if request else None,
                "details": json.dumps(details or {}),
                "success": success
            })
            self.db.commit()
            
        except Exception as e:
            logger.error(f"Error logging security event: {e}")
