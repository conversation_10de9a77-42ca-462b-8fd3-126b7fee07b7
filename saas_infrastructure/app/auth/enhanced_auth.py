"""
Enhanced Authentication System
Integrates 2FA, device fingerprinting, and advanced security measures.
"""

import os
import json
import secrets
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
import logging

import bcrypt
from flask import request, g, session
from flask_jwt_extended import create_access_token, decode_token
from sqlalchemy import text

from .two_factor_auth import TwoFactorAuthManager
from .device_fingerprinting import DeviceFingerprintManager

logger = logging.getLogger(__name__)


class EnhancedAuthManager:
    """Enhanced authentication manager with 2FA and device fingerprinting"""
    
    def __init__(self, db_session, jwt_secret_key: str):
        """Initialize enhanced authentication manager"""
        self.db = db_session
        self.jwt_secret_key = jwt_secret_key
        self.mfa_manager = TwoFactorAuthManager(db_session)
        self.device_manager = DeviceFingerprintManager(db_session)
        
        # Security settings
        self.max_login_attempts = 5
        self.lockout_duration = timedelta(minutes=30)
        self.session_timeout = timedelta(hours=24)
        self.require_mfa_for_new_devices = True
    
    def authenticate_user(self, email: str, password: str, totp_code: str = None, 
                         device_data: Dict = None) -> Tuple[bool, Dict]:
        """
        Enhanced user authentication with 2FA and device fingerprinting
        
        Args:
            email: User email
            password: User password
            totp_code: TOTP code for 2FA
            device_data: Additional device fingerprint data
            
        Returns:
            Tuple of (success, result_data)
        """
        try:
            # Check if user exists and is active
            user = self._get_user_by_email(email)
            if not user:
                self._log_auth_attempt(None, email, "user_not_found", False)
                return False, {"error": "Invalid credentials"}
            
            user_id = user['id']
            
            # Check account lockout
            if self._is_account_locked(user_id):
                self._log_auth_attempt(user_id, email, "account_locked", False)
                return False, {"error": "Account temporarily locked due to too many failed attempts"}
            
            # Verify password
            if not bcrypt.checkpw(password.encode('utf-8'), user['password_hash'].encode('utf-8')):
                self._record_failed_attempt(user_id)
                self._log_auth_attempt(user_id, email, "invalid_password", False)
                return False, {"error": "Invalid credentials"}
            
            # Check device fingerprint
            is_trusted_device, device_id = self.device_manager.verify_device(user_id, device_data)
            
            # Register new device if not found
            if not device_id:
                device_id = self.device_manager.register_device(
                    user_id, 
                    device_data.get('device_name') if device_data else None,
                    device_data
                )
                is_trusted_device = False
            
            # Check if MFA is enabled
            mfa_enabled = self.mfa_manager.is_mfa_enabled(user_id)
            
            # Determine if 2FA is required
            require_2fa = (
                mfa_enabled and (
                    not is_trusted_device or 
                    self.require_mfa_for_new_devices or
                    totp_code is not None  # User provided code
                )
            )
            
            if require_2fa:
                if not totp_code:
                    # 2FA required but no code provided
                    return False, {
                        "error": "Two-factor authentication required",
                        "requires_2fa": True,
                        "device_trusted": is_trusted_device
                    }
                
                # Verify 2FA code
                if not self.mfa_manager.verify_totp_token(user_id, totp_code):
                    self._record_failed_attempt(user_id)
                    self._log_auth_attempt(user_id, email, "invalid_2fa", False)
                    return False, {"error": "Invalid two-factor authentication code"}
            
            # Authentication successful
            self._clear_failed_attempts(user_id)
            
            # Create session
            session_data = self._create_user_session(user_id, device_id)
            
            # Create JWT token
            token_data = {
                'user_id': user_id,
                'tenant_id': str(g.tenant_id),
                'tenant_subdomain': g.tenant_data['subdomain'],
                'user_role': user['role'],
                'subscription_tier': g.tenant_data['subscription_tier'],
                'session_id': session_data['session_id'],
                'device_id': device_id,
                'mfa_verified': require_2fa
            }
            
            access_token = create_access_token(
                identity=str(user_id),
                additional_claims=token_data,
                expires_delta=self.session_timeout
            )
            
            # Update last login
            self._update_last_login(user_id)
            
            # Log successful authentication
            self._log_auth_attempt(user_id, email, "login_success", True, {
                "device_id": device_id,
                "mfa_used": require_2fa,
                "trusted_device": is_trusted_device
            })
            
            return True, {
                "access_token": access_token,
                "user": {
                    "id": user_id,
                    "email": user['email'],
                    "first_name": user['first_name'],
                    "last_name": user['last_name'],
                    "role": user['role']
                },
                "session": session_data,
                "device_trusted": is_trusted_device,
                "mfa_enabled": mfa_enabled
            }
            
        except Exception as e:
            logger.error(f"Authentication error for {email}: {e}")
            return False, {"error": "Authentication failed"}
    
    def setup_2fa(self, user_id: str, user_email: str) -> Dict:
        """Setup 2FA for a user"""
        return self.mfa_manager.setup_totp(user_id, user_email)
    
    def verify_2fa_setup(self, user_id: str, token: str) -> bool:
        """Verify 2FA setup"""
        return self.mfa_manager.verify_totp_setup(user_id, token)
    
    def disable_2fa(self, user_id: str) -> bool:
        """Disable 2FA for a user"""
        return self.mfa_manager.disable_mfa(user_id)
    
    def trust_device(self, user_id: str, device_id: str) -> bool:
        """Trust a device for a user"""
        return self.device_manager.trust_device(user_id, device_id)
    
    def revoke_device(self, user_id: str, device_id: str) -> bool:
        """Revoke trust for a device"""
        return self.device_manager.revoke_device(user_id, device_id)
    
    def get_user_devices(self, user_id: str) -> list:
        """Get all devices for a user"""
        return self.device_manager.get_user_devices(user_id)
    
    def verify_session(self, token: str) -> Tuple[bool, Optional[Dict]]:
        """
        Verify JWT token and session validity
        
        Args:
            token: JWT token
            
        Returns:
            Tuple of (is_valid, user_data)
        """
        try:
            # Decode token
            decoded_token = decode_token(token)
            user_id = decoded_token['sub']
            session_id = decoded_token.get('session_id')
            
            if not session_id:
                return False, None
            
            # Verify session is still active
            cursor = self.db.execute(text("""
                SELECT user_id, expires_at, is_active FROM user_sessions
                WHERE id = :session_id AND user_id = :user_id
            """), {"session_id": session_id, "user_id": user_id})
            
            session_data = cursor.fetchone()
            if not session_data or not session_data.is_active:
                return False, None
            
            # Check if session has expired
            if session_data.expires_at < datetime.utcnow():
                self._invalidate_session(session_id)
                return False, None
            
            # Update last activity
            self._update_session_activity(session_id)
            
            # Get user data
            user_data = self._get_user_by_id(user_id)
            if not user_data or not user_data['is_active']:
                return False, None
            
            return True, {
                "user": user_data,
                "claims": decoded_token
            }
            
        except Exception as e:
            logger.error(f"Session verification error: {e}")
            return False, None
    
    def logout(self, session_id: str) -> bool:
        """Logout user and invalidate session"""
        try:
            self._invalidate_session(session_id)
            return True
        except Exception as e:
            logger.error(f"Logout error: {e}")
            return False
    
    def _get_user_by_email(self, email: str) -> Optional[Dict]:
        """Get user by email"""
        try:
            cursor = self.db.execute(text("""
                SELECT * FROM users
                WHERE tenant_id = :tenant_id AND email = :email AND is_active = true
            """), {"tenant_id": g.tenant_id, "email": email})
            
            result = cursor.fetchone()
            return dict(result._mapping) if result else None
            
        except Exception as e:
            logger.error(f"Error getting user by email {email}: {e}")
            return None
    
    def _get_user_by_id(self, user_id: str) -> Optional[Dict]:
        """Get user by ID"""
        try:
            cursor = self.db.execute(text("""
                SELECT * FROM users
                WHERE tenant_id = :tenant_id AND id = :user_id AND is_active = true
            """), {"tenant_id": g.tenant_id, "user_id": user_id})
            
            result = cursor.fetchone()
            return dict(result._mapping) if result else None
            
        except Exception as e:
            logger.error(f"Error getting user by ID {user_id}: {e}")
            return None
    
    def _is_account_locked(self, user_id: str) -> bool:
        """Check if account is locked due to failed attempts"""
        try:
            cursor = self.db.execute(text("""
                SELECT COUNT(*) as failed_count, MAX(created_at) as last_attempt
                FROM security_audit_log
                WHERE user_id = :user_id AND action LIKE '%failed%' 
                AND created_at > NOW() - INTERVAL '30 minutes'
            """), {"user_id": user_id})
            
            result = cursor.fetchone()
            if result and result.failed_count >= self.max_login_attempts:
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking account lock status: {e}")
            return False
    
    def _record_failed_attempt(self, user_id: str):
        """Record a failed login attempt"""
        self._log_auth_attempt(user_id, None, "login_failed", False)
    
    def _clear_failed_attempts(self, user_id: str):
        """Clear failed login attempts (implicitly by successful login)"""
        pass  # Failed attempts are time-based, so no need to clear explicitly
    
    def _create_user_session(self, user_id: str, device_id: str) -> Dict:
        """Create a new user session"""
        try:
            session_token = secrets.token_urlsafe(32)
            session_token_hash = hashlib.sha256(session_token.encode()).hexdigest()
            expires_at = datetime.utcnow() + self.session_timeout
            
            cursor = self.db.execute(text("""
                INSERT INTO user_sessions 
                (tenant_id, user_id, session_token_hash, device_fingerprint_id, 
                 ip_address, user_agent, expires_at)
                VALUES (:tenant_id, :user_id, :session_token_hash, :device_id,
                        :ip_address, :user_agent, :expires_at)
                RETURNING id
            """), {
                "tenant_id": g.tenant_id,
                "user_id": user_id,
                "session_token_hash": session_token_hash,
                "device_id": device_id,
                "ip_address": request.remote_addr,
                "user_agent": request.headers.get('User-Agent'),
                "expires_at": expires_at
            })
            
            session_id = cursor.fetchone().id
            self.db.commit()
            
            return {
                "session_id": session_id,
                "session_token": session_token,
                "expires_at": expires_at.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error creating user session: {e}")
            self.db.rollback()
            raise
    
    def _update_session_activity(self, session_id: str):
        """Update session last activity timestamp"""
        try:
            self.db.execute(text("""
                UPDATE user_sessions 
                SET last_activity_at = NOW()
                WHERE id = :session_id
            """), {"session_id": session_id})
            self.db.commit()
            
        except Exception as e:
            logger.error(f"Error updating session activity: {e}")
    
    def _invalidate_session(self, session_id: str):
        """Invalidate a user session"""
        try:
            self.db.execute(text("""
                UPDATE user_sessions 
                SET is_active = false
                WHERE id = :session_id
            """), {"session_id": session_id})
            self.db.commit()
            
        except Exception as e:
            logger.error(f"Error invalidating session: {e}")
            self.db.rollback()
    
    def _update_last_login(self, user_id: str):
        """Update user's last login timestamp"""
        try:
            self.db.execute(text("""
                UPDATE users 
                SET last_login_at = NOW()
                WHERE id = :user_id
            """), {"user_id": user_id})
            self.db.commit()
            
        except Exception as e:
            logger.error(f"Error updating last login: {e}")
    
    def _log_auth_attempt(self, user_id: str, email: str, action: str, 
                         success: bool, details: Dict = None):
        """Log authentication attempt"""
        try:
            self.db.execute(text("""
                INSERT INTO security_audit_log 
                (tenant_id, user_id, action, ip_address, user_agent, details, success)
                VALUES (:tenant_id, :user_id, :action, :ip_address, :user_agent, :details, :success)
            """), {
                "tenant_id": g.tenant_id,
                "user_id": user_id,
                "action": action,
                "ip_address": request.remote_addr,
                "user_agent": request.headers.get('User-Agent'),
                "details": json.dumps(details or {"email": email}),
                "success": success
            })
            self.db.commit()
            
        except Exception as e:
            logger.error(f"Error logging auth attempt: {e}")
