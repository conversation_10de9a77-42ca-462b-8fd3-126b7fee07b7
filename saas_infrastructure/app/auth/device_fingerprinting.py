"""
Device Fingerprinting System
Provides hardware and browser fingerprinting for enhanced security.
"""

import hashlib
import json
import logging
from datetime import datetime
from typing import Dict, Optional, Tuple
from flask import request, g
from sqlalchemy import text

logger = logging.getLogger(__name__)


class DeviceFingerprintManager:
    """Manages device fingerprinting for enhanced security"""
    
    def __init__(self, db_session):
        """Initialize device fingerprint manager"""
        self.db = db_session
    
    def generate_fingerprint(self, additional_data: Dict = None) -> str:
        """
        Generate device fingerprint from request headers and additional data
        
        Args:
            additional_data: Additional data to include in fingerprint
            
        Returns:
            Fingerprint hash string
        """
        try:
            # Collect fingerprint data
            fingerprint_data = {
                'user_agent': request.headers.get('User-Agent', ''),
                'accept_language': request.headers.get('Accept-Language', ''),
                'accept_encoding': request.headers.get('Accept-Encoding', ''),
                'accept': request.headers.get('Accept', ''),
                'connection': request.headers.get('Connection', ''),
                'upgrade_insecure_requests': request.headers.get('Upgrade-Insecure-Requests', ''),
                'sec_fetch_site': request.headers.get('Sec-Fetch-Site', ''),
                'sec_fetch_mode': request.headers.get('Sec-Fetch-Mode', ''),
                'sec_fetch_dest': request.headers.get('Sec-Fetch-Dest', ''),
                'cache_control': request.headers.get('Cache-Control', ''),
            }
            
            # Add additional data if provided
            if additional_data:
                fingerprint_data.update(additional_data)
            
            # Create deterministic fingerprint
            fingerprint_string = json.dumps(fingerprint_data, sort_keys=True)
            fingerprint_hash = hashlib.sha256(fingerprint_string.encode()).hexdigest()
            
            return fingerprint_hash
            
        except Exception as e:
            logger.error(f"Error generating device fingerprint: {e}")
            # Return a basic fallback fingerprint
            return hashlib.sha256(
                (request.headers.get('User-Agent', 'unknown')).encode()
            ).hexdigest()
    
    def register_device(self, user_id: str, device_name: str = None, 
                       additional_data: Dict = None) -> str:
        """
        Register a new device for a user
        
        Args:
            user_id: User ID
            device_name: Optional device name
            additional_data: Additional fingerprint data
            
        Returns:
            Device fingerprint ID
        """
        try:
            fingerprint_hash = self.generate_fingerprint(additional_data)
            
            # Collect browser info
            browser_info = {
                'user_agent': request.headers.get('User-Agent', ''),
                'accept_language': request.headers.get('Accept-Language', ''),
                'screen_resolution': additional_data.get('screen_resolution') if additional_data else None,
                'timezone': additional_data.get('timezone') if additional_data else None,
                'platform': additional_data.get('platform') if additional_data else None,
            }
            
            # Check if device already exists
            cursor = self.db.execute(text("""
                SELECT id FROM device_fingerprints
                WHERE tenant_id = :tenant_id AND user_id = :user_id AND fingerprint_hash = :fingerprint_hash
            """), {
                "tenant_id": g.tenant_id,
                "user_id": user_id,
                "fingerprint_hash": fingerprint_hash
            })
            
            existing_device = cursor.fetchone()
            if existing_device:
                # Update last seen
                self.db.execute(text("""
                    UPDATE device_fingerprints 
                    SET last_seen_at = NOW(), ip_address = :ip_address
                    WHERE id = :device_id
                """), {
                    "device_id": existing_device.id,
                    "ip_address": request.remote_addr
                })
                self.db.commit()
                return existing_device.id
            
            # Register new device
            cursor = self.db.execute(text("""
                INSERT INTO device_fingerprints 
                (tenant_id, user_id, fingerprint_hash, device_name, browser_info, ip_address)
                VALUES (:tenant_id, :user_id, :fingerprint_hash, :device_name, :browser_info, :ip_address)
                RETURNING id
            """), {
                "tenant_id": g.tenant_id,
                "user_id": user_id,
                "fingerprint_hash": fingerprint_hash,
                "device_name": device_name or self._generate_device_name(browser_info),
                "browser_info": json.dumps(browser_info),
                "ip_address": request.remote_addr
            })
            
            device_id = cursor.fetchone().id
            self.db.commit()
            
            # Log device registration
            self._log_security_event(
                user_id=user_id,
                action="device_registered",
                details={
                    "device_id": device_id,
                    "fingerprint_hash": fingerprint_hash[:16] + "...",  # Partial hash for logging
                    "device_name": device_name
                }
            )
            
            return device_id
            
        except Exception as e:
            logger.error(f"Error registering device for user {user_id}: {e}")
            self.db.rollback()
            raise
    
    def verify_device(self, user_id: str, additional_data: Dict = None) -> Tuple[bool, Optional[str]]:
        """
        Verify if current device is recognized for the user
        
        Args:
            user_id: User ID
            additional_data: Additional fingerprint data
            
        Returns:
            Tuple of (is_trusted, device_id)
        """
        try:
            fingerprint_hash = self.generate_fingerprint(additional_data)
            
            cursor = self.db.execute(text("""
                SELECT id, is_trusted FROM device_fingerprints
                WHERE tenant_id = :tenant_id AND user_id = :user_id AND fingerprint_hash = :fingerprint_hash
            """), {
                "tenant_id": g.tenant_id,
                "user_id": user_id,
                "fingerprint_hash": fingerprint_hash
            })
            
            result = cursor.fetchone()
            if result:
                # Update last seen
                self.db.execute(text("""
                    UPDATE device_fingerprints 
                    SET last_seen_at = NOW(), ip_address = :ip_address
                    WHERE id = :device_id
                """), {
                    "device_id": result.id,
                    "ip_address": request.remote_addr
                })
                self.db.commit()
                
                return result.is_trusted, result.id
            
            return False, None
            
        except Exception as e:
            logger.error(f"Error verifying device for user {user_id}: {e}")
            return False, None
    
    def trust_device(self, user_id: str, device_id: str) -> bool:
        """
        Mark a device as trusted
        
        Args:
            user_id: User ID
            device_id: Device fingerprint ID
            
        Returns:
            True if successful
        """
        try:
            self.db.execute(text("""
                UPDATE device_fingerprints 
                SET is_trusted = true
                WHERE tenant_id = :tenant_id AND user_id = :user_id AND id = :device_id
            """), {
                "tenant_id": g.tenant_id,
                "user_id": user_id,
                "device_id": device_id
            })
            self.db.commit()
            
            # Log device trust
            self._log_security_event(
                user_id=user_id,
                action="device_trusted",
                details={"device_id": device_id}
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error trusting device {device_id} for user {user_id}: {e}")
            self.db.rollback()
            return False
    
    def revoke_device(self, user_id: str, device_id: str) -> bool:
        """
        Revoke trust for a device
        
        Args:
            user_id: User ID
            device_id: Device fingerprint ID
            
        Returns:
            True if successful
        """
        try:
            self.db.execute(text("""
                UPDATE device_fingerprints 
                SET is_trusted = false
                WHERE tenant_id = :tenant_id AND user_id = :user_id AND id = :device_id
            """), {
                "tenant_id": g.tenant_id,
                "user_id": user_id,
                "device_id": device_id
            })
            self.db.commit()
            
            # Log device revocation
            self._log_security_event(
                user_id=user_id,
                action="device_revoked",
                details={"device_id": device_id}
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error revoking device {device_id} for user {user_id}: {e}")
            self.db.rollback()
            return False
    
    def get_user_devices(self, user_id: str) -> list:
        """
        Get all devices for a user
        
        Args:
            user_id: User ID
            
        Returns:
            List of device information
        """
        try:
            cursor = self.db.execute(text("""
                SELECT id, device_name, browser_info, ip_address, is_trusted, 
                       first_seen_at, last_seen_at
                FROM device_fingerprints
                WHERE tenant_id = :tenant_id AND user_id = :user_id
                ORDER BY last_seen_at DESC
            """), {
                "tenant_id": g.tenant_id,
                "user_id": user_id
            })
            
            devices = []
            for row in cursor.fetchall():
                device_info = dict(row._mapping)
                # Parse browser info
                if device_info['browser_info']:
                    device_info['browser_info'] = json.loads(device_info['browser_info'])
                devices.append(device_info)
            
            return devices
            
        except Exception as e:
            logger.error(f"Error getting devices for user {user_id}: {e}")
            return []
    
    def _generate_device_name(self, browser_info: Dict) -> str:
        """Generate a friendly device name from browser info"""
        try:
            user_agent = browser_info.get('user_agent', '')
            
            # Simple device name generation
            if 'Windows' in user_agent:
                os_name = 'Windows'
            elif 'Mac' in user_agent:
                os_name = 'macOS'
            elif 'Linux' in user_agent:
                os_name = 'Linux'
            elif 'Android' in user_agent:
                os_name = 'Android'
            elif 'iPhone' in user_agent or 'iPad' in user_agent:
                os_name = 'iOS'
            else:
                os_name = 'Unknown'
            
            # Browser detection
            if 'Chrome' in user_agent:
                browser = 'Chrome'
            elif 'Firefox' in user_agent:
                browser = 'Firefox'
            elif 'Safari' in user_agent:
                browser = 'Safari'
            elif 'Edge' in user_agent:
                browser = 'Edge'
            else:
                browser = 'Unknown'
            
            return f"{os_name} - {browser}"
            
        except Exception:
            return "Unknown Device"
    
    def _log_security_event(self, user_id: str, action: str, details: Dict = None):
        """Log security events for audit purposes"""
        try:
            self.db.execute(text("""
                INSERT INTO security_audit_log 
                (tenant_id, user_id, action, ip_address, user_agent, details, success)
                VALUES (:tenant_id, :user_id, :action, :ip_address, :user_agent, :details, :success)
            """), {
                "tenant_id": g.tenant_id,
                "user_id": user_id,
                "action": action,
                "ip_address": request.remote_addr,
                "user_agent": request.headers.get('User-Agent'),
                "details": json.dumps(details or {}),
                "success": True
            })
            self.db.commit()
            
        except Exception as e:
            logger.error(f"Error logging security event: {e}")
