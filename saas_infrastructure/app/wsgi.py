#!/usr/bin/env python3
"""
WSGI entry point for production deployment of the Mobile Automation SaaS Platform.
This file provides a proper WSGI interface for production servers like Gunicorn.
"""

import os
import sys
import logging
from pathlib import Path

# Add the app directory to Python path
app_dir = Path(__file__).parent
sys.path.insert(0, str(app_dir))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

try:
    # Import the application factory
    from saas_app import create_app
    
    # Create the application and SocketIO instance
    app, socketio = create_app()
    
    # For WSGI servers, we need to expose the app object
    application = app
    
    logger.info("WSGI application initialized successfully")
    logger.info(f"Environment: {os.environ.get('ENVIRONMENT', 'development')}")
    logger.info(f"Debug mode: {app.debug}")
    
except Exception as e:
    logger.error(f"Failed to initialize WSGI application: {e}")
    raise

# For Gunicorn with eventlet/gevent worker, we need to expose socketio
# This allows proper WebSocket support in production
if __name__ != '__main__':
    # Running under WSGI server
    logger.info("Running under WSGI server")
    
    # For SocketIO support with Gunicorn, use the socketio app
    application = socketio
