#!/usr/bin/env python3
"""
Template Adapter for Multi-Tenant Dashboard Integration
Adapts existing templates for multi-tenant use with tenant context injection
"""

import os
import json
import logging
from pathlib import Path
from flask import g, request, url_for
from jinja2 import Environment, FileSystemLoader, select_autoescape

logger = logging.getLogger(__name__)

class TemplateAdapter:
    def __init__(self, app):
        self.app = app
        self.setup_template_context()
        self.setup_template_filters()
        
    def setup_template_context(self):
        """Setup template context processors for tenant data injection"""
        
        @self.app.context_processor
        def inject_tenant_context():
            """Inject tenant context into all templates"""
            context = {}
            
            # Add tenant information if available
            if hasattr(g, 'tenant_data') and g.tenant_data:
                context['tenant'] = {
                    'id': g.tenant_data.get('id'),
                    'name': g.tenant_data.get('name'),
                    'subdomain': g.tenant_data.get('subdomain'),
                    'subscription_tier': g.tenant_data.get('subscription_tier'),
                    'max_devices': g.tenant_data.get('max_devices'),
                    'max_test_minutes': g.tenant_data.get('max_test_minutes')
                }
            else:
                context['tenant'] = None
            
            # Add user information if available
            if hasattr(g, 'user_id') and g.user_id:
                context['current_user'] = {
                    'id': g.user_id,
                    'role': getattr(g, 'user_role', 'user')
                }
            else:
                context['current_user'] = None
            
            # Add platform context
            context['platform'] = self.get_current_platform()
            
            # Add navigation context
            context['navigation'] = self.get_navigation_context()
            
            # Add subscription limits
            context['subscription_limits'] = self.get_subscription_limits()
            
            return context
    
    def setup_template_filters(self):
        """Setup custom template filters for multi-tenant use"""
        
        @self.app.template_filter('tenant_url')
        def tenant_url_filter(endpoint, **values):
            """Generate tenant-specific URLs"""
            if hasattr(g, 'tenant_data') and g.tenant_data:
                # Add tenant context to URL generation
                if 'tenant_subdomain' not in values:
                    values['tenant_subdomain'] = g.tenant_data.get('subdomain')
            
            return url_for(endpoint, **values)
        
        @self.app.template_filter('format_duration')
        def format_duration_filter(seconds):
            """Format duration in seconds to human readable format"""
            if not seconds:
                return "0s"
            
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            secs = int(seconds % 60)
            
            if hours > 0:
                return f"{hours}h {minutes}m {secs}s"
            elif minutes > 0:
                return f"{minutes}m {secs}s"
            else:
                return f"{secs}s"
        
        @self.app.template_filter('subscription_feature')
        def subscription_feature_filter(feature_name):
            """Check if current tenant has access to a feature"""
            if not hasattr(g, 'tenant_data') or not g.tenant_data:
                return False
            
            tier = g.tenant_data.get('subscription_tier', 'starter')
            
            # Define feature matrix
            features = {
                'starter': ['basic_reporting', 'email_support'],
                'professional': ['basic_reporting', 'email_support', 'advanced_reporting', 'priority_support', 'api_access'],
                'team': ['basic_reporting', 'email_support', 'advanced_reporting', 'priority_support', 'api_access', 'team_collaboration', 'dedicated_support', 'custom_integrations'],
                'enterprise': ['basic_reporting', 'email_support', 'advanced_reporting', 'priority_support', 'api_access', 'team_collaboration', 'dedicated_support', 'custom_integrations', 'unlimited_devices', '24_7_support', 'white_label', 'on_premise']
            }
            
            return feature_name in features.get(tier, [])
        
        @self.app.template_filter('device_status_class')
        def device_status_class_filter(status):
            """Get CSS class for device status"""
            status_classes = {
                'connected': 'text-success',
                'disconnected': 'text-danger',
                'busy': 'text-warning',
                'offline': 'text-muted'
            }
            return status_classes.get(status, 'text-muted')
        
        @self.app.template_filter('platform_icon')
        def platform_icon_filter(platform):
            """Get icon class for platform"""
            icons = {
                'ios': 'fab fa-apple',
                'android': 'fab fa-android'
            }
            return icons.get(platform, 'fas fa-mobile-alt')
    
    def get_current_platform(self):
        """Determine current platform from request path"""
        path = request.path
        if '/dashboard/ios' in path:
            return 'ios'
        elif '/dashboard/android' in path:
            return 'android'
        else:
            return None
    
    def get_navigation_context(self):
        """Get navigation context for current tenant"""
        if not hasattr(g, 'tenant_data') or not g.tenant_data:
            return {}
        
        subdomain = g.tenant_data.get('subdomain')
        
        navigation = {
            'dashboard_links': [
                {
                    'name': 'iOS Dashboard',
                    'url': f'/dashboard/ios/',
                    'icon': 'fab fa-apple',
                    'active': self.get_current_platform() == 'ios'
                },
                {
                    'name': 'Android Dashboard',
                    'url': f'/dashboard/android/',
                    'icon': 'fab fa-android',
                    'active': self.get_current_platform() == 'android'
                }
            ],
            'main_links': [
                {
                    'name': 'Devices',
                    'url': '/devices',
                    'icon': 'fas fa-mobile-alt'
                },
                {
                    'name': 'Test Suites',
                    'url': '/test-suites',
                    'icon': 'fas fa-list-check'
                },
                {
                    'name': 'Reports',
                    'url': '/reports',
                    'icon': 'fas fa-chart-bar'
                },
                {
                    'name': 'Settings',
                    'url': '/settings',
                    'icon': 'fas fa-cog'
                }
            ]
        }
        
        # Add admin links for admin users
        if hasattr(g, 'user_role') and g.user_role == 'admin':
            navigation['admin_links'] = [
                {
                    'name': 'User Management',
                    'url': '/admin/users',
                    'icon': 'fas fa-users'
                },
                {
                    'name': 'Bridge Management',
                    'url': '/admin/bridges',
                    'icon': 'fas fa-network-wired'
                },
                {
                    'name': 'Usage Analytics',
                    'url': '/admin/analytics',
                    'icon': 'fas fa-analytics'
                }
            ]
        
        return navigation
    
    def get_subscription_limits(self):
        """Get subscription limits for current tenant"""
        if not hasattr(g, 'tenant_data') or not g.tenant_data:
            return {}
        
        return {
            'max_devices': g.tenant_data.get('max_devices', 0),
            'max_test_minutes': g.tenant_data.get('max_test_minutes', 0),
            'current_devices': self.get_current_device_count(),
            'current_test_minutes': self.get_current_test_minutes()
        }
    
    def get_current_device_count(self):
        """Get current device count for tenant"""
        try:
            from flask import current_app
            db = current_app.extensions.get('sqlalchemy')
            if not db:
                return 0
            
            cursor = db.session.execute("""
                SELECT COUNT(*) as count FROM tenant_devices 
                WHERE tenant_id = :tenant_id
            """, {"tenant_id": g.tenant_id})
            
            result = cursor.fetchone()
            return result['count'] if result else 0
            
        except Exception as e:
            logger.error(f"Error getting device count: {e}")
            return 0
    
    def get_current_test_minutes(self):
        """Get current month test minutes for tenant"""
        try:
            from flask import current_app
            db = current_app.extensions.get('sqlalchemy')
            if not db:
                return 0
            
            cursor = db.session.execute("""
                SELECT COALESCE(metric_value, 0) as minutes FROM usage_tracking
                WHERE tenant_id = :tenant_id 
                AND metric_type = 'test_minutes'
                AND billing_period = DATE_TRUNC('month', CURRENT_DATE)::DATE
            """, {"tenant_id": g.tenant_id})
            
            result = cursor.fetchone()
            return float(result['minutes']) if result else 0.0
            
        except Exception as e:
            logger.error(f"Error getting test minutes: {e}")
            return 0.0

class TenantTemplateLoader:
    """Custom template loader that can override templates per tenant"""
    
    def __init__(self, base_template_dirs):
        self.base_template_dirs = base_template_dirs
        self.tenant_template_cache = {}
    
    def get_template_path(self, template_name, tenant_subdomain=None):
        """Get template path, checking for tenant-specific overrides"""
        
        # Check for tenant-specific template first
        if tenant_subdomain:
            tenant_template_dir = Path(f"templates/tenants/{tenant_subdomain}")
            tenant_template_path = tenant_template_dir / template_name
            
            if tenant_template_path.exists():
                return str(tenant_template_path)
        
        # Fall back to default template
        for template_dir in self.base_template_dirs:
            template_path = Path(template_dir) / template_name
            if template_path.exists():
                return str(template_path)
        
        return None
    
    def customize_template_for_tenant(self, template_content, tenant_data):
        """Customize template content for specific tenant"""
        
        # Replace tenant-specific placeholders
        if tenant_data:
            replacements = {
                '{{TENANT_NAME}}': tenant_data.get('name', ''),
                '{{TENANT_SUBDOMAIN}}': tenant_data.get('subdomain', ''),
                '{{SUBSCRIPTION_TIER}}': tenant_data.get('subscription_tier', 'starter')
            }
            
            for placeholder, value in replacements.items():
                template_content = template_content.replace(placeholder, value)
        
        return template_content

def setup_template_adapter(app):
    """Setup template adapter for the Flask app"""
    return TemplateAdapter(app)
