#!/usr/bin/env python3
"""
Device Manager for Multi-Tenant SaaS Platform
Handles device registration, status tracking, and communication with device bridges
"""

import json
import logging
import uuid
from datetime import datetime, timedelta
from flask import g
from flask_socketio import emit

logger = logging.getLogger(__name__)

class DeviceManager:
    def __init__(self, db, socketio):
        self.db = db
        self.socketio = socketio
        self.active_bridges = {}  # bridge_id -> connection info
        
    def register_bridge(self, bridge_token, bridge_name, connection_info):
        """Register a device bridge connection"""
        try:
            # Verify bridge token and get bridge info
            cursor = self.db.session.execute("""
                SELECT b.*, t.id as tenant_id, t.name as tenant_name
                FROM device_bridges b
                JOIN tenants t ON b.tenant_id = t.id
                WHERE b.bridge_token = :bridge_token
            """, {"bridge_token": bridge_token})
            
            bridge_data = cursor.fetchone()
            if not bridge_data:
                logger.warning(f"Invalid bridge token attempted: {bridge_token[:8]}...")
                return None
            
            bridge_dict = dict(bridge_data)
            bridge_id = bridge_dict['id']
            
            # Update bridge status
            self.db.session.execute("""
                UPDATE device_bridges 
                SET connection_status = 'connected', last_seen = CURRENT_TIMESTAMP
                WHERE id = :bridge_id
            """, {"bridge_id": bridge_id})
            
            # Store active connection
            self.active_bridges[bridge_id] = {
                'bridge_data': bridge_dict,
                'connection_info': connection_info,
                'connected_at': datetime.utcnow(),
                'devices': {}
            }
            
            self.db.session.commit()
            
            logger.info(f"Bridge registered: {bridge_name} (ID: {bridge_id}) for tenant {bridge_dict['tenant_name']}")
            
            return bridge_dict
            
        except Exception as e:
            logger.error(f"Error registering bridge: {e}")
            self.db.session.rollback()
            return None
    
    def unregister_bridge(self, bridge_id):
        """Unregister a device bridge connection"""
        try:
            # Update bridge status
            self.db.session.execute("""
                UPDATE device_bridges 
                SET connection_status = 'disconnected'
                WHERE id = :bridge_id
            """, {"bridge_id": bridge_id})
            
            # Update all devices from this bridge
            self.db.session.execute("""
                UPDATE tenant_devices 
                SET status = 'offline', last_seen = CURRENT_TIMESTAMP
                WHERE bridge_id = :bridge_id
            """, {"bridge_id": bridge_id})
            
            # Remove from active bridges
            if bridge_id in self.active_bridges:
                bridge_info = self.active_bridges[bridge_id]
                logger.info(f"Bridge disconnected: {bridge_info['bridge_data']['bridge_name']}")
                del self.active_bridges[bridge_id]
            
            self.db.session.commit()
            
        except Exception as e:
            logger.error(f"Error unregistering bridge: {e}")
            self.db.session.rollback()
    
    def update_device_list(self, bridge_id, devices):
        """Update device list from a bridge"""
        try:
            if bridge_id not in self.active_bridges:
                logger.warning(f"Received device update from unregistered bridge: {bridge_id}")
                return False
            
            bridge_info = self.active_bridges[bridge_id]
            tenant_id = bridge_info['bridge_data']['tenant_id']
            
            # Get current devices for this bridge
            cursor = self.db.session.execute("""
                SELECT device_id, device_name, platform, status
                FROM tenant_devices
                WHERE bridge_id = :bridge_id
            """, {"bridge_id": bridge_id})
            
            current_devices = {row['device_id']: dict(row) for row in cursor.fetchall()}
            
            # Process incoming devices
            updated_devices = set()
            
            for device in devices:
                device_id = device.get('device_id')
                device_name = device.get('device_name')
                platform = device.get('platform')
                device_info = device.get('device_info', {})
                
                if not device_id or not device_name or not platform:
                    logger.warning(f"Invalid device data received: {device}")
                    continue
                
                updated_devices.add(device_id)
                
                if device_id in current_devices:
                    # Update existing device
                    self.db.session.execute("""
                        UPDATE tenant_devices 
                        SET device_name = :device_name, device_info = :device_info,
                            status = 'connected', last_seen = CURRENT_TIMESTAMP
                        WHERE device_id = :device_id AND bridge_id = :bridge_id
                    """, {
                        "device_id": device_id,
                        "device_name": device_name,
                        "device_info": json.dumps(device_info),
                        "bridge_id": bridge_id
                    })
                else:
                    # Add new device
                    self.db.session.execute("""
                        INSERT INTO tenant_devices (
                            tenant_id, bridge_id, device_id, device_name, 
                            platform, device_info, status
                        ) VALUES (
                            :tenant_id, :bridge_id, :device_id, :device_name,
                            :platform, :device_info, :status
                        )
                    """, {
                        "tenant_id": tenant_id,
                        "bridge_id": bridge_id,
                        "device_id": device_id,
                        "device_name": device_name,
                        "platform": platform,
                        "device_info": json.dumps(device_info),
                        "status": "connected"
                    })
            
            # Mark missing devices as disconnected
            for device_id, device_data in current_devices.items():
                if device_id not in updated_devices:
                    self.db.session.execute("""
                        UPDATE tenant_devices 
                        SET status = 'disconnected', last_seen = CURRENT_TIMESTAMP
                        WHERE device_id = :device_id AND bridge_id = :bridge_id
                    """, {"device_id": device_id, "bridge_id": bridge_id})
            
            # Update bridge device cache
            bridge_info['devices'] = {d['device_id']: d for d in devices}
            
            self.db.session.commit()
            
            # Notify tenant users about device changes
            self.notify_device_changes(tenant_id)
            
            logger.info(f"Updated {len(devices)} devices for bridge {bridge_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating device list: {e}")
            self.db.session.rollback()
            return False
    
    def send_command_to_device(self, device_id, command, parameters=None):
        """Send command to a specific device via its bridge"""
        try:
            # Get device and bridge info
            cursor = self.db.session.execute("""
                SELECT d.*, b.id as bridge_id, b.bridge_name
                FROM tenant_devices d
                JOIN device_bridges b ON d.bridge_id = b.id
                WHERE d.device_id = :device_id AND d.tenant_id = :tenant_id
            """, {"device_id": device_id, "tenant_id": g.tenant_id})
            
            device_data = cursor.fetchone()
            if not device_data:
                logger.warning(f"Device not found: {device_id}")
                return None
            
            device_dict = dict(device_data)
            bridge_id = device_dict['bridge_id']
            
            # Check if bridge is connected
            if bridge_id not in self.active_bridges:
                logger.warning(f"Bridge not connected for device {device_id}")
                return None
            
            # Prepare command
            command_data = {
                'command_id': str(uuid.uuid4()),
                'device_id': device_id,
                'command': command,
                'parameters': parameters or {},
                'timestamp': datetime.utcnow().isoformat()
            }
            
            # Send command via WebSocket
            bridge_info = self.active_bridges[bridge_id]
            connection_info = bridge_info['connection_info']
            
            # Emit to specific bridge session
            self.socketio.emit(
                'device_command',
                command_data,
                room=connection_info.get('session_id')
            )
            
            logger.info(f"Command '{command}' sent to device {device_id} via bridge {bridge_id}")
            
            return command_data['command_id']
            
        except Exception as e:
            logger.error(f"Error sending command to device: {e}")
            return None
    
    def handle_command_response(self, bridge_id, response_data):
        """Handle command response from device bridge"""
        try:
            command_id = response_data.get('command_id')
            device_id = response_data.get('device_id')
            status = response_data.get('status')
            result = response_data.get('result')
            error = response_data.get('error')
            
            if bridge_id not in self.active_bridges:
                logger.warning(f"Received response from unregistered bridge: {bridge_id}")
                return
            
            bridge_info = self.active_bridges[bridge_id]
            tenant_id = bridge_info['bridge_data']['tenant_id']
            
            # Log command execution
            self.db.session.execute("""
                INSERT INTO command_executions (
                    tenant_id, device_id, command_id, status, result, error
                ) VALUES (
                    :tenant_id, :device_id, :command_id, :status, :result, :error
                )
            """, {
                "tenant_id": tenant_id,
                "device_id": device_id,
                "command_id": command_id,
                "status": status,
                "result": json.dumps(result) if result else None,
                "error": error
            })
            
            self.db.session.commit()
            
            # Notify tenant users about command completion
            self.notify_command_completion(tenant_id, command_id, response_data)
            
            logger.info(f"Command response processed: {command_id} - {status}")
            
        except Exception as e:
            logger.error(f"Error handling command response: {e}")
            self.db.session.rollback()
    
    def get_tenant_devices(self, tenant_id, platform=None):
        """Get devices for a specific tenant"""
        try:
            query = """
                SELECT d.*, b.bridge_name, b.connection_status as bridge_status
                FROM tenant_devices d
                LEFT JOIN device_bridges b ON d.bridge_id = b.id
                WHERE d.tenant_id = :tenant_id
            """
            
            params = {"tenant_id": tenant_id}
            
            if platform:
                query += " AND d.platform = :platform"
                params["platform"] = platform
            
            query += " ORDER BY d.last_seen DESC"
            
            cursor = self.db.session.execute(query, params)
            devices = [dict(row) for row in cursor.fetchall()]
            
            return devices
            
        except Exception as e:
            logger.error(f"Error getting tenant devices: {e}")
            return []
    
    def notify_device_changes(self, tenant_id):
        """Notify tenant users about device changes"""
        try:
            # Get updated device list
            devices = self.get_tenant_devices(tenant_id)
            
            # Emit to tenant room
            self.socketio.emit(
                'devices_updated',
                {'devices': devices},
                room=f"tenant_{tenant_id}"
            )
            
        except Exception as e:
            logger.error(f"Error notifying device changes: {e}")
    
    def notify_command_completion(self, tenant_id, command_id, response_data):
        """Notify tenant users about command completion"""
        try:
            self.socketio.emit(
                'command_completed',
                {
                    'command_id': command_id,
                    'response': response_data
                },
                room=f"tenant_{tenant_id}"
            )
            
        except Exception as e:
            logger.error(f"Error notifying command completion: {e}")

    def send_device_command(self, tenant_id, device_udid, command, parameters=None):
        """Send command to a specific device through the bridge"""
        try:
            # Find the device and its bridge
            cursor = self.db.session.execute("""
                SELECT d.*, b.id as bridge_id, b.bridge_name
                FROM tenant_devices d
                LEFT JOIN device_bridges b ON d.bridge_id = b.id
                WHERE d.tenant_id = :tenant_id AND d.device_id = :device_udid
            """, {"tenant_id": tenant_id, "device_udid": device_udid})

            device_data = cursor.fetchone()
            if not device_data:
                return {
                    'status': 'error',
                    'message': f'Device {device_udid} not found for tenant'
                }

            device_dict = dict(device_data)
            bridge_id = device_dict['bridge_id']

            if not bridge_id or bridge_id not in self.active_bridges:
                return {
                    'status': 'error',
                    'message': 'Device bridge not connected'
                }

            # Generate command ID
            command_id = str(uuid.uuid4())

            # Create command record
            self.db.session.execute("""
                INSERT INTO device_commands (
                    tenant_id, device_id, bridge_id, command_id,
                    command_type, parameters, status
                ) VALUES (
                    :tenant_id, :device_id, :bridge_id, :command_id,
                    :command_type, :parameters, :status
                )
            """, {
                "tenant_id": tenant_id,
                "device_id": device_udid,
                "bridge_id": bridge_id,
                "command_id": command_id,
                "command_type": command,
                "parameters": json.dumps(parameters or {}),
                "status": "pending"
            })

            # Send command to bridge via WebSocket
            command_message = {
                'type': 'device_command',
                'command_id': command_id,
                'device_udid': device_udid,
                'command': command,
                'parameters': parameters or {}
            }

            self.socketio.emit(
                'device_command',
                command_message,
                room=f"bridge_{bridge_id}"
            )

            self.db.session.commit()

            return {
                'status': 'success',
                'command_id': command_id,
                'message': f'Command {command} sent to device {device_udid}'
            }

        except Exception as e:
            logger.error(f"Error sending device command: {e}")
            self.db.session.rollback()
            return {
                'status': 'error',
                'message': str(e)
            }

    def get_device_status(self, tenant_id, device_udid):
        """Get status of a specific device"""
        try:
            cursor = self.db.session.execute("""
                SELECT d.*, b.bridge_name, b.connection_status as bridge_status
                FROM tenant_devices d
                LEFT JOIN device_bridges b ON d.bridge_id = b.id
                WHERE d.tenant_id = :tenant_id AND d.device_id = :device_udid
            """, {"tenant_id": tenant_id, "device_udid": device_udid})

            device_data = cursor.fetchone()
            if not device_data:
                return {
                    'status': 'not_found',
                    'message': f'Device {device_udid} not found'
                }

            device_dict = dict(device_data)

            return {
                'status': 'success',
                'device': {
                    'udid': device_dict['device_id'],
                    'name': device_dict['device_name'],
                    'platform': device_dict['platform'],
                    'device_status': device_dict['status'],
                    'bridge_status': device_dict['bridge_status'],
                    'last_seen': device_dict['last_seen'].isoformat() if device_dict['last_seen'] else None
                }
            }

        except Exception as e:
            logger.error(f"Error getting device status: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }

    def start_test_session(self, tenant_id, device_udid, test_config):
        """Start a test session on a specific device"""
        try:
            # Generate session ID
            session_id = str(uuid.uuid4())

            # Send start test command
            result = self.send_device_command(
                tenant_id, device_udid, 'start_test', {
                    'session_id': session_id,
                    'test_config': test_config
                }
            )

            if result['status'] == 'success':
                result['session_id'] = session_id

            return result

        except Exception as e:
            logger.error(f"Error starting test session: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }

    def stop_test_session(self, tenant_id, device_udid, session_id):
        """Stop a test session on a specific device"""
        try:
            # Send stop test command
            result = self.send_device_command(
                tenant_id, device_udid, 'stop_test', {
                    'session_id': session_id
                }
            )

            return result

        except Exception as e:
            logger.error(f"Error stopping test session: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }

    def get_test_logs(self, tenant_id, device_udid, session_id):
        """Get test logs for a specific session"""
        try:
            # For now, return placeholder logs
            # This would need to be implemented to retrieve actual logs
            return {
                'status': 'success',
                'logs': [
                    {
                        'timestamp': datetime.utcnow().isoformat(),
                        'level': 'info',
                        'message': f'Test session {session_id} logs for device {device_udid}'
                    }
                ]
            }

        except Exception as e:
            logger.error(f"Error getting test logs: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
    
    def cleanup_stale_connections(self):
        """Clean up stale bridge connections and devices"""
        try:
            # Mark bridges as disconnected if not seen for 5 minutes
            cutoff_time = datetime.utcnow() - timedelta(minutes=5)
            
            self.db.session.execute("""
                UPDATE device_bridges 
                SET connection_status = 'disconnected'
                WHERE connection_status = 'connected' 
                AND last_seen < :cutoff_time
            """, {"cutoff_time": cutoff_time})
            
            # Mark devices as offline if not seen for 2 minutes
            device_cutoff = datetime.utcnow() - timedelta(minutes=2)
            
            self.db.session.execute("""
                UPDATE tenant_devices 
                SET status = 'offline'
                WHERE status IN ('connected', 'busy') 
                AND last_seen < :cutoff_time
            """, {"cutoff_time": device_cutoff})
            
            self.db.session.commit()
            
            # Clean up active bridges cache
            stale_bridges = []
            for bridge_id, bridge_info in self.active_bridges.items():
                if bridge_info['connected_at'] < cutoff_time:
                    stale_bridges.append(bridge_id)
            
            for bridge_id in stale_bridges:
                del self.active_bridges[bridge_id]
                logger.info(f"Cleaned up stale bridge connection: {bridge_id}")
            
        except Exception as e:
            logger.error(f"Error cleaning up stale connections: {e}")
            self.db.session.rollback()

def create_device_manager(db, socketio):
    """Factory function to create device manager"""
    return DeviceManager(db, socketio)
