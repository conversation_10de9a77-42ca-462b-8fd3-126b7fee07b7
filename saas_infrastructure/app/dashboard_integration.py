#!/usr/bin/env python3
"""
Dashboard Integration Module for Multi-Tenant SaaS Platform
Integrates existing iOS and Android automation dashboards into the multi-tenant architecture
"""

import os
import sys
import json
import logging
from pathlib import Path
from flask import Blueprint, render_template, request, jsonify, g, session, redirect, url_for, send_from_directory
from flask_jwt_extended import verify_jwt_in_request, get_jwt_identity, get_jwt

# Setup logging
logger = logging.getLogger(__name__)

class DashboardIntegrator:
    def __init__(self, app, db):
        self.app = app
        self.db = db
        self.setup_blueprints()

    def get_tenant_by_subdomain(self, subdomain):
        """Get tenant by subdomain"""
        try:
            from sqlalchemy import text
            cursor = self.db.session.execute(
                text("SELECT id, name, subdomain, subscription_tier, is_active FROM tenants WHERE subdomain = :subdomain AND is_active = true"),
                {"subdomain": subdomain}
            )
            row = cursor.fetchone()
            if row:
                return {
                    'id': row[0],
                    'name': row[1],
                    'subdomain': row[2],
                    'subscription_tier': row[3],
                    'is_active': row[4]
                }
            return None
        except Exception as e:
            logger.error(f"Error getting tenant by subdomain: {e}")
            return None
        
    def setup_blueprints(self):
        """Setup dashboard blueprints for iOS and Android"""
        logger.info("Setting up dashboard blueprints...")

        # iOS Dashboard Blueprint
        self.ios_dashboard = Blueprint(
            'ios_dashboard',
            __name__,
            url_prefix='/dashboard/ios'
        )
        logger.info("iOS dashboard blueprint created")

        # Android Dashboard Blueprint
        self.android_dashboard = Blueprint(
            'android_dashboard',
            __name__,
            url_prefix='/dashboard/android'
        )
        logger.info("Android dashboard blueprint created")

        # Setup routes for both dashboards
        self.setup_ios_routes()
        self.setup_android_routes()
        logger.info("Dashboard routes setup completed")

        # Register blueprints with the main app
        self.app.register_blueprint(self.ios_dashboard)
        logger.info("iOS dashboard blueprint registered")
        self.app.register_blueprint(self.android_dashboard)
        logger.info("Android dashboard blueprint registered")
        logger.info("Dashboard blueprints setup completed successfully")
    
    def get_ios_template_path(self):
        """Get iOS templates path"""
        # From saas_infrastructure/app/dashboard_integration.py, go to root, then to app/templates
        path = str(Path(__file__).parent.parent.parent / "app" / "templates")
        logger.info(f"iOS template path: {path}")
        return path

    def get_ios_static_path(self):
        """Get iOS static files path"""
        path = str(Path(__file__).parent.parent.parent / "app" / "static")
        logger.info(f"iOS static path: {path}")
        return path

    def get_android_template_path(self):
        """Get Android templates path"""
        path = str(Path(__file__).parent.parent.parent / "app_android" / "templates")
        logger.info(f"Android template path: {path}")
        return path

    def get_android_static_path(self):
        """Get Android static files path"""
        path = str(Path(__file__).parent.parent.parent / "app_android" / "static")
        logger.info(f"Android static path: {path}")
        return path
    
    def require_tenant_auth(self, f):
        """Decorator to require tenant authentication (supports both JWT and session)"""
        def decorated_function(*args, **kwargs):
            # Check if tenant context is set, if not try to set it from query parameter
            if not hasattr(g, 'tenant_id') or not g.tenant_id:
                # Try to get tenant from query parameter (for iframe requests)
                tenant_subdomain = request.args.get('tenant')
                if tenant_subdomain:
                    tenant = self.get_tenant_by_subdomain(tenant_subdomain)
                    if tenant:
                        g.tenant_id = tenant['id']
                        g.tenant_data = tenant
                        logger.info(f"Tenant context set from query param: {tenant['name']} (ID: {tenant['id']})")
                    else:
                        return jsonify({'error': f'Tenant "{tenant_subdomain}" not found'}), 404
                else:
                    return jsonify({'error': 'Tenant not found'}), 404

            # Try JWT authentication first
            try:
                verify_jwt_in_request(optional=True)
                user_id = get_jwt_identity()
                if user_id:
                    claims = get_jwt()

                    # Verify tenant matches
                    if claims.get('tenant_id') != str(g.tenant_id):
                        return jsonify({'error': 'Invalid tenant access'}), 403

                    # Set user context
                    g.user_id = user_id
                    g.user_role = claims.get('user_role')

                    return f(*args, **kwargs)
            except:
                pass

            # Try session authentication for browser requests
            # Check if session has authentication for any tenant (for iframe requests)
            if session.get('authenticated'):
                # For iframe requests, allow if user is authenticated for any tenant
                # and the requested tenant exists
                if request.args.get('tenant') and hasattr(g, 'tenant_data') and g.tenant_data:
                    g.user_id = session.get('user_id')
                    g.user_role = session.get('user_role', 'user')
                    logger.info(f"Iframe request authenticated for tenant: {g.tenant_data['name']}")
                    return f(*args, **kwargs)
                # For direct requests, require exact tenant match
                elif session.get('tenant_id') == str(g.tenant_id):
                    g.user_id = session.get('user_id')
                    g.user_role = session.get('user_role', 'user')
                    return f(*args, **kwargs)

            # No valid authentication found
            return jsonify({'error': 'Authentication required'}), 401

        decorated_function.__name__ = f.__name__
        return decorated_function
    
    def setup_ios_routes(self):
        """Setup iOS dashboard routes"""
        
        @self.ios_dashboard.route('/')
        def ios_index():
            """iOS dashboard main page"""
            logger.info("iOS dashboard index route called")
            try:
                # Import and render the actual iOS application template
                return self.render_ios_dashboard()
            except Exception as e:
                logger.error(f"Error rendering iOS dashboard: {e}")
                return f"<h1>iOS Dashboard</h1><p>Error loading dashboard: {str(e)}</p>", 500

        @self.ios_dashboard.route('/test')
        def ios_test():
            """iOS dashboard test route"""
            logger.info("iOS dashboard test route called")
            return "iOS Test Route Working"

        @self.ios_dashboard.route('/static/<path:filename>')
        def ios_static(filename):
            """Serve iOS static files"""
            try:
                static_path = Path(__file__).parent.parent.parent / "app" / "static"
                return send_from_directory(str(static_path), filename)
            except Exception as e:
                logger.error(f"Error serving iOS static file {filename}: {e}")
                return "File not found", 404
        
        @self.ios_dashboard.route('/api/devices')
        @self.require_tenant_auth
        def ios_devices():
            """Get iOS devices for current tenant"""
            try:
                devices = self.get_tenant_devices('ios')
                return jsonify({'devices': devices})
            except Exception as e:
                logger.error(f"Error getting iOS devices: {e}")
                return jsonify({'error': 'Failed to get devices'}), 500
        
        @self.ios_dashboard.route('/api/screenshot/<device_id>')
        @self.require_tenant_auth
        def ios_screenshot(device_id):
            """Take screenshot of iOS device"""
            try:
                # Verify device belongs to tenant
                device = self.get_tenant_device(device_id)
                if not device or device['platform'] != 'ios':
                    return jsonify({'error': 'Device not found'}), 404
                
                # Request screenshot from device bridge
                screenshot_data = self.request_device_screenshot(device_id)
                
                if screenshot_data:
                    return jsonify({'screenshot': screenshot_data})
                else:
                    return jsonify({'error': 'Failed to capture screenshot'}), 500
                    
            except Exception as e:
                logger.error(f"Error taking iOS screenshot: {e}")
                return jsonify({'error': 'Screenshot failed'}), 500
        
        @self.ios_dashboard.route('/api/test/run', methods=['POST'])
        @self.require_tenant_auth
        def ios_run_test():
            """Run test on iOS device"""
            try:
                data = request.get_json()
                device_id = data.get('device_id')
                test_config = data.get('test_config')
                
                # Verify device belongs to tenant
                device = self.get_tenant_device(device_id)
                if not device or device['platform'] != 'ios':
                    return jsonify({'error': 'Device not found'}), 404
                
                # Check subscription limits
                if not self.check_test_execution_limits():
                    return jsonify({'error': 'Test execution limit reached'}), 429
                
                # Start test execution
                execution_id = self.start_test_execution(device_id, test_config, 'ios')
                
                return jsonify({
                    'execution_id': execution_id,
                    'status': 'started',
                    'message': 'Test execution started'
                })
                
            except Exception as e:
                logger.error(f"Error running iOS test: {e}")
                return jsonify({'error': 'Test execution failed'}), 500
    
    def setup_android_routes(self):
        """Setup Android dashboard routes"""
        
        @self.android_dashboard.route('/')
        def android_index():
            """Android dashboard main page"""
            logger.info("Android dashboard index route called")
            try:
                # Import and render the actual Android application template
                return self.render_android_dashboard()
            except Exception as e:
                logger.error(f"Error rendering Android dashboard: {e}")
                return f"<h1>Android Dashboard</h1><p>Error loading dashboard: {str(e)}</p>", 500

        @self.android_dashboard.route('/test')
        def android_test():
            """Android dashboard test route"""
            return "Android Test Route Working"

        @self.android_dashboard.route('/static/<path:filename>')
        def android_static(filename):
            """Serve Android static files"""
            try:
                static_path = Path(__file__).parent.parent.parent / "app_android" / "static"
                return send_from_directory(str(static_path), filename)
            except Exception as e:
                logger.error(f"Error serving Android static file {filename}: {e}")
                return "File not found", 404
        
        @self.android_dashboard.route('/api/devices')
        @self.require_tenant_auth
        def android_devices():
            """Get Android devices for current tenant"""
            try:
                devices = self.get_tenant_devices('android')
                return jsonify({'devices': devices})
            except Exception as e:
                logger.error(f"Error getting Android devices: {e}")
                return jsonify({'error': 'Failed to get devices'}), 500
        
        @self.android_dashboard.route('/api/screenshot/<device_id>')
        @self.require_tenant_auth
        def android_screenshot(device_id):
            """Take screenshot of Android device"""
            try:
                # Verify device belongs to tenant
                device = self.get_tenant_device(device_id)
                if not device or device['platform'] != 'android':
                    return jsonify({'error': 'Device not found'}), 404
                
                # Request screenshot from device bridge
                screenshot_data = self.request_device_screenshot(device_id)
                
                if screenshot_data:
                    return jsonify({'screenshot': screenshot_data})
                else:
                    return jsonify({'error': 'Failed to capture screenshot'}), 500
                    
            except Exception as e:
                logger.error(f"Error taking Android screenshot: {e}")
                return jsonify({'error': 'Screenshot failed'}), 500
        
        @self.android_dashboard.route('/api/test/run', methods=['POST'])
        @self.require_tenant_auth
        def android_run_test():
            """Run test on Android device"""
            try:
                data = request.get_json()
                device_id = data.get('device_id')
                test_config = data.get('test_config')
                
                # Verify device belongs to tenant
                device = self.get_tenant_device(device_id)
                if not device or device['platform'] != 'android':
                    return jsonify({'error': 'Device not found'}), 404
                
                # Check subscription limits
                if not self.check_test_execution_limits():
                    return jsonify({'error': 'Test execution limit reached'}), 429
                
                # Start test execution
                execution_id = self.start_test_execution(device_id, test_config, 'android')
                
                return jsonify({
                    'execution_id': execution_id,
                    'status': 'started',
                    'message': 'Test execution started'
                })
                
            except Exception as e:
                logger.error(f"Error running Android test: {e}")
                return jsonify({'error': 'Test execution failed'}), 500
    
    def get_tenant_devices(self, platform=None):
        """Get devices for current tenant"""
        try:
            query = """
                SELECT d.*, b.bridge_name, b.connection_status as bridge_status
                FROM tenant_devices d
                LEFT JOIN device_bridges b ON d.bridge_id = b.id
                WHERE d.tenant_id = :tenant_id
            """
            
            params = {"tenant_id": g.tenant_id}
            
            if platform:
                query += " AND d.platform = :platform"
                params["platform"] = platform
            
            query += " ORDER BY d.last_seen DESC"
            
            cursor = self.db.session.execute(query, params)
            devices = [dict(row) for row in cursor.fetchall()]
            
            return devices
            
        except Exception as e:
            logger.error(f"Error getting tenant devices: {e}")
            return []
    
    def get_tenant_device(self, device_id):
        """Get specific device for current tenant"""
        try:
            cursor = self.db.session.execute("""
                SELECT d.*, b.bridge_name, b.connection_status as bridge_status
                FROM tenant_devices d
                LEFT JOIN device_bridges b ON d.bridge_id = b.id
                WHERE d.id = :device_id AND d.tenant_id = :tenant_id
            """, {"device_id": device_id, "tenant_id": g.tenant_id})
            
            result = cursor.fetchone()
            return dict(result) if result else None
            
        except Exception as e:
            logger.error(f"Error getting tenant device: {e}")
            return None
    
    def get_recent_test_executions(self, platform=None, limit=10):
        """Get recent test executions for current tenant"""
        try:
            query = """
                SELECT te.*, d.device_name, d.platform
                FROM test_executions te
                LEFT JOIN tenant_devices d ON te.device_id = d.id
                WHERE te.tenant_id = :tenant_id
            """
            
            params = {"tenant_id": g.tenant_id}
            
            if platform:
                query += " AND d.platform = :platform"
                params["platform"] = platform
            
            query += " ORDER BY te.started_at DESC LIMIT :limit"
            params["limit"] = limit
            
            cursor = self.db.session.execute(query, params)
            executions = [dict(row) for row in cursor.fetchall()]
            
            return executions
            
        except Exception as e:
            logger.error(f"Error getting recent test executions: {e}")
            return []
    
    def request_device_screenshot(self, device_id):
        """Request screenshot from device bridge"""
        try:
            # Get device info
            device = self.get_tenant_device(device_id)
            if not device:
                return None
            
            # Send screenshot command via WebSocket to bridge
            # This would integrate with the WebSocket system in saas_app.py
            # For now, return a placeholder
            logger.info(f"Screenshot requested for device {device_id}")
            
            # In production, this would:
            # 1. Send WebSocket message to device bridge
            # 2. Wait for response with screenshot data
            # 3. Return base64 encoded image
            
            return None  # Placeholder
            
        except Exception as e:
            logger.error(f"Error requesting device screenshot: {e}")
            return None
    
    def check_test_execution_limits(self):
        """Check if tenant can execute more tests based on subscription"""
        try:
            # Get current month usage
            cursor = self.db.session.execute("""
                SELECT metric_value FROM usage_tracking
                WHERE tenant_id = :tenant_id 
                AND metric_type = 'test_minutes'
                AND billing_period = DATE_TRUNC('month', CURRENT_DATE)::DATE
            """, {"tenant_id": g.tenant_id})
            
            result = cursor.fetchone()
            current_usage = float(result['metric_value']) if result else 0.0
            
            # Get tenant limits
            max_minutes = g.tenant_data.get('max_test_minutes', 0)
            
            return current_usage < max_minutes
            
        except Exception as e:
            logger.error(f"Error checking test execution limits: {e}")
            return False
    
    def start_test_execution(self, device_id, test_config, platform):
        """Start test execution and track it"""
        try:
            # Create test execution record
            cursor = self.db.session.execute("""
                INSERT INTO test_executions (
                    tenant_id, user_id, device_id, test_name, test_config,
                    platform, status
                ) VALUES (
                    :tenant_id, :user_id, :device_id, :test_name, :test_config,
                    :platform, :status
                ) RETURNING id
            """, {
                "tenant_id": g.tenant_id,
                "user_id": g.user_id,
                "device_id": device_id,
                "test_name": test_config.get('test_name', 'Unnamed Test'),
                "test_config": json.dumps(test_config),
                "platform": platform,
                "status": 'running'
            })
            
            execution_id = cursor.fetchone()['id']
            self.db.session.commit()
            
            # Send test execution command to device bridge
            # This would integrate with the WebSocket system
            logger.info(f"Test execution {execution_id} started for device {device_id}")
            
            # Log audit event
            self.log_audit_event(
                g.user_id, 'test_started', 'test_execution', execution_id,
                {'device_id': device_id, 'platform': platform}
            )
            
            return execution_id
            
        except Exception as e:
            logger.error(f"Error starting test execution: {e}")
            self.db.session.rollback()
            raise
    
    def log_audit_event(self, user_id, action, resource_type=None, resource_id=None, details=None):
        """Log audit event"""
        try:
            self.db.session.execute("""
                INSERT INTO audit_logs (tenant_id, user_id, action, resource_type, resource_id, details, ip_address)
                VALUES (:tenant_id, :user_id, :action, :resource_type, :resource_id, :details, :ip_address)
            """, {
                "tenant_id": g.tenant_id,
                "user_id": user_id,
                "action": action,
                "resource_type": resource_type,
                "resource_id": resource_id,
                "details": json.dumps(details or {}),
                "ip_address": request.remote_addr
            })
            self.db.session.commit()
        except Exception as e:
            logger.error(f"Error logging audit event: {e}")

    def render_ios_dashboard(self):
        """Render the actual iOS automation dashboard"""
        try:
            # Use Flask's render_template to properly handle template variables
            from flask import render_template_string

            # Import the iOS app template
            ios_template_path = Path(__file__).parent.parent.parent / "app" / "templates" / "index.html"

            if ios_template_path.exists():
                with open(ios_template_path, 'r', encoding='utf-8') as f:
                    template_content = f.read()

                # Replace Flask template variables with dashboard-specific paths
                import re
                template_content = re.sub(
                    r"\{\{\s*url_for\('static',\s*filename='([^']+)'\)\s*\}\}",
                    r"/dashboard/ios/static/\1",
                    template_content
                )

                # Replace other common Flask template patterns (but avoid double replacement)
                template_content = re.sub(r'href="(?!/dashboard)/', 'href="/dashboard/ios/', template_content)
                template_content = re.sub(r'src="(?!/dashboard)/', 'src="/dashboard/ios/', template_content)
                template_content = re.sub(r"url\('(?!/dashboard)/", "url('/dashboard/ios/", template_content)

                return template_content
            else:
                return "<h1>iOS Dashboard</h1><p>Template not found</p>"

        except Exception as e:
            logger.error(f"Error rendering iOS dashboard: {e}")
            return f"<h1>iOS Dashboard</h1><p>Error: {str(e)}</p>"

    def render_android_dashboard(self):
        """Render the actual Android automation dashboard"""
        try:
            # Use Flask's render_template to properly handle template variables
            from flask import render_template_string

            # Import the Android app template
            android_template_path = Path(__file__).parent.parent.parent / "app_android" / "templates" / "index.html"

            if android_template_path.exists():
                with open(android_template_path, 'r', encoding='utf-8') as f:
                    template_content = f.read()

                # Replace Flask template variables with dashboard-specific paths
                import re
                template_content = re.sub(
                    r"\{\{\s*url_for\('static',\s*filename='([^']+)'\)\s*\}\}",
                    r"/dashboard/android/static/\1",
                    template_content
                )

                # Replace other common Flask template patterns (but avoid double replacement)
                template_content = re.sub(r'href="(?!/dashboard)/', 'href="/dashboard/android/', template_content)
                template_content = re.sub(r'src="(?!/dashboard)/', 'src="/dashboard/android/', template_content)
                template_content = re.sub(r"url\('(?!/dashboard)/", "url('/dashboard/android/", template_content)

                return template_content
            else:
                return "<h1>Android Dashboard</h1><p>Template not found</p>"

        except Exception as e:
            logger.error(f"Error rendering Android dashboard: {e}")
            return f"<h1>Android Dashboard</h1><p>Error: {str(e)}</p>"

def create_dashboard_integration(app, db):
    """Factory function to create dashboard integration"""
    return DashboardIntegrator(app, db)
