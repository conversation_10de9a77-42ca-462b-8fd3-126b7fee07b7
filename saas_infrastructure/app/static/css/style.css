/* Mobile Automation SaaS Platform - Custom Styles */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: #f8f9fa;
}

.main-content {
    min-height: calc(100vh - 200px);
}

/* Navigation */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9);
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: white;
}

/* Hero Section */
.hero-section {
    background: var(--gradient-primary);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

.hero-section .container {
    position: relative;
    z-index: 1;
}

.bg-gradient-primary {
    background: var(--gradient-primary) !important;
}

.feature-badges .badge {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
}

/* Tenant Cards */
.tenant-card {
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
}

.tenant-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.tenant-card .tenant-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background: -webkit-linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    color: white;
}

.tenant-select-btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.tenant-select-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

/* Login Page */
.login-container {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.login-container .card {
    border-radius: 16px;
    overflow: hidden;
}

.login-container .card-header {
    background: var(--gradient-primary) !important;
    border: none;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-control-lg {
    border-radius: 8px;
}

.btn-lg {
    border-radius: 8px;
    font-weight: 500;
}

/* Dashboard */
.dashboard-container .card {
    border-radius: 12px;
    border: none;
    box-shadow: var(--shadow-sm);
}

.dashboard-container .card-header {
    background: white;
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0 !important;
}

.nav-tabs {
    border-bottom: 2px solid #e9ecef;
}

.nav-tabs .nav-link {
    border: none;
    border-radius: 8px 8px 0 0;
    color: var(--secondary-color);
    font-weight: 500;
    margin-right: 0.5rem;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: var(--primary-color);
    background-color: #f8f9fa;
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    background-color: white;
    border-color: #e9ecef #e9ecef white;
    border-width: 2px 2px 2px 2px;
}

.tab-content {
    background: white;
    border-radius: 0 12px 12px 12px;
    box-shadow: var(--shadow-sm);
}

/* Stats Cards */
.dashboard-container .bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.dashboard-container .bg-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
}

.dashboard-container .bg-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
}

.dashboard-container .bg-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
}

/* Code Blocks */
code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.4;
}

.bg-dark code {
    color: #f8f9fa;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding: 3rem 0;
    }
    
    .hero-section .display-4 {
        font-size: 2rem;
    }
    
    .tenant-card {
        margin-bottom: 1rem;
    }
    
    .login-container {
        padding: 1rem;
    }
    
    .dashboard-container .col-md-3 {
        margin-bottom: 1rem;
    }
    
    .nav-tabs .nav-link {
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
}

@media (max-width: 576px) {
    .hero-section .feature-badges .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
        margin: 0.25rem;
    }
    
    .tenant-icon {
        width: 60px !important;
        height: 60px !important;
    }
    
    .tenant-icon i {
        font-size: 1.5rem !important;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Utility Classes */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-custom {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.border-radius-lg {
    border-radius: 12px !important;
}

.border-radius-xl {
    border-radius: 16px !important;
}
