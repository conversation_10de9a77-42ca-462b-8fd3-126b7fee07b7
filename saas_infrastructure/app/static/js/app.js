/**
 * Mobile Automation SaaS Platform - Main JavaScript
 */

// Global application object
window.MobileAutomationSaaS = {
    config: {
        apiBaseUrl: window.location.origin,
        tokenKey: 'access_token',
        refreshInterval: 30000 // 30 seconds
    },
    
    // Initialize the application
    init: function() {
        this.setupGlobalEventListeners();
        this.setupAjaxDefaults();
        this.checkAuthentication();
        console.log('Mobile Automation SaaS Platform initialized');
    },
    
    // Setup global event listeners
    setupGlobalEventListeners: function() {
        // Handle all form submissions with AJAX by default
        document.addEventListener('submit', this.handleFormSubmit.bind(this));
        
        // Handle navigation clicks
        document.addEventListener('click', this.handleNavigation.bind(this));
        
        // Handle window resize for responsive adjustments
        window.addEventListener('resize', this.handleResize.bind(this));
        
        // Handle visibility change for pausing/resuming updates
        document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    },
    
    // Setup AJAX defaults
    setupAjaxDefaults: function() {
        // Add default headers to all fetch requests
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
            options.headers = options.headers || {};
            
            // Add authentication token if available
            const token = localStorage.getItem('access_token');
            if (token && !options.headers['Authorization']) {
                options.headers['Authorization'] = `Bearer ${token}`;
            }
            
            // Add tenant header if available
            const tenant = MobileAutomationSaaS.getCurrentTenant();
            if (tenant && !options.headers['X-Tenant-Subdomain']) {
                options.headers['X-Tenant-Subdomain'] = tenant;
            }
            
            return originalFetch(url, options);
        };
    },
    
    // Check authentication status
    checkAuthentication: function() {
        const token = localStorage.getItem(this.config.tokenKey);
        if (token) {
            // Verify token is still valid
            this.verifyToken(token);
        }
    },
    
    // Verify authentication token
    verifyToken: function(token) {
        fetch('/api/auth/verify', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        })
        .then(response => {
            if (!response.ok) {
                this.logout();
            }
        })
        .catch(error => {
            console.error('Token verification failed:', error);
            this.logout();
        });
    },
    
    // Get current tenant from URL or context
    getCurrentTenant: function() {
        // Try to get from URL path
        const pathMatch = window.location.pathname.match(/\/tenant\/([^\/]+)/);
        if (pathMatch) {
            return pathMatch[1];
        }
        
        // Try to get from meta tag or global variable
        const metaTenant = document.querySelector('meta[name="tenant-subdomain"]');
        if (metaTenant) {
            return metaTenant.getAttribute('content');
        }
        
        return null;
    },
    
    // Handle form submissions
    handleFormSubmit: function(event) {
        const form = event.target;
        
        // Skip if form has data-no-ajax attribute
        if (form.hasAttribute('data-no-ajax')) {
            return;
        }
        
        // Skip if form is not for API endpoints
        if (!form.action.includes('/api/')) {
            return;
        }
        
        event.preventDefault();
        this.submitForm(form);
    },
    
    // Submit form via AJAX
    submitForm: function(form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
        submitBtn.disabled = true;
        
        fetch(form.action, {
            method: form.method || 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.access_token) {
                // Handle successful login
                localStorage.setItem(this.config.tokenKey, data.access_token);
                this.showMessage('Login successful! Redirecting...', 'success');
                
                setTimeout(() => {
                    const tenant = this.getCurrentTenant();
                    if (tenant) {
                        window.location.href = `/tenant/${tenant}`;
                    } else {
                        window.location.href = '/';
                    }
                }, 1000);
            } else if (data.error) {
                this.showMessage(data.error, 'danger');
            } else {
                this.showMessage('Operation completed successfully', 'success');
            }
        })
        .catch(error => {
            console.error('Form submission error:', error);
            this.showMessage('An error occurred. Please try again.', 'danger');
        })
        .finally(() => {
            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    },
    
    // Handle navigation clicks
    handleNavigation: function(event) {
        const link = event.target.closest('a[data-navigate]');
        if (link) {
            event.preventDefault();
            this.navigate(link.href);
        }
    },
    
    // Navigate to URL with loading state
    navigate: function(url) {
        // Show loading indicator
        this.showLoadingOverlay();
        
        // Navigate after short delay for UX
        setTimeout(() => {
            window.location.href = url;
        }, 300);
    },
    
    // Handle window resize
    handleResize: function() {
        // Adjust layout for mobile devices
        this.adjustMobileLayout();
    },
    
    // Handle visibility change
    handleVisibilityChange: function() {
        if (document.hidden) {
            // Page is hidden, pause updates
            this.pauseUpdates();
        } else {
            // Page is visible, resume updates
            this.resumeUpdates();
        }
    },
    
    // Show message to user
    showMessage: function(message, type = 'info') {
        const container = document.getElementById('message-container') || document.body;
        
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            <i class="fas fa-${this.getIconForType(type)} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        container.insertBefore(alertDiv, container.firstChild);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    },
    
    // Get icon for message type
    getIconForType: function(type) {
        const icons = {
            'success': 'check-circle',
            'danger': 'exclamation-triangle',
            'warning': 'exclamation-circle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    },
    
    // Show loading overlay
    showLoadingOverlay: function() {
        let overlay = document.getElementById('loading-overlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.id = 'loading-overlay';
            overlay.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center';
            overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            overlay.style.zIndex = '9999';
            overlay.innerHTML = `
                <div class="text-center text-white">
                    <div class="spinner-border mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div>Loading...</div>
                </div>
            `;
            document.body.appendChild(overlay);
        }
        overlay.style.display = 'flex';
    },
    
    // Hide loading overlay
    hideLoadingOverlay: function() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    },
    
    // Adjust layout for mobile
    adjustMobileLayout: function() {
        const isMobile = window.innerWidth < 768;
        document.body.classList.toggle('mobile-layout', isMobile);
    },
    
    // Pause periodic updates
    pauseUpdates: function() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    },
    
    // Resume periodic updates
    resumeUpdates: function() {
        if (!this.updateInterval && typeof window.loadDashboardData === 'function') {
            this.updateInterval = setInterval(window.loadDashboardData, this.config.refreshInterval);
        }
    },
    
    // Logout user
    logout: function() {
        localStorage.removeItem(this.config.tokenKey);
        window.location.href = '/';
    },
    
    // Utility function to format dates
    formatDate: function(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    },
    
    // Utility function to format file sizes
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    // Utility function to debounce function calls
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    MobileAutomationSaaS.init();
});

// Export for use in other scripts
window.App = MobileAutomationSaaS;
