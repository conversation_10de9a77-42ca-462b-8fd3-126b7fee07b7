# Multi-Tenant SaaS Flask Application Requirements

# Core Flask and extensions
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-JWT-Extended==4.5.3
Flask-SocketIO==5.3.6
Flask-CORS==4.0.0

# Database
psycopg2-binary==2.9.7
SQLAlchemy==2.0.21

# Authentication and security
bcrypt==4.0.1
cryptography==41.0.4

# WebSocket support
python-socketio==5.8.0
eventlet>=0.33.3

# HTTP and networking
requests==2.31.0
urllib3==2.0.4

# JSON and data handling
jsonschema==4.19.1

# Utilities
python-dotenv==1.0.0
click==8.1.7
setuptools>=65.0.0

# Production server
gunicorn==21.2.0

# Monitoring and logging
prometheus-client==0.17.1

# Development and testing (optional)
pytest==7.4.2
pytest-flask==1.2.0
pytest-cov==4.1.0
black==23.7.0
flake8==6.0.0
