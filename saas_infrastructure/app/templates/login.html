{% extends "base.html" %}

{% block title %}Login - {{ tenant.name }} - Mobile Automation SaaS{% endblock %}

{% block content %}
<div class="login-container">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-primary text-white text-center py-4">
                        <h4 class="mb-0">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            Sign In
                        </h4>
                        {% if tenant %}
                        <p class="mb-0 mt-2">
                            <i class="fas fa-building me-1"></i>
                            {{ tenant.name }}
                        </p>
                        {% endif %}
                    </div>
                    
                    <div class="card-body p-4">
                        <!-- Error/Success Messages -->
                        <div id="message-container"></div>
                        
                        <form id="loginForm">
                            {% if tenant %}
                            <input type="hidden" name="tenant_subdomain" value="{{ tenant.subdomain }}">
                            {% endif %}
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>
                                    Email Address
                                </label>
                                <input type="email" 
                                       class="form-control form-control-lg" 
                                       id="email" 
                                       name="email" 
                                       required 
                                       placeholder="Enter your email"
                                       autocomplete="email">
                                <div class="invalid-feedback"></div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    Password
                                </label>
                                <div class="input-group">
                                    <input type="password" 
                                           class="form-control form-control-lg" 
                                           id="password" 
                                           name="password" 
                                           required 
                                           placeholder="Enter your password"
                                           autocomplete="current-password">
                                    <button class="btn btn-outline-secondary" 
                                            type="button" 
                                            id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback"></div>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="rememberMe" name="remember_me">
                                <label class="form-check-label" for="rememberMe">
                                    Remember me
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-lg w-100 mb-3" id="loginBtn">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Sign In
                            </button>
                        </form>
                        
                        <div class="text-center">
                            <a href="#" class="text-muted text-decoration-none">
                                <i class="fas fa-question-circle me-1"></i>
                                Forgot your password?
                            </a>
                        </div>
                    </div>
                    
                    <div class="card-footer bg-light text-center py-3">
                        <small class="text-muted">
                            <a href="/" class="text-decoration-none">
                                <i class="fas fa-arrow-left me-1"></i>
                                Back to Organization Selection
                            </a>
                        </small>
                    </div>
                </div>
                
                <!-- Demo Credentials -->
                {% if tenant and tenant.subdomain in ['testcompany1', 'testcompany2'] %}
                <div class="card mt-4 border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Demo Credentials
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2"><strong>Email:</strong> testuser{{ '1' if tenant.subdomain == 'testcompany1' else '2' }}@example.com</p>
                        <p class="mb-0"><strong>Password:</strong> testpass123</p>
                        <button class="btn btn-sm btn-outline-info mt-2" onclick="fillDemoCredentials()">
                            <i class="fas fa-magic me-1"></i>
                            Use Demo Credentials
                        </button>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const loginBtn = document.getElementById('loginBtn');
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');
    const messageContainer = document.getElementById('message-container');
    
    // Toggle password visibility
    togglePassword.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);
        
        const icon = this.querySelector('i');
        icon.classList.toggle('fa-eye');
        icon.classList.toggle('fa-eye-slash');
    });
    
    // Handle form submission
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Clear previous messages
        messageContainer.innerHTML = '';
        
        // Show loading state
        loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Signing In...';
        loginBtn.disabled = true;
        
        // Get form data
        const formData = new FormData(loginForm);
        const data = {
            email: formData.get('email'),
            password: formData.get('password')
        };
        
        // Set tenant header
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,application/json;q=0.8,*/*;q=0.7'
        };

        {% if tenant %}
        headers['X-Tenant-Subdomain'] = '{{ tenant.subdomain }}';
        {% endif %}
        
        // Submit login request
        fetch('/api/auth/login', {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(data)
        })
        .then(response => {
            console.log('Response status:', response.status); // Debug log
            console.log('Response headers:', response.headers); // Debug log

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return response.json();
        })
        .then(data => {
            console.log('Login response data:', data); // Debug log

            // Check if login was successful
            if (data && data.success === true) {
                console.log('Login successful, processing redirect...'); // Debug log

                // Login was successful
                showMessage('Login successful! Redirecting...', 'success');

                // Determine redirect URL
                let redirectUrl;
                if (data.redirect_url) {
                    // Use the redirect URL provided by server
                    redirectUrl = data.redirect_url;
                    console.log('Using server redirect URL:', redirectUrl); // Debug log
                } else if (data.tenant && data.tenant.subdomain) {
                    // Construct redirect URL from tenant data
                    redirectUrl = '/tenant/' + data.tenant.subdomain;
                    console.log('Using tenant subdomain:', redirectUrl); // Debug log
                } else {
                    // Fallback to tenant subdomain from template
                    {% if tenant %}
                    redirectUrl = '/tenant/{{ tenant.subdomain }}';
                    {% else %}
                    redirectUrl = '/';
                    {% endif %}
                    console.log('Using template fallback:', redirectUrl); // Debug log
                }

                // Store access token if provided (for API compatibility)
                if (data.access_token) {
                    localStorage.setItem('access_token', data.access_token);
                    console.log('Access token stored'); // Debug log
                }

                console.log('Final redirect URL:', redirectUrl); // Debug log
                setTimeout(() => {
                    console.log('Executing redirect...'); // Debug log
                    window.location.href = redirectUrl;
                }, 1000);
            } else {
                console.log('Login failed, data.success =', data ? data.success : 'undefined'); // Debug log
                showMessage(data && data.error ? data.error : 'Login failed. Please try again.', 'danger');
                resetLoginButton();
            }
        })
        .catch(error => {
            console.error('Login error:', error);
            showMessage('An error occurred. Please try again.', 'danger');
            resetLoginButton();
        });
    });
    
    function resetLoginButton() {
        loginBtn.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>Sign In';
        loginBtn.disabled = false;
    }
    
    function showMessage(message, type) {
        messageContainer.innerHTML = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
    }
});

// Function to fill demo credentials
function fillDemoCredentials() {
    {% if tenant %}
    document.getElementById('email').value = 'testuser{{ '1' if tenant.subdomain == 'testcompany1' else '2' }}@example.com';
    {% endif %}
    document.getElementById('password').value = 'testpass123';
}
</script>
{% endblock %}
