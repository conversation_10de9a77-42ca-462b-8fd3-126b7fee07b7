{% extends "base.html" %}

{% block title %}Mobile Automation Dashboard - {{ tenant.name }}{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Dashboard Header -->
    <div class="bg-primary text-white py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h3 mb-1">
                        <i class="fas fa-mobile-alt me-2"></i>
                        Mobile Automation Platform
                    </h1>
                    <p class="mb-0 opacity-75">
                        {{ tenant.name }} - Choose your automation platform
                    </p>
                </div>
                <div class="col-md-4 text-md-end">
                    <span class="badge bg-light text-primary px-3 py-2">
                        <i class="fas fa-crown me-1"></i>
                        {{ tenant.subscription_tier|title }} Plan
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Platform Selection Cards -->
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="text-center mb-5">
                    <h2 class="h4 text-muted mb-3">Select Your Automation Platform</h2>
                    <p class="text-muted">Choose iOS or Android to access your dedicated automation interface</p>
                </div>

                <div class="row g-4">
                    <!-- iOS Automation Card -->
                    <div class="col-md-6">
                        <div class="card h-100 border-0 shadow-sm automation-card" data-platform="ios">
                            <div class="card-body text-center p-5">
                                <div class="mb-4">
                                    <i class="fab fa-apple display-1 text-primary"></i>
                                </div>
                                <h3 class="h4 mb-3">iOS Automation</h3>
                                <p class="text-muted mb-4">
                                    Access your iOS device automation interface with full testing capabilities,
                                    device management, and real-time monitoring.
                                </p>
                                <div class="mb-4">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="text-primary fw-bold" id="ios-device-count">-</div>
                                            <small class="text-muted">Devices</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="text-success fw-bold" id="ios-online-count">-</div>
                                            <small class="text-muted">Online</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="text-info fw-bold" id="ios-test-count">-</div>
                                            <small class="text-muted">Tests</small>
                                        </div>
                                    </div>
                                </div>
                                <button class="btn btn-primary btn-lg w-100" onclick="openIOSAutomation()">
                                    <i class="fas fa-external-link-alt me-2"></i>
                                    Open iOS Automation
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Android Automation Card -->
                    <div class="col-md-6">
                        <div class="card h-100 border-0 shadow-sm automation-card" data-platform="android">
                            <div class="card-body text-center p-5">
                                <div class="mb-4">
                                    <i class="fab fa-android display-1 text-success"></i>
                                </div>
                                <h3 class="h4 mb-3">Android Automation</h3>
                                <p class="text-muted mb-4">
                                    Access your Android device automation interface with comprehensive testing tools,
                                    device control, and performance monitoring.
                                </p>
                                <div class="mb-4">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="text-primary fw-bold" id="android-device-count">-</div>
                                            <small class="text-muted">Devices</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="text-success fw-bold" id="android-online-count">-</div>
                                            <small class="text-muted">Online</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="text-info fw-bold" id="android-test-count">-</div>
                                            <small class="text-muted">Tests</small>
                                        </div>
                                    </div>
                                </div>
                                <button class="btn btn-success btn-lg w-100" onclick="openAndroidAutomation()">
                                    <i class="fas fa-external-link-alt me-2"></i>
                                    Open Android Automation
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats Section -->
                <div class="row mt-5">
                    <div class="col-12">
                        <div class="card border-0 bg-light">
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <div class="h5 mb-1" id="total-devices">-</div>
                                        <small class="text-muted">Total Devices</small>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="h5 mb-1" id="active-tests">-</div>
                                        <small class="text-muted">Active Tests</small>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="h5 mb-1" id="test-minutes-used">-</div>
                                        <small class="text-muted">Test Minutes Used</small>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="h5 mb-1" id="test-minutes-remaining">-</div>
                                        <small class="text-muted">Minutes Remaining</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the dashboard
    console.log('Simplified Mobile Automation Dashboard loaded');

    // Load dashboard statistics
    loadDashboardStats();

    // Add hover effects to automation cards
    const cards = document.querySelectorAll('.automation-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.transition = 'transform 0.3s ease';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Refresh stats every 30 seconds
    setInterval(loadDashboardStats, 30000);
});

function loadDashboardStats() {
    // Load device and test statistics
    fetch('/api/dashboard/stats')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('Error loading dashboard stats:', data.error);
                return;
            }

            // Update iOS stats
            document.getElementById('ios-device-count').textContent = data.ios?.devices || 0;
            document.getElementById('ios-online-count').textContent = data.ios?.online || 0;
            document.getElementById('ios-test-count').textContent = data.ios?.tests || 0;

            // Update Android stats
            document.getElementById('android-device-count').textContent = data.android?.devices || 0;
            document.getElementById('android-online-count').textContent = data.android?.online || 0;
            document.getElementById('android-test-count').textContent = data.android?.tests || 0;

            // Update overall stats
            document.getElementById('total-devices').textContent = data.total?.devices || 0;
            document.getElementById('active-tests').textContent = data.total?.active_tests || 0;
            document.getElementById('test-minutes-used').textContent = data.usage?.minutes_used || 0;
            document.getElementById('test-minutes-remaining').textContent = data.usage?.minutes_remaining || 0;
        })
        .catch(error => {
            console.error('Error loading dashboard stats:', error);
        });
}

function openIOSAutomation() {
    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Opening iOS Automation...';
    button.disabled = true;

    // Get iOS automation access URL
    fetch('/api/automation/ios/access', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('Error accessing iOS automation: ' + data.error);
            return;
        }

        // Open iOS automation in new window
        const newWindow = window.open(data.access_url, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');

        if (!newWindow) {
            alert('Please allow popups for this site to open the automation interface.');
        }
    })
    .catch(error => {
        console.error('Error opening iOS automation:', error);
        alert('Error opening iOS automation. Please try again.');
    })
    .finally(() => {
        // Restore button state
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function openAndroidAutomation() {
    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Opening Android Automation...';
    button.disabled = true;

    // Get Android automation access URL
    fetch('/api/automation/android/access', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('Error accessing Android automation: ' + data.error);
            return;
        }

        // Open Android automation in new window
        const newWindow = window.open(data.access_url, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');

        if (!newWindow) {
            alert('Please allow popups for this site to open the automation interface.');
        }
    })
    .catch(error => {
        console.error('Error opening Android automation:', error);
        alert('Error opening Android automation. Please try again.');
    })
    .finally(() => {
        // Restore button state
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// Handle authentication retry messages from automation windows
window.addEventListener('message', function(event) {
    if (event.data.type === 'request_ios_access') {
        // Re-generate iOS access for authentication retry
        fetch('/api/automation/ios/access', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (!data.error) {
                event.source.postMessage({
                    type: 'ios_access_response',
                    access_url: data.access_url
                }, '*');
            }
        })
        .catch(error => {
            console.error('Error regenerating iOS access:', error);
        });
    } else if (event.data.type === 'request_android_access') {
        // Re-generate Android access for authentication retry
        fetch('/api/automation/android/access', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (!data.error) {
                event.source.postMessage({
                    type: 'android_access_response',
                    access_url: data.access_url
                }, '*');
            }
        })
        .catch(error => {
            console.error('Error regenerating Android access:', error);
        });
    }
});
</script>

<style>
.automation-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.automation-card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.display-1 {
    font-size: 4rem;
}

@media (max-width: 768px) {
    .display-1 {
        font-size: 3rem;
    }
}
</style>
{% endblock %}
