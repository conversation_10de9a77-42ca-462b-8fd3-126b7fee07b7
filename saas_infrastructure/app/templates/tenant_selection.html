{% extends "base.html" %}

{% block title %}Select Your Organization - Mobile Automation SaaS{% endblock %}

{% block content %}
<div class="hero-section bg-gradient-primary text-white py-5">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-4">
                    <i class="fas fa-mobile-alt me-3"></i>
                    Mobile Automation SaaS Platform
                </h1>
                <p class="lead mb-4">
                    Multi-tenant mobile testing automation platform for iOS and Android devices
                </p>
                <div class="feature-badges">
                    <span class="badge bg-light text-primary me-2 mb-2">
                        <i class="fas fa-cloud me-1"></i>Cloud-Based
                    </span>
                    <span class="badge bg-light text-primary me-2 mb-2">
                        <i class="fas fa-users me-1"></i>Multi-Tenant
                    </span>
                    <span class="badge bg-light text-primary me-2 mb-2">
                        <i class="fas fa-shield-alt me-1"></i>Secure
                    </span>
                    <span class="badge bg-light text-primary me-2 mb-2">
                        <i class="fas fa-rocket me-1"></i>Scalable
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="text-center mb-5">
                <h2 class="h3 mb-3">Select Your Organization</h2>
                <p class="text-muted">Choose your organization to access your mobile automation dashboard</p>
            </div>

            {% if available_tenants %}
            <div class="row g-4">
                {% for tenant in available_tenants %}
                <div class="col-md-6 col-lg-4">
                    <div class="tenant-card card h-100 shadow-sm border-0">
                        <div class="card-body text-center p-4">
                            <div class="tenant-icon mb-3">
                                <i class="fas fa-building fa-3x text-primary"></i>
                            </div>
                            <h5 class="card-title mb-2">{{ tenant.name }}</h5>
                            <p class="card-text text-muted mb-3">
                                <small>
                                    <i class="fas fa-tag me-1"></i>
                                    {{ tenant.subscription_tier|title }} Plan
                                </small>
                            </p>
                            <a href="/tenant/{{ tenant.subdomain }}" 
                               class="btn btn-primary btn-lg w-100 tenant-select-btn"
                               data-tenant="{{ tenant.subdomain }}">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Access Dashboard
                            </a>
                        </div>
                        <div class="card-footer bg-light border-0 text-center">
                            <small class="text-muted">
                                <i class="fas fa-link me-1"></i>
                                {{ tenant.subdomain }}
                            </small>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <h4>No Organizations Available</h4>
                <p class="text-muted">No active organizations found. Please contact your administrator.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- API Documentation Section -->
<div class="bg-light py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="text-center mb-4">
                    <h3 class="h4">
                        <i class="fas fa-code me-2"></i>
                        API Access
                    </h3>
                    <p class="text-muted">For developers and automated systems</p>
                </div>
                
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Tenant Information
                                </h6>
                            </div>
                            <div class="card-body">
                                <code class="d-block bg-dark text-light p-3 rounded">
curl -H "X-Tenant-Subdomain: testcompany1" \<br>
&nbsp;&nbsp;&nbsp;&nbsp;{{ request.url_root }}api/tenant/info
                                </code>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-key me-2"></i>
                                    Authentication
                                </h6>
                            </div>
                            <div class="card-body">
                                <code class="d-block bg-dark text-light p-3 rounded">
curl -X POST \<br>
&nbsp;&nbsp;&nbsp;&nbsp;-H "Content-Type: application/json" \<br>
&nbsp;&nbsp;&nbsp;&nbsp;-H "X-Tenant-Subdomain: testcompany1" \<br>
&nbsp;&nbsp;&nbsp;&nbsp;-d '{"email":"<EMAIL>","password":"***"}' \<br>
&nbsp;&nbsp;&nbsp;&nbsp;{{ request.url_root }}api/auth/login
                                </code>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects to tenant cards
    const tenantCards = document.querySelectorAll('.tenant-card');
    tenantCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.transition = 'transform 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // Add click tracking for tenant selection
    const selectButtons = document.querySelectorAll('.tenant-select-btn');
    selectButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const tenant = this.getAttribute('data-tenant');
            console.log('Accessing tenant:', tenant);
            
            // Add loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
            this.disabled = true;
        });
    });
});
</script>
{% endblock %}
