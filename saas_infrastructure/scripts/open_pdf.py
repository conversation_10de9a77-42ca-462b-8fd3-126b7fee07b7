#!/usr/bin/env python3
"""
Simple script to open the generated PDF and provide information about it.
"""

import os
import sys
import subprocess
from pathlib import Path
from datetime import datetime

def main():
    # Get the PDF file path
    base_dir = Path(__file__).parent.parent
    pdf_dir = base_dir / "docs" / "pdf"
    
    # Find the most recent PDF file
    pdf_files = list(pdf_dir.glob("*.pdf"))
    if not pdf_files:
        print("❌ No PDF files found in the docs/pdf directory.")
        return 1
    
    # Get the most recent PDF
    latest_pdf = max(pdf_files, key=lambda x: x.stat().st_mtime)
    
    # Get file information
    file_size = latest_pdf.stat().st_size
    file_size_mb = file_size / (1024 * 1024)
    modified_time = datetime.fromtimestamp(latest_pdf.stat().st_mtime)
    
    print("📄 Mobile Automation SaaS - Technical Specifications PDF")
    print("=" * 60)
    print(f"📁 File: {latest_pdf.name}")
    print(f"📍 Location: {latest_pdf}")
    print(f"📊 Size: {file_size_mb:.2f} MB ({file_size:,} bytes)")
    print(f"🕒 Generated: {modified_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Try to open the PDF
    try:
        if sys.platform == "darwin":  # macOS
            subprocess.run(["open", str(latest_pdf)], check=True)
            print("✅ PDF opened successfully in default viewer!")
        elif sys.platform == "win32":  # Windows
            os.startfile(str(latest_pdf))
            print("✅ PDF opened successfully in default viewer!")
        elif sys.platform.startswith("linux"):  # Linux
            subprocess.run(["xdg-open", str(latest_pdf)], check=True)
            print("✅ PDF opened successfully in default viewer!")
        else:
            print(f"⚠️  Cannot auto-open PDF on {sys.platform}. Please open manually:")
            print(f"   {latest_pdf}")
    except subprocess.CalledProcessError:
        print("⚠️  Could not open PDF automatically. Please open manually:")
        print(f"   {latest_pdf}")
    except Exception as e:
        print(f"⚠️  Error opening PDF: {e}")
        print(f"   Please open manually: {latest_pdf}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
