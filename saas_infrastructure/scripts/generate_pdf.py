#!/usr/bin/env python3
"""
PDF Generation Script for Mobile Automation SaaS Technical Specifications

This script converts the technical specifications markdown document to a professional PDF
with proper formatting, syntax highlighting, and table of contents.

Requirements:
- weasyprint: pip install weasyprint
- markdown: pip install markdown
- pygments: pip install pygments
- markdown-extensions: pip install pymdown-extensions

Usage:
    python generate_pdf.py
"""

import os
import sys
import markdown
import logging
from datetime import datetime
from pathlib import Path

try:
    from weasyprint import HTML, CSS
    from weasyprint.text.fonts import FontConfiguration
except ImportError:
    print("Error: weasyprint is required. Install with: pip install weasyprint")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TechnicalSpecsPDFGenerator:
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent
        self.docs_dir = self.base_dir / "docs"
        self.output_dir = self.base_dir / "docs" / "pdf"
        self.output_dir.mkdir(exist_ok=True)
        
        # Input and output files
        self.input_file = self.docs_dir / "TECHNICAL_SPECIFICATIONS.md"
        self.output_file = self.output_dir / f"Mobile_Automation_SaaS_Technical_Specifications_{datetime.now().strftime('%Y%m%d')}.pdf"
        
        # Markdown extensions for better formatting
        self.markdown_extensions = [
            'markdown.extensions.toc',
            'markdown.extensions.tables',
            'markdown.extensions.fenced_code',
            'markdown.extensions.codehilite',
            'markdown.extensions.attr_list',
            'markdown.extensions.def_list',
            'markdown.extensions.footnotes',
            'markdown.extensions.meta',
            'pymdownx.superfences',
            'pymdownx.highlight',
            'pymdownx.inlinehilite',
            'pymdownx.snippets',
            'pymdownx.tabbed'
        ]
        
        # CSS styles for professional PDF formatting
        self.css_styles = """
        @page {
            size: A4;
            margin: 2cm 1.5cm;
            @top-center {
                content: "Mobile Automation SaaS - Technical Specifications";
                font-family: 'Arial', sans-serif;
                font-size: 10pt;
                color: #666;
            }
            @bottom-center {
                content: "Page " counter(page) " of " counter(pages);
                font-family: 'Arial', sans-serif;
                font-size: 10pt;
                color: #666;
            }
        }
        
        body {
            font-family: 'Arial', 'Helvetica', sans-serif;
            font-size: 11pt;
            line-height: 1.6;
            color: #333;
            max-width: none;
        }
        
        h1 {
            color: #2c3e50;
            font-size: 24pt;
            font-weight: bold;
            margin-top: 30pt;
            margin-bottom: 20pt;
            page-break-before: always;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10pt;
        }
        
        h1:first-of-type {
            page-break-before: avoid;
        }
        
        h2 {
            color: #34495e;
            font-size: 18pt;
            font-weight: bold;
            margin-top: 25pt;
            margin-bottom: 15pt;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 5pt;
        }
        
        h3 {
            color: #2c3e50;
            font-size: 14pt;
            font-weight: bold;
            margin-top: 20pt;
            margin-bottom: 10pt;
        }
        
        h4 {
            color: #34495e;
            font-size: 12pt;
            font-weight: bold;
            margin-top: 15pt;
            margin-bottom: 8pt;
        }
        
        p {
            margin-bottom: 10pt;
            text-align: justify;
        }
        
        ul, ol {
            margin-bottom: 10pt;
            padding-left: 20pt;
        }
        
        li {
            margin-bottom: 5pt;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15pt 0;
            font-size: 10pt;
        }
        
        th, td {
            border: 1px solid #bdc3c7;
            padding: 8pt;
            text-align: left;
            vertical-align: top;
        }
        
        th {
            background-color: #ecf0f1;
            font-weight: bold;
            color: #2c3e50;
        }
        
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        code {
            font-family: 'Courier New', 'Monaco', monospace;
            font-size: 9pt;
            background-color: #f8f9fa;
            padding: 2pt 4pt;
            border-radius: 3pt;
            border: 1px solid #e9ecef;
        }
        
        pre {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5pt;
            padding: 10pt;
            margin: 10pt 0;
            overflow-x: auto;
            font-family: 'Courier New', 'Monaco', monospace;
            font-size: 8pt;
            line-height: 1.4;
            page-break-inside: avoid;
        }
        
        pre code {
            background: none;
            border: none;
            padding: 0;
            font-size: inherit;
        }
        
        blockquote {
            border-left: 4px solid #3498db;
            margin: 15pt 0;
            padding: 10pt 15pt;
            background-color: #f8f9fa;
            font-style: italic;
        }
        
        .toc {
            page-break-after: always;
            margin-bottom: 30pt;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            margin-bottom: 5pt;
            padding-left: 15pt;
        }
        
        .toc a {
            text-decoration: none;
            color: #2c3e50;
        }
        
        .toc a:hover {
            color: #3498db;
        }
        
        .highlight {
            background-color: #f8f9fa;
            border-radius: 3pt;
        }
        
        .codehilite {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5pt;
            padding: 10pt;
            margin: 10pt 0;
            overflow-x: auto;
        }
        
        .codehilite pre {
            background: none;
            border: none;
            padding: 0;
            margin: 0;
        }
        
        /* Syntax highlighting colors */
        .codehilite .k { color: #0000ff; } /* Keyword */
        .codehilite .s { color: #008000; } /* String */
        .codehilite .c { color: #808080; font-style: italic; } /* Comment */
        .codehilite .n { color: #000000; } /* Name */
        .codehilite .o { color: #666666; } /* Operator */
        .codehilite .p { color: #000000; } /* Punctuation */
        
        /* Page breaks */
        .page-break {
            page-break-before: always;
        }
        
        /* Cover page styles */
        .cover-page {
            text-align: center;
            page-break-after: always;
            margin-top: 100pt;
        }
        
        .cover-title {
            font-size: 32pt;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20pt;
        }
        
        .cover-subtitle {
            font-size: 18pt;
            color: #34495e;
            margin-bottom: 40pt;
        }
        
        .cover-info {
            font-size: 12pt;
            color: #7f8c8d;
            margin-top: 60pt;
        }
        """
    
    def read_markdown_file(self):
        """Read the markdown file and return its content"""
        try:
            with open(self.input_file, 'r', encoding='utf-8') as f:
                content = f.read()
            logger.info(f"Successfully read markdown file: {self.input_file}")
            return content
        except FileNotFoundError:
            logger.error(f"Markdown file not found: {self.input_file}")
            raise
        except Exception as e:
            logger.error(f"Error reading markdown file: {e}")
            raise
    
    def add_cover_page(self, content):
        """Add a professional cover page to the document"""
        cover_page = f"""
<div class="cover-page">
    <div class="cover-title">Mobile Automation SaaS Platform</div>
    <div class="cover-subtitle">Technical Specifications & Deployment Guide</div>
    <div class="cover-info">
        <p><strong>Document Version:</strong> 1.0</p>
        <p><strong>Generated:</strong> {datetime.now().strftime('%B %d, %Y')}</p>
        <p><strong>Classification:</strong> Technical Documentation</p>
        <p><strong>Target Audience:</strong> DevOps Engineers, System Administrators, Technical Leadership</p>
    </div>
</div>

"""
        return cover_page + content
    
    def convert_markdown_to_html(self, markdown_content):
        """Convert markdown content to HTML with proper formatting"""
        try:
            # Configure markdown processor
            md = markdown.Markdown(
                extensions=self.markdown_extensions,
                extension_configs={
                    'markdown.extensions.toc': {
                        'title': 'Table of Contents',
                        'anchorlink': True,
                        'permalink': True
                    },
                    'markdown.extensions.codehilite': {
                        'css_class': 'codehilite',
                        'use_pygments': True,
                        'noclasses': False
                    },
                    'pymdownx.superfences': {
                        'custom_fences': [
                            {
                                'name': 'mermaid',
                                'class': 'mermaid',
                                'format': lambda source, language, css_class, options, md: f'<div class="{css_class}">{source}</div>'
                            }
                        ]
                    }
                }
            )
            
            # Convert markdown to HTML
            html_content = md.convert(markdown_content)
            
            # Add cover page
            html_content = self.add_cover_page(html_content)
            
            # Wrap in complete HTML document
            full_html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Automation SaaS - Technical Specifications</title>
    <style>{self.css_styles}</style>
</head>
<body>
    {html_content}
</body>
</html>
"""
            
            logger.info("Successfully converted markdown to HTML")
            return full_html
            
        except Exception as e:
            logger.error(f"Error converting markdown to HTML: {e}")
            raise
    
    def generate_pdf(self, html_content):
        """Generate PDF from HTML content"""
        try:
            logger.info("Starting PDF generation...")
            
            # Configure font handling
            font_config = FontConfiguration()
            
            # Create HTML object
            html_doc = HTML(string=html_content, base_url=str(self.docs_dir))
            
            # Create CSS object
            css_doc = CSS(string=self.css_styles, font_config=font_config)
            
            # Generate PDF
            html_doc.write_pdf(
                target=str(self.output_file),
                stylesheets=[css_doc],
                font_config=font_config,
                optimize_images=True
            )
            
            logger.info(f"PDF generated successfully: {self.output_file}")
            return str(self.output_file)
            
        except Exception as e:
            logger.error(f"Error generating PDF: {e}")
            raise
    
    def generate(self):
        """Main method to generate the PDF"""
        try:
            logger.info("Starting PDF generation process...")
            
            # Read markdown content
            markdown_content = self.read_markdown_file()
            
            # Convert to HTML
            html_content = self.convert_markdown_to_html(markdown_content)
            
            # Generate PDF
            pdf_path = self.generate_pdf(html_content)
            
            # Get file size
            file_size = os.path.getsize(pdf_path)
            file_size_mb = file_size / (1024 * 1024)
            
            logger.info(f"PDF generation completed successfully!")
            logger.info(f"Output file: {pdf_path}")
            logger.info(f"File size: {file_size_mb:.2f} MB")
            
            return pdf_path
            
        except Exception as e:
            logger.error(f"PDF generation failed: {e}")
            raise

def main():
    """Main function"""
    try:
        generator = TechnicalSpecsPDFGenerator()
        pdf_path = generator.generate()
        
        print(f"\n✅ PDF generated successfully!")
        print(f"📄 File: {pdf_path}")
        print(f"📁 Location: {os.path.dirname(pdf_path)}")
        
        # Open PDF if on macOS
        if sys.platform == "darwin":
            os.system(f"open '{pdf_path}'")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
