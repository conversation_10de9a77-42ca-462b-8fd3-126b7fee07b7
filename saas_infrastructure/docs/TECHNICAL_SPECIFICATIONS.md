# Mobile Automation SaaS Platform - Technical Specifications

## Executive Summary

This document provides comprehensive technical specifications for deploying and operating the Mobile Automation SaaS Platform at enterprise scale. The platform supports 10,000+ concurrent subscribers using a hybrid cloud architecture that reduces infrastructure costs by 97% compared to traditional cloud device farms.

## Table of Contents

1. [Server Hardware Requirements](#1-server-hardware-requirements)
2. [Cross-Platform Device Bridge Compatibility](#2-cross-platform-device-bridge-compatibility)
3. [Security and Best Practices](#3-security-and-best-practices)

---

## 1. Server Hardware Requirements

### 1.1 Load Analysis for 10,000 Concurrent Subscribers

**Expected Load Patterns:**
- **Concurrent Users**: 10,000 active subscribers
- **Device Connections**: ~50,000 devices (5 devices per subscriber average)
- **WebSocket Connections**: 20,000 (10K subscribers + 10K device bridges)
- **API Requests**: Peak 1M requests/second (100 req/sec per subscriber)
- **Database Queries**: ~500 queries/second sustained, 2K queries/second peak
- **Data Transfer**: 10 GB/hour average, 50 GB/hour peak

### 1.2 Application Server Specifications

**Minimum Production Configuration:**
```yaml
CPU: 16 cores (32 vCPUs)
  - Architecture: x86_64
  - Type: Intel Xeon Gold 6248R or AMD EPYC 7302P
  - Base Clock: 3.0 GHz minimum
  - Cache: 32MB L3 cache minimum

RAM: 64 GB DDR4
  - Speed: 3200 MHz
  - ECC: Required for production
  - Configuration: 4x 16GB modules for redundancy

Storage: 500 GB NVMe SSD
  - Read Speed: 3,500 MB/s minimum
  - Write Speed: 3,000 MB/s minimum
  - IOPS: 500K read, 400K write
  - Endurance: 1 DWPD (Drive Writes Per Day)

Network: 10 Gbps
  - Bandwidth: Unlimited or 100TB/month minimum
  - Latency: <1ms to database server
  - Redundancy: Dual network interfaces
```

**Recommended Production Configuration:**
```yaml
CPU: 24-32 cores (48-64 vCPUs)
  - Intel Xeon Platinum 8358 or AMD EPYC 7543
  - Turbo Boost: 4.0 GHz
  - Hyperthreading: Enabled

RAM: 128 GB DDR4
  - Speed: 3200 MHz ECC
  - Configuration: 8x 16GB modules

Storage: 1 TB NVMe SSD + 2 TB for logs
  - Primary: Samsung PM9A3 or equivalent
  - Logs: Separate drive for I/O isolation
  - Backup: Local RAID 1 configuration

Network: 25 Gbps
  - Provider: Tier 1 network with global presence
  - CDN Integration: Cloudflare or AWS CloudFront
```

### 1.3 Database Server Specifications

**PostgreSQL Primary Server:**
```yaml
CPU: 24 cores (48 vCPUs)
  - High single-thread performance critical
  - Intel Xeon Gold 6348 or AMD EPYC 7443P
  - Base Clock: 3.2 GHz minimum

RAM: 256 GB DDR4
  - Large buffer cache for multi-tenant queries
  - Speed: 3200 MHz ECC
  - Configuration: 16x 16GB modules

Storage: 2 TB NVMe SSD (RAID 10)
  - Enterprise grade: Intel P5510 or Samsung PM9A3
  - RAID Controller: Hardware RAID with BBU
  - Hot Spare: 1 additional drive
  - IOPS: 1M+ random read/write

Network: 25 Gbps dedicated
  - Dedicated connection to application tier
  - Separate management network
  - Database replication network
```

**PostgreSQL Configuration for Scale:**
```sql
-- postgresql.conf optimizations for 10K tenants
shared_buffers = 64GB                    # 25% of RAM
effective_cache_size = 192GB             # 75% of RAM
work_mem = 256MB                         # Per connection
maintenance_work_mem = 2GB               # Maintenance operations
max_connections = 500                    # Connection limit
max_worker_processes = 24                # Parallel workers
max_parallel_workers = 16                # Parallel query workers
max_parallel_workers_per_gather = 8      # Per query
random_page_cost = 1.1                   # SSD optimization
effective_io_concurrency = 200           # SSD concurrent I/O

-- Multi-tenant optimizations
enable_partitionwise_join = on           # Partition-wise joins
enable_partitionwise_aggregate = on      # Partition-wise aggregates
constraint_exclusion = partition         # Partition pruning
```

### 1.4 Read Replica Configuration

**Read Replica Servers (3-5 instances):**
```yaml
CPU: 16 cores (32 vCPUs)
RAM: 128 GB DDR4
Storage: 2 TB NVMe SSD
Network: 10 Gbps

Geographic Distribution:
  - Primary Region: 2 replicas
  - Secondary Region: 2 replicas
  - Tertiary Region: 1 replica

Replication Configuration:
  - Streaming replication with WAL-E
  - Automatic failover with Patroni
  - Load balancing with pgpool-II
```

### 1.5 Infrastructure Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    LOAD BALANCER TIER                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   HAProxy   │  │   HAProxy   │  │   HAProxy   │        │
│  │  (Primary)  │  │ (Secondary) │  │  (Tertiary) │        │
│  │   4 vCPU    │  │   4 vCPU    │  │   4 vCPU    │        │
│  │   8 GB RAM  │  │   8 GB RAM  │  │   8 GB RAM  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  APPLICATION TIER                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  App Server │  │  App Server │  │  App Server │        │
│  │  32 vCPU    │  │  32 vCPU    │  │  32 vCPU    │        │
│  │  64 GB RAM  │  │  64 GB RAM  │  │  64 GB RAM  │        │
│  │  500GB SSD  │  │  500GB SSD  │  │  500GB SSD  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   DATABASE TIER                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ PostgreSQL  │  │ Read Replica│  │ Read Replica│        │
│  │  Primary    │  │  16 vCPU    │  │  16 vCPU    │        │
│  │  48 vCPU    │  │  128GB RAM  │  │  128GB RAM  │        │
│  │  256GB RAM  │  │  2TB SSD    │  │  2TB SSD    │        │
│  │  2TB RAID10 │  └─────────────┘  └─────────────┘        │
│  └─────────────┘                                           │
└─────────────────────────────────────────────────────────────┘
```

### 1.6 Cost Analysis

**Hetzner Cloud Pricing (Recommended Provider):**
```
Application Servers:
  - 3x CCX62 (16 vCPU, 64GB RAM): €179 × 3 = €537/month

Database Primary:
  - 1x CCX72 (32 vCPU, 256GB RAM): €1,074/month

Read Replicas:
  - 2x CCX52 (16 vCPU, 128GB RAM): €179 × 2 = €358/month

Load Balancers:
  - 3x CX21 (2 vCPU, 4GB RAM): €5.83 × 3 = €17.49/month

Storage (additional):
  - 6TB additional SSD: €0.119/GB × 6000 = €714/month

Network:
  - Unlimited traffic included

Total Monthly Cost: €2,700
Cost per subscriber: €0.27/month
Annual cost per subscriber: €3.24
```

**AWS Comparison (for reference):**
```
Application Servers:
  - 3x c5.8xlarge: $1.36 × 24 × 30 × 3 = $2,937/month

Database:
  - 1x r5.12xlarge: $3.024 × 24 × 30 = $2,177/month

Total AWS Cost: ~$8,000/month
Hetzner Savings: 66% cost reduction
```

### 1.7 Scaling Considerations

**Horizontal Scaling Strategy:**
```python
# Auto-scaling configuration
class AutoScaler:
    def __init__(self):
        self.metrics_thresholds = {
            "cpu_usage": 70,           # Scale up at 70% CPU
            "memory_usage": 80,        # Scale up at 80% memory
            "active_connections": 8000, # Scale up at 8K connections
            "response_time": 500       # Scale up at 500ms response
        }

    def should_scale_up(self, metrics):
        return any(
            metrics[key] > threshold
            for key, threshold in self.metrics_thresholds.items()
        )

    def scale_application_tier(self, target_instances):
        # Add new application server instances
        for i in range(target_instances):
            self.provision_app_server()
            self.update_load_balancer_config()
```

**Database Scaling Strategy:**
```sql
-- Partition strategy for large tables
CREATE TABLE test_executions (
    id UUID PRIMARY KEY,
    tenant_id UUID NOT NULL,
    created_at TIMESTAMP NOT NULL,
    -- other columns
) PARTITION BY RANGE (created_at);

-- Monthly partitions for performance
CREATE TABLE test_executions_2024_01 PARTITION OF test_executions
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- Tenant-specific partitioning for very large tenants
CREATE TABLE test_executions_large_tenant PARTITION OF test_executions
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01')
WHERE tenant_id = 'large-tenant-uuid';
```

---

## 2. Cross-Platform Device Bridge Compatibility

### 2.1 iOS Testing from Windows: Current Limitations

**Technical Challenges:**
```python
# Current iOS device communication stack
class iOSDeviceManager:
    def __init__(self):
        self.required_tools = {
            "libimobiledevice": {
                "linux": "Available via apt/yum",
                "macos": "Available via Homebrew",
                "windows": "Limited functionality"
            },
            "usbmuxd": {
                "linux": "Native support",
                "macos": "Built-in",
                "windows": "Requires iTunes drivers"
            },
            "ideviceinstaller": {
                "linux": "Full functionality",
                "macos": "Full functionality",
                "windows": "Not available"
            }
        }
```

**Windows-Specific Limitations:**
1. **libimobiledevice Windows Port**: Incomplete feature set
2. **USB Communication**: Windows USB stack differences
3. **iTunes Dependencies**: Requires iTunes installation
4. **Code Signing**: iOS app installation requires macOS toolchain
5. **File System Access**: Limited iOS file system access
6. **Crash Log Access**: Restricted crash log retrieval

### 2.2 Detailed Compatibility Matrix

```
┌─────────────────┬─────────┬─────────┬─────────┐
│   Operation     │ Windows │  macOS  │  Linux  │
├─────────────────┼─────────┼─────────┼─────────┤
│ Device Discovery│   ✓     │   ✓     │   ✓     │
│ Basic Info      │   ✓     │   ✓     │   ✓     │
│ App Installation│   ✗     │   ✓     │   ✓     │
│ App Launch      │   ✗     │   ✓     │   ✓     │
│ File Transfer   │   ✗     │   ✓     │   ✓     │
│ Screenshot      │   ✗     │   ✓     │   ✓     │
│ Crash Logs      │   ✗     │   ✓     │   ✓     │
│ System Logs     │   ✗     │   ✓     │   ✓     │
│ Performance     │   ✗     │   ✓     │   ✓     │
└─────────────────┴─────────┴─────────┴─────────┘
```

### 2.3 Workaround Solutions

**Solution 1: Windows Subsystem for Linux (WSL2)**
```bash
# Install WSL2 with Ubuntu
wsl --install -d Ubuntu-20.04

# Install required packages in WSL2
sudo apt update
sudo apt install -y \
    libimobiledevice-utils \
    usbmuxd \
    ideviceinstaller \
    ifuse

# USB device passthrough setup
sudo apt install linux-tools-virtual hwdata
sudo update-alternatives --install /usr/local/bin/usbip usbip \
    /usr/lib/linux-tools/*/usbip 20

# Start usbmuxd service
sudo systemctl enable usbmuxd
sudo systemctl start usbmuxd
```

**WSL2 Bridge Implementation:**
```python
class WSL2iOSBridge:
    def __init__(self):
        self.wsl_command_prefix = ["wsl", "-d", "Ubuntu-20.04"]

    async def discover_ios_devices(self):
        """Discover iOS devices through WSL2"""
        try:
            cmd = self.wsl_command_prefix + ["idevice_id", "-l"]
            result = await asyncio.create_subprocess_exec(
                *cmd, stdout=asyncio.subprocess.PIPE
            )
            stdout, _ = await result.communicate()

            device_ids = stdout.decode().strip().split('\n')
            devices = []

            for device_id in device_ids:
                if device_id:
                    device_info = await self.get_device_info(device_id)
                    devices.append(device_info)

            return devices

        except Exception as e:
            logger.error(f"WSL2 iOS discovery failed: {e}")
            return []

    async def get_device_info(self, device_id):
        """Get device information via WSL2"""
        cmd = self.wsl_command_prefix + [
            "ideviceinfo", "-u", device_id, "-k", "DeviceName"
        ]
        result = await asyncio.create_subprocess_exec(
            *cmd, stdout=asyncio.subprocess.PIPE
        )
        stdout, _ = await result.communicate()

        return {
            "id": device_id,
            "name": stdout.decode().strip(),
            "platform": "iOS",
            "bridge_type": "WSL2"
        }
```

**Solution 2: Hybrid Bridge Architecture**
```python
class HybridDeviceBridge:
    def __init__(self):
        self.local_bridges = {
            "android": WindowsAndroidBridge(),  # Local Android support
            "ios_proxy": []  # Remote iOS bridge connections
        }

    async def register_ios_proxy(self, proxy_config):
        """Register remote macOS/Linux bridge for iOS"""
        proxy_bridge = RemoteiOSBridge(
            host=proxy_config["host"],
            port=proxy_config["port"],
            auth_token=proxy_config["token"]
        )

        if await proxy_bridge.connect():
            self.local_bridges["ios_proxy"].append(proxy_bridge)
            return True
        return False

    async def discover_all_devices(self):
        """Discover devices from all available bridges"""
        all_devices = []

        # Local Android devices
        android_devices = await self.local_bridges["android"].discover()
        all_devices.extend(android_devices)

        # Remote iOS devices via proxy bridges
        for proxy in self.local_bridges["ios_proxy"]:
            try:
                ios_devices = await proxy.discover_devices()
                all_devices.extend(ios_devices)
            except Exception as e:
                logger.warning(f"iOS proxy bridge failed: {e}")

        return all_devices

class RemoteiOSBridge:
    def __init__(self, host, port, auth_token):
        self.host = host
        self.port = port
        self.auth_token = auth_token
        self.websocket = None

    async def connect(self):
        """Connect to remote iOS bridge"""
        try:
            uri = f"wss://{self.host}:{self.port}/ios-bridge"
            headers = {"Authorization": f"Bearer {self.auth_token}"}

            self.websocket = await websockets.connect(uri, extra_headers=headers)
            return True
        except Exception as e:
            logger.error(f"Failed to connect to iOS proxy: {e}")
            return False

    async def discover_devices(self):
        """Discover iOS devices via remote bridge"""
        if not self.websocket:
            return []

        await self.websocket.send(json.dumps({
            "action": "discover_devices",
            "platform": "ios"
        }))

        response = await self.websocket.recv()
        data = json.loads(response)

        return data.get("devices", [])
```

**Solution 3: Cloud iOS Testing Integration**
```python
class CloudiOSProvider:
    def __init__(self):
        self.providers = {
            "browserstack": BrowserStackProvider(),
            "saucelabs": SauceLabsProvider(),
            "aws_device_farm": AWSDeviceFarmProvider(),
            "firebase_test_lab": FirebaseTestLabProvider()
        }

    async def provision_ios_device(self, requirements):
        """Provision cloud iOS device when local unavailable"""
        device_spec = {
            "platform": "iOS",
            "version": requirements.get("ios_version", "latest"),
            "device_type": requirements.get("device_type", "iPhone"),
            "duration": requirements.get("duration", 3600)  # 1 hour
        }

        # Try providers in order of preference
        for provider_name, provider in self.providers.items():
            try:
                device = await provider.provision_device(device_spec)
                if device:
                    logger.info(f"Provisioned iOS device via {provider_name}")
                    return device
            except Exception as e:
                logger.warning(f"{provider_name} provisioning failed: {e}")

        raise Exception("No cloud iOS providers available")

class BrowserStackProvider:
    def __init__(self):
        self.api_base = "https://api.browserstack.com"
        self.username = os.getenv("BROWSERSTACK_USERNAME")
        self.access_key = os.getenv("BROWSERSTACK_ACCESS_KEY")

    async def provision_device(self, device_spec):
        """Provision BrowserStack iOS device"""
        session_data = {
            "os": "ios",
            "os_version": device_spec["version"],
            "device": device_spec["device_type"],
            "real_mobile": "true"
        }

        async with aiohttp.ClientSession() as session:
            auth = aiohttp.BasicAuth(self.username, self.access_key)

            async with session.post(
                f"{self.api_base}/app-automate/sessions",
                json=session_data,
                auth=auth
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        "id": data["session_id"],
                        "provider": "browserstack",
                        "capabilities": data["capabilities"],
                        "hub_url": data["hub_url"]
                    }

        return None
```

### 2.4 Recommended Cross-Platform Setup

**For Windows Users Requiring iOS Testing:**

1. **Primary Setup (Recommended)**:
   ```yaml
   Windows Machine:
     - Android testing: Native support via ADB
     - iOS testing: WSL2 + libimobiledevice (limited)
     - Cloud fallback: BrowserStack/SauceLabs integration

   Dedicated Mac Mini:
     - iOS testing: Full native support
     - Remote bridge: WebSocket connection to Windows
     - Cost: $599 one-time vs $200+/month cloud
   ```

2. **Alternative Setup**:
   ```yaml
   Windows Machine:
     - Android testing: Native support
     - iOS testing: Cloud providers only
     - Cost: $0.05-0.10 per minute cloud usage
   ```

3. **Enterprise Setup**:
   ```yaml
   Windows Machines:
     - Android testing: Native support
     - iOS proxy: Connection to shared Mac infrastructure

   Shared Mac Infrastructure:
     - Multiple Mac Minis in rack
     - Centralized iOS device management
     - Load balancing across Mac instances
   ```

**Implementation Example:**
```python
# Cross-platform bridge configuration
class CrossPlatformBridge:
    def __init__(self, config):
        self.config = config
        self.bridges = {}

        # Initialize platform-specific bridges
        if platform.system() == "Windows":
            self.bridges["android"] = WindowsAndroidBridge()

            if config.get("wsl2_enabled"):
                self.bridges["ios"] = WSL2iOSBridge()
            elif config.get("ios_proxy_bridges"):
                self.bridges["ios"] = RemoteiOSBridgeManager(
                    config["ios_proxy_bridges"]
                )
            elif config.get("cloud_ios_enabled"):
                self.bridges["ios"] = CloudiOSProvider()

        elif platform.system() == "Darwin":  # macOS
            self.bridges["android"] = MacOSAndroidBridge()
            self.bridges["ios"] = MacOSiOSBridge()

        elif platform.system() == "Linux":
            self.bridges["android"] = LinuxAndroidBridge()
            self.bridges["ios"] = LinuxiOSBridge()

    async def discover_devices(self):
        """Discover devices across all available bridges"""
        all_devices = []

        for platform, bridge in self.bridges.items():
            try:
                devices = await bridge.discover_devices()
                for device in devices:
                    device["bridge_platform"] = platform
                all_devices.extend(devices)
            except Exception as e:
                logger.warning(f"Bridge {platform} discovery failed: {e}")

        return all_devices
```

---

## 3. Security and Best Practices

### 3.1 Multi-Tenant Data Isolation and Security

**Row Level Security (RLS) Implementation:**
```sql
-- Enable RLS on all tenant tables
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenant_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenant_devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Tenant isolation policy
CREATE POLICY tenant_isolation ON tenant_devices
    USING (tenant_id = current_setting('app.current_tenant_id')::uuid);

CREATE POLICY tenant_user_isolation ON tenant_users
    USING (tenant_id = current_setting('app.current_tenant_id')::uuid);

-- Admin bypass policy (for system operations)
CREATE POLICY admin_bypass ON tenant_devices
    TO admin_role
    USING (true);

-- Audit logging trigger for all tenant operations
CREATE OR REPLACE FUNCTION audit_tenant_access()
RETURNS TRIGGER AS $$
DECLARE
    tenant_id_val UUID;
    user_id_val UUID;
BEGIN
    -- Get current tenant and user from session
    BEGIN
        tenant_id_val := current_setting('app.current_tenant_id')::uuid;
        user_id_val := current_setting('app.current_user_id')::uuid;
    EXCEPTION WHEN OTHERS THEN
        -- Log potential security violation
        INSERT INTO security_violations (
            table_name, operation, timestamp, session_info
        ) VALUES (
            TG_TABLE_NAME, TG_OP, NOW(),
            current_setting('application_name', true)
        );
        RAISE EXCEPTION 'Tenant context not set - potential security violation';
    END;

    -- Log the operation
    INSERT INTO audit_logs (
        tenant_id, user_id, table_name, operation,
        old_values, new_values, timestamp, ip_address
    ) VALUES (
        tenant_id_val, user_id_val, TG_TABLE_NAME, TG_OP,
        CASE WHEN TG_OP = 'DELETE' OR TG_OP = 'UPDATE'
             THEN row_to_json(OLD) ELSE NULL END,
        CASE WHEN TG_OP = 'INSERT' OR TG_OP = 'UPDATE'
             THEN row_to_json(NEW) ELSE NULL END,
        NOW(),
        current_setting('app.client_ip', true)
    );

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Apply audit trigger to all tenant tables
CREATE TRIGGER audit_tenant_devices
    AFTER INSERT OR UPDATE OR DELETE ON tenant_devices
    FOR EACH ROW EXECUTE FUNCTION audit_tenant_access();
```

**Application-Level Tenant Context Management:**
```python
import contextvars
from functools import wraps
from flask import g, request, abort
import jwt

class TenantContextManager:
    def __init__(self):
        self.tenant_context = contextvars.ContextVar('tenant_id')
        self.user_context = contextvars.ContextVar('user_id')

    def set_tenant_context(self, tenant_id, user_id=None):
        """Set tenant context for current request"""
        self.tenant_context.set(tenant_id)
        if user_id:
            self.user_context.set(user_id)

        # Set database session variables
        db.session.execute(
            text("SET app.current_tenant_id = :tenant_id"),
            {"tenant_id": str(tenant_id)}
        )

        if user_id:
            db.session.execute(
                text("SET app.current_user_id = :user_id"),
                {"user_id": str(user_id)}
            )

        # Set client IP for audit logging
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR',
                                       request.environ.get('REMOTE_ADDR'))
        db.session.execute(
            text("SET app.client_ip = :ip"),
            {"ip": client_ip}
        )

    def get_current_tenant(self):
        """Get current tenant ID"""
        try:
            return self.tenant_context.get()
        except LookupError:
            return None

    @contextmanager
    def tenant_scope(self, tenant_id, user_id=None):
        """Context manager for tenant operations"""
        token = self.tenant_context.set(tenant_id)
        user_token = None

        if user_id:
            user_token = self.user_context.set(user_id)

        try:
            self.set_tenant_context(tenant_id, user_id)
            yield
        finally:
            self.tenant_context.reset(token)
            if user_token:
                self.user_context.reset(user_token)

# Global tenant context manager
tenant_context = TenantContextManager()

def require_tenant_context(f):
    """Decorator to ensure tenant context is set"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not tenant_context.get_current_tenant():
            abort(403, "Tenant context not set")
        return f(*args, **kwargs)
    return decorated_function

def extract_tenant_from_token(f):
    """Decorator to extract tenant from JWT token"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            abort(401, "Missing or invalid authorization header")

        token = auth_header.split(' ')[1]

        try:
            payload = jwt.decode(
                token,
                current_app.config['JWT_SECRET_KEY'],
                algorithms=['RS256']
            )

            tenant_id = payload.get('tenant_id')
            user_id = payload.get('sub')

            if not tenant_id:
                abort(403, "Token missing tenant information")

            tenant_context.set_tenant_context(tenant_id, user_id)
            g.current_tenant_id = tenant_id
            g.current_user_id = user_id

        except jwt.InvalidTokenError as e:
            abort(401, f"Invalid token: {str(e)}")

        return f(*args, **kwargs)
    return decorated_function
```

**Tenant Data Validation:**
```python
class TenantDataValidator:
    def __init__(self):
        self.max_devices_per_tenant = {
            "starter": 5,
            "professional": 25,
            "team": 100,
            "enterprise": 1000
        }

        self.max_executions_per_month = {
            "starter": 1000,
            "professional": 10000,
            "team": 50000,
            "enterprise": 500000
        }

    def validate_device_limit(self, tenant_id, new_device_count=1):
        """Validate tenant device limits"""
        tenant = Tenant.query.get(tenant_id)
        current_devices = TenantDevice.query.filter_by(
            tenant_id=tenant_id, active=True
        ).count()

        max_allowed = self.max_devices_per_tenant.get(
            tenant.subscription_tier, 5
        )

        if current_devices + new_device_count > max_allowed:
            raise ValidationError(
                f"Device limit exceeded. Current: {current_devices}, "
                f"Max allowed: {max_allowed}"
            )

    def validate_execution_limit(self, tenant_id):
        """Validate monthly execution limits"""
        tenant = Tenant.query.get(tenant_id)

        # Get current month executions
        current_month = datetime.now().replace(day=1, hour=0, minute=0, second=0)
        executions_this_month = TestExecution.query.filter(
            TestExecution.tenant_id == tenant_id,
            TestExecution.created_at >= current_month
        ).count()

        max_allowed = self.max_executions_per_month.get(
            tenant.subscription_tier, 1000
        )

        if executions_this_month >= max_allowed:
            raise ValidationError(
                f"Monthly execution limit exceeded. "
                f"Used: {executions_this_month}, Max: {max_allowed}"
            )

    def check_cross_tenant_access(self, resource_tenant_id, current_tenant_id):
        """Detect potential cross-tenant access attempts"""
        if resource_tenant_id != current_tenant_id:
            # Log security violation
            SecurityViolation.create(
                tenant_id=current_tenant_id,
                violation_type="cross_tenant_access_attempt",
                details={
                    "attempted_tenant": str(resource_tenant_id),
                    "current_tenant": str(current_tenant_id),
                    "endpoint": request.endpoint,
                    "ip_address": request.remote_addr,
                    "user_agent": request.user_agent.string
                }
            )

            abort(403, "Access denied: Cross-tenant access not allowed")

### 3.2 Network Security and Tunnel Configuration

**Cloudflare Tunnel Security Configuration:**
```yaml
# /etc/cloudflared/config.yml - Production configuration
tunnel: mobile-automation-tunnel
credentials-file: /etc/cloudflared/tunnel.json

# Ingress rules with security controls
ingress:
  # API endpoints with rate limiting
  - hostname: api.yourdomain.com
    service: http://localhost:5000
    originRequest:
      httpHostHeader: api.yourdomain.com
      tlsTimeout: 30s
      tcpKeepAlive: 30s
      noHappyEyeballs: true
      keepAliveConnections: 10
      keepAliveTimeout: 90s
      # Security headers
      originServerName: api.yourdomain.com
      caPool: /etc/ssl/certs/ca-certificates.crt

  # WebSocket endpoints for device bridges
  - hostname: bridge.yourdomain.com
    service: http://localhost:5000
    originRequest:
      httpHostHeader: bridge.yourdomain.com
      # WebSocket specific settings
      upgradeWebsocket: true

  # Admin interface (restricted)
  - hostname: admin.yourdomain.com
    service: http://localhost:5000
    originRequest:
      httpHostHeader: admin.yourdomain.com
      # Additional security for admin
      tlsTimeout: 10s

  # Block all other traffic
  - service: http_status:403

# Tunnel-level security settings
warp-routing:
  enabled: false  # Disable unless specifically needed

# Logging configuration
loglevel: info
logfile: /var/log/cloudflared/tunnel.log

# Metrics and monitoring
metrics: localhost:8080
```

**Network Segmentation and Firewall Rules:**
```bash
#!/bin/bash
# /opt/mobile-automation-saas/scripts/setup_firewall.sh

# Reset firewall rules
ufw --force reset
ufw default deny incoming
ufw default allow outgoing

# SSH access (restrict to specific IPs in production)
ufw allow from ***********/24 to any port 22 comment "Office IP range"
ufw allow from ************/24 to any port 22 comment "VPN IP range"

# HTTP/HTTPS (via Cloudflare only)
ufw allow 80/tcp comment "HTTP redirect to HTTPS"
ufw allow 443/tcp comment "HTTPS via Cloudflare"

# Database access (internal network only)
ufw allow from 10.0.0.0/8 to any port 5432 comment "PostgreSQL internal"
ufw allow from **********/12 to any port 5432 comment "PostgreSQL internal"
ufw allow from ***********/16 to any port 5432 comment "PostgreSQL internal"

# Redis (internal only)
ufw allow from 10.0.0.0/8 to any port 6379 comment "Redis internal"

# Monitoring and metrics
ufw allow from 10.0.0.0/8 to any port 9090 comment "Prometheus"
ufw allow from 10.0.0.0/8 to any port 3000 comment "Grafana"
ufw allow from 10.0.0.0/8 to any port 8080 comment "Cloudflared metrics"

# Block common attack vectors
ufw deny 23 comment "Block Telnet"
ufw deny 135 comment "Block RPC"
ufw deny 139 comment "Block NetBIOS"
ufw deny 445 comment "Block SMB"

# Rate limiting for SSH
ufw limit ssh comment "Rate limit SSH connections"

# Enable firewall
ufw --force enable

# Additional iptables rules for advanced protection
iptables -A INPUT -p tcp --dport 22 -m conntrack --ctstate NEW -m recent --set
iptables -A INPUT -p tcp --dport 22 -m conntrack --ctstate NEW -m recent --update --seconds 60 --hitcount 4 -j DROP

# Save iptables rules
iptables-save > /etc/iptables/rules.v4
```

**Nginx Security Configuration:**
```nginx
# /etc/nginx/sites-available/saas.conf
server {
    listen 80;
    server_name api.yourdomain.com bridge.yourdomain.com admin.yourdomain.com;

    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.yourdomain.com bridge.yourdomain.com admin.yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/ssl/certs/yourdomain.com.crt;
    ssl_certificate_key /etc/ssl/private/yourdomain.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self' wss:; frame-ancestors 'none';" always;

    # Rate Limiting Zones
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=bridge:10m rate=1r/s;

    # Hide server information
    server_tokens off;

    # Request size limits
    client_max_body_size 10M;
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;

    # Timeout settings
    client_body_timeout 12;
    client_header_timeout 12;
    keepalive_timeout 15;
    send_timeout 10;

    # API endpoints
    location /api/ {
        limit_req zone=api burst=20 nodelay;

        # Block suspicious requests
        if ($request_method !~ ^(GET|POST|PUT|DELETE|PATCH)$) {
            return 405;
        }

        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Security headers for API
        proxy_hide_header X-Powered-By;
        proxy_hide_header Server;
    }

    # Authentication endpoints (stricter rate limiting)
    location /api/auth/ {
        limit_req zone=auth burst=5 nodelay;

        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Additional security for auth endpoints
        proxy_connect_timeout 5s;
        proxy_send_timeout 5s;
        proxy_read_timeout 5s;
    }

    # WebSocket endpoints for device bridges
    location /socket.io/ {
        limit_req zone=bridge burst=10 nodelay;

        proxy_pass http://127.0.0.1:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket specific timeouts
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }

    # Static files with caching
    location /static/ {
        alias /opt/mobile-automation-saas/app/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";

        # Security for static files
        location ~* \.(php|jsp|cgi)$ {
            deny all;
        }
    }

    # Block access to sensitive files
    location ~ /\. {
        deny all;
    }

    location ~ \.(sql|log|conf)$ {
        deny all;
    }

    # Admin interface (additional restrictions)
    location /admin/ {
        # Restrict to specific IP ranges
        allow ***********/24;  # Office network
        allow ************/24; # VPN network
        deny all;

        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Separate server block for metrics (internal only)
server {
    listen 127.0.0.1:8081;
    server_name localhost;

    location /metrics {
        proxy_pass http://127.0.0.1:5000/metrics;

        # Restrict to monitoring systems
        allow 127.0.0.1;
        allow 10.0.0.0/8;
        deny all;
    }
}
```

### 3.3 Authentication and Authorization Best Practices

**JWT Token Security Implementation:**
```python
import jwt
import secrets
from datetime import datetime, timedelta
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa

class SecureJWTManager:
    def __init__(self):
        # Use RSA keys for better security
        self.algorithm = "RS256"
        self.access_token_expires = timedelta(hours=1)
        self.refresh_token_expires = timedelta(days=30)

        # Generate or load RSA key pair
        self.private_key, self.public_key = self._load_or_generate_keys()

        # Token blacklist for revocation
        self.blacklisted_tokens = set()

    def _load_or_generate_keys(self):
        """Load existing RSA keys or generate new ones"""
        private_key_path = "/etc/mobile-automation-saas/jwt_private.pem"
        public_key_path = "/etc/mobile-automation-saas/jwt_public.pem"

        try:
            # Load existing keys
            with open(private_key_path, 'rb') as f:
                private_key = serialization.load_pem_private_key(
                    f.read(), password=None
                )

            with open(public_key_path, 'rb') as f:
                public_key = serialization.load_pem_public_key(f.read())

            return private_key, public_key

        except FileNotFoundError:
            # Generate new keys
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048
            )
            public_key = private_key.public_key()

            # Save keys securely
            os.makedirs(os.path.dirname(private_key_path), exist_ok=True)

            with open(private_key_path, 'wb') as f:
                f.write(private_key.private_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PrivateFormat.PKCS8,
                    encryption_algorithm=serialization.NoEncryption()
                ))

            with open(public_key_path, 'wb') as f:
                f.write(public_key.public_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PublicFormat.SubjectPublicKeyInfo
                ))

            # Set secure permissions
            os.chmod(private_key_path, 0o600)
            os.chmod(public_key_path, 0o644)

            return private_key, public_key

    def create_token_pair(self, user_id, tenant_id, permissions=None):
        """Create access and refresh token pair"""
        now = datetime.utcnow()
        jti_access = secrets.token_urlsafe(32)
        jti_refresh = secrets.token_urlsafe(32)

        # Access token with minimal claims
        access_payload = {
            "sub": str(user_id),
            "tenant_id": str(tenant_id),
            "type": "access",
            "permissions": permissions or [],
            "exp": now + self.access_token_expires,
            "iat": now,
            "nbf": now,
            "jti": jti_access,
            "iss": "mobile-automation-saas",
            "aud": "api.yourdomain.com"
        }

        # Refresh token (minimal information)
        refresh_payload = {
            "sub": str(user_id),
            "tenant_id": str(tenant_id),
            "type": "refresh",
            "exp": now + self.refresh_token_expires,
            "iat": now,
            "nbf": now,
            "jti": jti_refresh,
            "iss": "mobile-automation-saas",
            "aud": "api.yourdomain.com"
        }

        # Sign tokens
        access_token = jwt.encode(
            access_payload,
            self.private_key,
            algorithm=self.algorithm
        )

        refresh_token = jwt.encode(
            refresh_payload,
            self.private_key,
            algorithm=self.algorithm
        )

        # Store refresh token in database for revocation
        RefreshToken.create(
            jti=jti_refresh,
            user_id=user_id,
            tenant_id=tenant_id,
            expires_at=now + self.refresh_token_expires,
            created_at=now
        )

        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "Bearer",
            "expires_in": int(self.access_token_expires.total_seconds())
        }

    def verify_token(self, token, token_type="access"):
        """Verify and decode JWT token"""
        try:
            # Check if token is blacklisted
            if token in self.blacklisted_tokens:
                raise jwt.InvalidTokenError("Token has been revoked")

            # Decode and verify token
            payload = jwt.decode(
                token,
                self.public_key,
                algorithms=[self.algorithm],
                audience="api.yourdomain.com",
                issuer="mobile-automation-saas"
            )

            # Verify token type
            if payload.get("type") != token_type:
                raise jwt.InvalidTokenError(f"Invalid token type: {payload.get('type')}")

            # For refresh tokens, check database
            if token_type == "refresh":
                refresh_token = RefreshToken.query.filter_by(
                    jti=payload["jti"]
                ).first()

                if not refresh_token or refresh_token.revoked:
                    raise jwt.InvalidTokenError("Refresh token not found or revoked")

            return payload

        except jwt.ExpiredSignatureError:
            raise jwt.InvalidTokenError("Token has expired")
        except jwt.InvalidTokenError as e:
            raise jwt.InvalidTokenError(f"Invalid token: {str(e)}")

    def revoke_token(self, jti):
        """Revoke a token by adding to blacklist"""
        self.blacklisted_tokens.add(jti)

        # Also revoke in database if it's a refresh token
        refresh_token = RefreshToken.query.filter_by(jti=jti).first()
        if refresh_token:
            refresh_token.revoked = True
            refresh_token.revoked_at = datetime.utcnow()
            db.session.commit()

    def refresh_access_token(self, refresh_token):
        """Generate new access token from refresh token"""
        try:
            payload = self.verify_token(refresh_token, "refresh")

            # Create new access token
            return self.create_token_pair(
                user_id=payload["sub"],
                tenant_id=payload["tenant_id"]
            )["access_token"]

        except jwt.InvalidTokenError:
            raise ValueError("Invalid refresh token")

# Global JWT manager instance
jwt_manager = SecureJWTManager()
```

**Multi-Factor Authentication (MFA) Implementation:**
```python
import pyotp
import qrcode
import io
import base64
from cryptography.fernet import Fernet

class MFAManager:
    def __init__(self):
        self.encryption_key = self._load_encryption_key()
        self.fernet = Fernet(self.encryption_key)
        self.backup_codes_count = 10
        self.totp_window = 1  # Allow 1 step tolerance

    def _load_encryption_key(self):
        """Load or generate encryption key for MFA secrets"""
        key_path = "/etc/mobile-automation-saas/mfa_key"

        try:
            with open(key_path, 'rb') as f:
                return f.read()
        except FileNotFoundError:
            key = Fernet.generate_key()
            os.makedirs(os.path.dirname(key_path), exist_ok=True)

            with open(key_path, 'wb') as f:
                f.write(key)

            os.chmod(key_path, 0o600)
            return key

    def setup_totp(self, user_id, user_email):
        """Set up TOTP for user"""
        # Generate secret
        secret = pyotp.random_base32()

        # Create TOTP instance
        totp = pyotp.TOTP(secret)

        # Generate QR code
        provisioning_uri = totp.provisioning_uri(
            name=user_email,
            issuer_name="Mobile Automation SaaS"
        )

        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(provisioning_uri)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")

        # Convert to base64 for web display
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='PNG')
        img_str = base64.b64encode(img_buffer.getvalue()).decode()

        # Generate backup codes
        backup_codes = [secrets.token_hex(4).upper() for _ in range(self.backup_codes_count)]

        # Encrypt and store MFA data
        encrypted_secret = self.fernet.encrypt(secret.encode())
        encrypted_backup_codes = self.fernet.encrypt(
            json.dumps(backup_codes).encode()
        )

        # Store in database (not enabled until verified)
        user_mfa = UserMFA(
            user_id=user_id,
            secret_encrypted=encrypted_secret,
            backup_codes_encrypted=encrypted_backup_codes,
            enabled=False,
            created_at=datetime.utcnow()
        )

        db.session.add(user_mfa)
        db.session.commit()

        return {
            "secret": secret,
            "qr_code_data": f"data:image/png;base64,{img_str}",
            "backup_codes": backup_codes,
            "provisioning_uri": provisioning_uri
        }

    def verify_totp_setup(self, user_id, token):
        """Verify TOTP setup and enable MFA"""
        user_mfa = UserMFA.query.filter_by(user_id=user_id).first()
        if not user_mfa:
            return False

        # Decrypt secret
        secret = self.fernet.decrypt(user_mfa.secret_encrypted).decode()
        totp = pyotp.TOTP(secret)

        # Verify token
        if totp.verify(token, valid_window=self.totp_window):
            user_mfa.enabled = True
            user_mfa.verified_at = datetime.utcnow()
            db.session.commit()

            # Log MFA enablement
            AuditLog.create(
                user_id=user_id,
                action="mfa_enabled",
                details={"method": "totp"}
            )

            return True

        return False

    def verify_totp_token(self, user_id, token):
        """Verify TOTP token for authentication"""
        user_mfa = UserMFA.query.filter_by(
            user_id=user_id, enabled=True
        ).first()

        if not user_mfa:
            return False

        # Check if it's a backup code
        if self._verify_backup_code(user_mfa, token):
            return True

        # Verify TOTP token
        secret = self.fernet.decrypt(user_mfa.secret_encrypted).decode()
        totp = pyotp.TOTP(secret)

        if totp.verify(token, valid_window=self.totp_window):
            # Update last used timestamp
            user_mfa.last_used_at = datetime.utcnow()
            db.session.commit()
            return True

        return False

    def _verify_backup_code(self, user_mfa, code):
        """Verify and consume backup code"""
        backup_codes_json = self.fernet.decrypt(
            user_mfa.backup_codes_encrypted
        ).decode()
        backup_codes = json.loads(backup_codes_json)

        if code.upper() in backup_codes:
            # Remove used backup code
            backup_codes.remove(code.upper())

            # Re-encrypt and save
            user_mfa.backup_codes_encrypted = self.fernet.encrypt(
                json.dumps(backup_codes).encode()
            )
            user_mfa.last_used_at = datetime.utcnow()
            db.session.commit()

            # Log backup code usage
            AuditLog.create(
                user_id=user_mfa.user_id,
                action="backup_code_used",
                details={"remaining_codes": len(backup_codes)}
            )

            return True

        return False

    def disable_mfa(self, user_id):
        """Disable MFA for user"""
        user_mfa = UserMFA.query.filter_by(user_id=user_id).first()
        if user_mfa:
            user_mfa.enabled = False
            user_mfa.disabled_at = datetime.utcnow()
            db.session.commit()

            # Log MFA disablement
            AuditLog.create(
                user_id=user_id,
                action="mfa_disabled",
                details={"method": "totp"}
            )

# Global MFA manager instance
mfa_manager = MFAManager()
```

**Role-Based Access Control (RBAC):**
```python
from enum import Enum
from functools import wraps

class Permission(Enum):
    # Device management
    DEVICE_VIEW = "device:view"
    DEVICE_MANAGE = "device:manage"
    DEVICE_EXECUTE = "device:execute"

    # Test management
    TEST_VIEW = "test:view"
    TEST_CREATE = "test:create"
    TEST_EXECUTE = "test:execute"
    TEST_DELETE = "test:delete"

    # User management
    USER_VIEW = "user:view"
    USER_INVITE = "user:invite"
    USER_MANAGE = "user:manage"

    # Tenant administration
    TENANT_VIEW = "tenant:view"
    TENANT_MANAGE = "tenant:manage"
    TENANT_BILLING = "tenant:billing"

    # System administration
    SYSTEM_ADMIN = "system:admin"
    AUDIT_VIEW = "audit:view"

class Role(Enum):
    # Tenant roles
    TENANT_OWNER = "tenant_owner"
    TENANT_ADMIN = "tenant_admin"
    TENANT_USER = "tenant_user"
    TENANT_VIEWER = "tenant_viewer"

    # System roles
    SYSTEM_ADMIN = "system_admin"
    SUPPORT_AGENT = "support_agent"

class RBACManager:
    def __init__(self):
        self.role_permissions = {
            Role.TENANT_OWNER: [
                Permission.DEVICE_VIEW, Permission.DEVICE_MANAGE, Permission.DEVICE_EXECUTE,
                Permission.TEST_VIEW, Permission.TEST_CREATE, Permission.TEST_EXECUTE, Permission.TEST_DELETE,
                Permission.USER_VIEW, Permission.USER_INVITE, Permission.USER_MANAGE,
                Permission.TENANT_VIEW, Permission.TENANT_MANAGE, Permission.TENANT_BILLING,
                Permission.AUDIT_VIEW
            ],
            Role.TENANT_ADMIN: [
                Permission.DEVICE_VIEW, Permission.DEVICE_MANAGE, Permission.DEVICE_EXECUTE,
                Permission.TEST_VIEW, Permission.TEST_CREATE, Permission.TEST_EXECUTE, Permission.TEST_DELETE,
                Permission.USER_VIEW, Permission.USER_INVITE, Permission.USER_MANAGE,
                Permission.TENANT_VIEW, Permission.AUDIT_VIEW
            ],
            Role.TENANT_USER: [
                Permission.DEVICE_VIEW, Permission.DEVICE_EXECUTE,
                Permission.TEST_VIEW, Permission.TEST_CREATE, Permission.TEST_EXECUTE,
                Permission.USER_VIEW
            ],
            Role.TENANT_VIEWER: [
                Permission.DEVICE_VIEW,
                Permission.TEST_VIEW,
                Permission.USER_VIEW
            ],
            Role.SYSTEM_ADMIN: [perm for perm in Permission],  # All permissions
            Role.SUPPORT_AGENT: [
                Permission.DEVICE_VIEW, Permission.TEST_VIEW, Permission.USER_VIEW,
                Permission.TENANT_VIEW, Permission.AUDIT_VIEW
            ]
        }

    def get_user_permissions(self, user_id, tenant_id=None):
        """Get all permissions for a user"""
        permissions = set()

        # Get tenant-specific roles
        if tenant_id:
            tenant_roles = TenantUserRole.query.filter_by(
                user_id=user_id, tenant_id=tenant_id
            ).all()

            for tenant_role in tenant_roles:
                role_perms = self.role_permissions.get(tenant_role.role, [])
                permissions.update(role_perms)

        # Get system-wide roles
        system_roles = SystemUserRole.query.filter_by(user_id=user_id).all()
        for system_role in system_roles:
            role_perms = self.role_permissions.get(system_role.role, [])
            permissions.update(role_perms)

        return list(permissions)

    def has_permission(self, user_id, permission, tenant_id=None):
        """Check if user has specific permission"""
        user_permissions = self.get_user_permissions(user_id, tenant_id)
        return permission in user_permissions

    def assign_tenant_role(self, user_id, tenant_id, role):
        """Assign role to user within tenant"""
        existing_role = TenantUserRole.query.filter_by(
            user_id=user_id, tenant_id=tenant_id
        ).first()

        if existing_role:
            existing_role.role = role
            existing_role.updated_at = datetime.utcnow()
        else:
            new_role = TenantUserRole(
                user_id=user_id,
                tenant_id=tenant_id,
                role=role,
                created_at=datetime.utcnow()
            )
            db.session.add(new_role)

        db.session.commit()

        # Log role assignment
        AuditLog.create(
            user_id=user_id,
            tenant_id=tenant_id,
            action="role_assigned",
            details={"role": role.value}
        )

# Global RBAC manager
rbac_manager = RBACManager()

def require_permission(permission):
    """Decorator to require specific permission"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not g.get('current_user_id'):
                abort(401, "Authentication required")

            tenant_id = g.get('current_tenant_id')
            user_id = g.current_user_id

            if not rbac_manager.has_permission(user_id, permission, tenant_id):
                abort(403, f"Permission denied: {permission.value}")

            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_role(role):
    """Decorator to require specific role"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not g.get('current_user_id'):
                abort(401, "Authentication required")

            tenant_id = g.get('current_tenant_id')
            user_id = g.current_user_id

            # Check tenant role
            if tenant_id:
                tenant_role = TenantUserRole.query.filter_by(
                    user_id=user_id, tenant_id=tenant_id
                ).first()

                if tenant_role and tenant_role.role == role:
                    return f(*args, **kwargs)

            # Check system role
            system_role = SystemUserRole.query.filter_by(
                user_id=user_id
            ).first()

            if system_role and system_role.role == role:
                return f(*args, **kwargs)

            abort(403, f"Role required: {role.value}")
        return decorated_function
    return decorator

### 3.4 Database Security and Backup Strategies

**PostgreSQL Security Hardening:**
```postgresql
-- postgresql.conf security configuration
# Connection and authentication
ssl = on
ssl_cert_file = '/etc/ssl/certs/postgresql.crt'
ssl_key_file = '/etc/ssl/private/postgresql.key'
ssl_ca_file = '/etc/ssl/certs/ca.crt'
ssl_crl_file = '/etc/ssl/certs/ca.crl'
ssl_min_protocol_version = 'TLSv1.2'
ssl_ciphers = 'ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256'
ssl_prefer_server_ciphers = on

# Password encryption
password_encryption = scram-sha-256
db_user_namespace = off

# Logging and auditing
log_connections = on
log_disconnections = on
log_statement = 'ddl'
log_min_duration_statement = 1000
log_checkpoints = on
log_lock_waits = on
log_temp_files = 0

# Resource limits and performance
max_connections = 200
shared_buffers = 64GB
effective_cache_size = 192GB
work_mem = 256MB
maintenance_work_mem = 2GB

# Security settings
shared_preload_libraries = 'pg_stat_statements,auto_explain,pg_audit'
track_activities = on
track_counts = on
track_functions = all

# Prevent unauthorized access
listen_addresses = 'localhost,10.0.0.0/8'
port = 5432
```

**Database User Security:**
```sql
-- Create dedicated application user with minimal privileges
CREATE USER saas_app WITH PASSWORD 'secure_generated_password';

-- Grant only necessary privileges
GRANT CONNECT ON DATABASE mobile_automation_saas TO saas_app;
GRANT USAGE ON SCHEMA public TO saas_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO saas_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO saas_app;

-- Create read-only user for reporting
CREATE USER saas_readonly WITH PASSWORD 'readonly_password';
GRANT CONNECT ON DATABASE mobile_automation_saas TO saas_readonly;
GRANT USAGE ON SCHEMA public TO saas_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO saas_readonly;

-- Create backup user
CREATE USER saas_backup WITH PASSWORD 'backup_password';
GRANT CONNECT ON DATABASE mobile_automation_saas TO saas_backup;
GRANT USAGE ON SCHEMA public TO saas_backup;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO saas_backup;

-- Revoke public schema privileges
REVOKE ALL ON SCHEMA public FROM public;
REVOKE ALL ON DATABASE mobile_automation_saas FROM public;

-- Set up connection limits
ALTER USER saas_app CONNECTION LIMIT 50;
ALTER USER saas_readonly CONNECTION LIMIT 10;
ALTER USER saas_backup CONNECTION LIMIT 2;
```

**Comprehensive Backup Strategy:**
```bash
#!/bin/bash
# /opt/mobile-automation-saas/scripts/backup_database.sh

set -euo pipefail

# Configuration
BACKUP_DIR="/opt/backups/postgresql"
REMOTE_BACKUP_DIR="s3://your-backup-bucket/postgresql"
DB_NAME="mobile_automation_saas"
DB_USER="saas_backup"
RETENTION_DAYS=30
RETENTION_WEEKS=12
RETENTION_MONTHS=12

# Logging
LOG_FILE="/var/log/mobile-automation-saas/backup.log"
exec 1> >(tee -a "$LOG_FILE")
exec 2>&1

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Create backup directories
mkdir -p "$BACKUP_DIR"/{daily,weekly,monthly}

# Generate timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
DAY_OF_WEEK=$(date +%u)
DAY_OF_MONTH=$(date +%d)

# Determine backup type
if [ "$DAY_OF_MONTH" = "01" ]; then
    BACKUP_TYPE="monthly"
    BACKUP_SUBDIR="monthly"
elif [ "$DAY_OF_WEEK" = "7" ]; then
    BACKUP_TYPE="weekly"
    BACKUP_SUBDIR="weekly"
else
    BACKUP_TYPE="daily"
    BACKUP_SUBDIR="daily"
fi

BACKUP_FILE="$BACKUP_DIR/$BACKUP_SUBDIR/backup_${BACKUP_TYPE}_$TIMESTAMP.sql"

log "Starting $BACKUP_TYPE backup: $BACKUP_FILE"

# Create database backup
pg_dump \
    -h localhost \
    -U "$DB_USER" \
    -d "$DB_NAME" \
    --verbose \
    --no-password \
    --format=custom \
    --compress=9 \
    --file="$BACKUP_FILE"

if [ $? -eq 0 ]; then
    log "Database backup completed successfully"
else
    log "ERROR: Database backup failed"
    exit 1
fi

# Encrypt backup
GPG_RECIPIENT="<EMAIL>"
gpg --trust-model always --encrypt --recipient "$GPG_RECIPIENT" --output "$BACKUP_FILE.gpg" "$BACKUP_FILE"

if [ $? -eq 0 ]; then
    log "Backup encryption completed"
    rm "$BACKUP_FILE"  # Remove unencrypted backup
else
    log "ERROR: Backup encryption failed"
    exit 1
fi

# Upload to cloud storage
aws s3 cp "$BACKUP_FILE.gpg" "$REMOTE_BACKUP_DIR/$BACKUP_SUBDIR/" \
    --storage-class STANDARD_IA \
    --server-side-encryption AES256

if [ $? -eq 0 ]; then
    log "Backup uploaded to cloud storage"
else
    log "WARNING: Cloud upload failed, backup retained locally"
fi

# Cleanup old backups
log "Cleaning up old backups"

# Daily backups (keep for 30 days)
find "$BACKUP_DIR/daily" -name "*.gpg" -mtime +$RETENTION_DAYS -delete

# Weekly backups (keep for 12 weeks)
find "$BACKUP_DIR/weekly" -name "*.gpg" -mtime +$((RETENTION_WEEKS * 7)) -delete

# Monthly backups (keep for 12 months)
find "$BACKUP_DIR/monthly" -name "*.gpg" -mtime +$((RETENTION_MONTHS * 30)) -delete

# Test backup integrity (weekly)
if [ "$DAY_OF_WEEK" = "7" ]; then
    log "Testing backup integrity"

    TEST_DB="test_restore_$TIMESTAMP"

    # Create test database
    createdb -h localhost -U postgres "$TEST_DB"

    # Restore backup
    gpg --decrypt "$BACKUP_FILE.gpg" | pg_restore \
        -h localhost \
        -U postgres \
        -d "$TEST_DB" \
        --verbose

    if [ $? -eq 0 ]; then
        log "Backup integrity test passed"

        # Verify data
        RECORD_COUNT=$(psql -h localhost -U postgres -d "$TEST_DB" -t -c "SELECT COUNT(*) FROM tenants;")
        log "Restored database contains $RECORD_COUNT tenant records"

    else
        log "ERROR: Backup integrity test failed"
        # Send alert
        echo "Backup integrity test failed for $BACKUP_FILE.gpg" | \
            mail -s "CRITICAL: Backup Integrity Failure" <EMAIL>
    fi

    # Cleanup test database
    dropdb -h localhost -U postgres "$TEST_DB"
fi

# Generate backup report
BACKUP_SIZE=$(du -h "$BACKUP_FILE.gpg" | cut -f1)
log "Backup completed: $BACKUP_FILE.gpg ($BACKUP_SIZE)"

# Update backup status in monitoring
curl -X POST "http://localhost:9090/api/v1/admin/tsdb/snapshot" \
    -H "Content-Type: application/json" \
    -d "{\"backup_status\": \"success\", \"backup_size\": \"$BACKUP_SIZE\", \"timestamp\": \"$(date -Iseconds)\"}" \
    || log "WARNING: Failed to update monitoring"

log "$BACKUP_TYPE backup process completed successfully"
```

**Point-in-Time Recovery Setup:**
```bash
#!/bin/bash
# /opt/mobile-automation-saas/scripts/setup_pitr.sh

# Configure WAL archiving for point-in-time recovery
cat >> /etc/postgresql/14/main/postgresql.conf << EOF

# WAL archiving configuration
wal_level = replica
archive_mode = on
archive_command = 'test ! -f /opt/backups/postgresql/wal_archive/%f && cp %p /opt/backups/postgresql/wal_archive/%f'
archive_timeout = 300  # Archive every 5 minutes

# Replication settings
max_wal_senders = 3
wal_keep_size = 1GB
hot_standby = on

EOF

# Create WAL archive directory
mkdir -p /opt/backups/postgresql/wal_archive
chown postgres:postgres /opt/backups/postgresql/wal_archive
chmod 700 /opt/backups/postgresql/wal_archive

# Setup WAL-E for cloud archiving
pip3 install wal-e[aws]

# Configure WAL-E
cat > /etc/wal-e.d/env << EOF
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
WALE_S3_PREFIX=s3://your-backup-bucket/wal-archive
WALE_S3_ENDPOINT=https://s3.amazonaws.com
EOF

chmod 600 /etc/wal-e.d/env

# Update archive command to use WAL-E
sed -i "s/archive_command = .*/archive_command = 'envdir \/etc\/wal-e.d\/env wal-e wal-push %p'/" \
    /etc/postgresql/14/main/postgresql.conf

# Restart PostgreSQL
systemctl restart postgresql

log "Point-in-time recovery setup completed"
```

### 3.5 Monitoring and Incident Response Procedures

**Comprehensive Monitoring Stack:**
```yaml
# docker-compose.monitoring.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:v2.40.0
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/rules:/etc/prometheus/rules
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=90d'
      - '--storage.tsdb.retention.size=50GB'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
      - '--alertmanager.notification-queue-capacity=10000'
    restart: unless-stopped

  alertmanager:
    image: prom/alertmanager:v0.25.0
    container_name: alertmanager
    ports:
      - "9093:9093"
    volumes:
      - ./monitoring/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager_data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
      - '--cluster.listen-address=0.0.0.0:9094'
    restart: unless-stopped

  grafana:
    image: grafana/grafana:9.3.0
    container_name: grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=secure_admin_password
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_USERS_ALLOW_ORG_CREATE=false
      - GF_AUTH_ANONYMOUS_ENABLED=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    restart: unless-stopped

  node_exporter:
    image: prom/node-exporter:v1.5.0
    container_name: node_exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    restart: unless-stopped

  postgres_exporter:
    image: prometheuscommunity/postgres-exporter:v0.11.1
    container_name: postgres_exporter
    ports:
      - "9187:9187"
    environment:
      - DATA_SOURCE_NAME=postgresql://monitoring_user:monitoring_password@localhost:5432/mobile_automation_saas?sslmode=require
    restart: unless-stopped

  redis_exporter:
    image: oliver006/redis_exporter:v1.45.0
    container_name: redis_exporter
    ports:
      - "9121:9121"
    environment:
      - REDIS_ADDR=redis://localhost:6379
    restart: unless-stopped

  loki:
    image: grafana/loki:2.7.0
    container_name: loki
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki.yml:/etc/loki/local-config.yaml
      - loki_data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    restart: unless-stopped

  promtail:
    image: grafana/promtail:2.7.0
    container_name: promtail
    volumes:
      - ./monitoring/promtail.yml:/etc/promtail/config.yml
      - /var/log:/var/log:ro
      - /opt/mobile-automation-saas/logs:/app/logs:ro
    command: -config.file=/etc/promtail/config.yml
    restart: unless-stopped

volumes:
  prometheus_data:
  alertmanager_data:
  grafana_data:
  loki_data:
```

**Critical Alerting Rules:**
```yaml
# monitoring/rules/saas_platform.yml
groups:
- name: saas_platform_critical
  rules:

  # Application availability
  - alert: ApplicationDown
    expr: up{job="saas_app"} == 0
    for: 1m
    labels:
      severity: critical
      team: platform
    annotations:
      summary: "SaaS application is down"
      description: "The SaaS application has been down for more than 1 minute"
      runbook_url: "https://docs.yourdomain.com/runbooks/app-down"

  # High error rate
  - alert: HighErrorRate
    expr: rate(flask_http_request_exceptions_total[5m]) > 0.1
    for: 2m
    labels:
      severity: critical
      team: platform
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value }} errors per second"
      runbook_url: "https://docs.yourdomain.com/runbooks/high-error-rate"

  # Database connectivity
  - alert: DatabaseDown
    expr: postgresql_up == 0
    for: 30s
    labels:
      severity: critical
      team: database
    annotations:
      summary: "PostgreSQL database is down"
      description: "PostgreSQL database has been unreachable for 30 seconds"
      runbook_url: "https://docs.yourdomain.com/runbooks/database-down"

  # High database connections
  - alert: HighDatabaseConnections
    expr: postgresql_stat_activity_count > 180
    for: 5m
    labels:
      severity: warning
      team: database
    annotations:
      summary: "High number of database connections"
      description: "Database has {{ $value }} active connections (limit: 200)"

  # Tenant data isolation breach
  - alert: TenantDataLeakage
    expr: increase(audit_logs_cross_tenant_access_total[5m]) > 0
    for: 0m
    labels:
      severity: critical
      team: security
    annotations:
      summary: "Potential tenant data leakage detected"
      description: "Cross-tenant access attempt detected in audit logs"
      runbook_url: "https://docs.yourdomain.com/runbooks/security-breach"

  # High memory usage
  - alert: HighMemoryUsage
    expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
    for: 5m
    labels:
      severity: warning
      team: infrastructure
    annotations:
      summary: "High memory usage"
      description: "Memory usage is above 90%"

  # Disk space
  - alert: DiskSpaceLow
    expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) < 0.1
    for: 5m
    labels:
      severity: warning
      team: infrastructure
    annotations:
      summary: "Low disk space"
      description: "Disk space is below 10% on {{ $labels.mountpoint }}"

  # SSL certificate expiry
  - alert: SSLCertificateExpiry
    expr: probe_ssl_earliest_cert_expiry - time() < 86400 * 7
    for: 1h
    labels:
      severity: warning
      team: infrastructure
    annotations:
      summary: "SSL certificate expiring soon"
      description: "SSL certificate expires in {{ $value | humanizeDuration }}"

  # Device bridge connectivity
  - alert: DeviceBridgeDisconnected
    expr: increase(device_bridge_disconnections_total[10m]) > 5
    for: 2m
    labels:
      severity: warning
      team: platform
    annotations:
      summary: "High device bridge disconnection rate"
      description: "{{ $value }} device bridges disconnected in the last 10 minutes"

- name: saas_platform_performance
  rules:

  # Response time
  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(flask_request_duration_seconds_bucket[5m])) > 2
    for: 5m
    labels:
      severity: warning
      team: platform
    annotations:
      summary: "High response time"
      description: "95th percentile response time is {{ $value }}s"

  # Database query performance
  - alert: SlowDatabaseQueries
    expr: rate(postgresql_stat_statements_mean_time_ms[5m]) > 1000
    for: 5m
    labels:
      severity: warning
      team: database
    annotations:
      summary: "Slow database queries detected"
      description: "Average query time is {{ $value }}ms"

  # WebSocket connection issues
  - alert: WebSocketConnectionFailures
    expr: rate(websocket_connection_failures_total[5m]) > 0.1
    for: 3m
    labels:
      severity: warning
      team: platform
    annotations:
      summary: "WebSocket connection failures"
      description: "{{ $value }} WebSocket connection failures per second"
```

**Incident Response Procedures:**
```python
# /opt/mobile-automation-saas/scripts/incident_response.py

import json
import requests
import subprocess
import logging
from datetime import datetime
from enum import Enum

class IncidentSeverity(Enum):
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class IncidentResponseManager:
    def __init__(self):
        self.slack_webhook = os.getenv("SLACK_INCIDENT_WEBHOOK")
        self.pagerduty_key = os.getenv("PAGERDUTY_INTEGRATION_KEY")
        self.email_recipients = [
            "<EMAIL>",
            "<EMAIL>"
        ]

        self.runbooks = {
            "ApplicationDown": self.handle_application_down,
            "DatabaseDown": self.handle_database_down,
            "TenantDataLeakage": self.handle_security_breach,
            "HighErrorRate": self.handle_high_error_rate,
            "DiskSpaceLow": self.handle_disk_space_low
        }

    def handle_alert(self, alert_data):
        """Main alert handler"""
        alert_name = alert_data.get("alertname")
        severity = alert_data.get("labels", {}).get("severity", "unknown")

        logging.info(f"Handling alert: {alert_name} (severity: {severity})")

        # Execute specific runbook if available
        if alert_name in self.runbooks:
            self.runbooks[alert_name](alert_data)
        else:
            self.handle_generic_alert(alert_data)

        # Send notifications
        self.send_notifications(alert_data)

    def handle_application_down(self, alert_data):
        """Handle application downtime"""
        logging.critical("Application down - executing recovery procedures")

        # Check application status
        app_status = self.check_application_health()

        if not app_status["healthy"]:
            # Restart application
            logging.info("Attempting to restart application")
            subprocess.run(["supervisorctl", "restart", "mobile-automation-saas"])

            # Wait and check again
            time.sleep(30)
            app_status = self.check_application_health()

            if app_status["healthy"]:
                logging.info("Application restart successful")
                self.send_recovery_notification(alert_data)
            else:
                logging.error("Application restart failed - escalating")
                self.escalate_incident(alert_data, "Application restart failed")

    def handle_database_down(self, alert_data):
        """Handle database connectivity issues"""
        logging.critical("Database down - executing recovery procedures")

        # Check database status
        db_status = self.check_database_health()

        if not db_status["healthy"]:
            # Check if it's a connection issue or actual downtime
            if db_status["error_type"] == "connection":
                # Restart connection pool
                logging.info("Restarting database connection pool")
                subprocess.run(["supervisorctl", "restart", "pgbouncer"])
            else:
                # Database is actually down - attempt restart
                logging.info("Attempting to restart PostgreSQL")
                subprocess.run(["systemctl", "restart", "postgresql"])

            # Wait and verify
            time.sleep(60)
            db_status = self.check_database_health()

            if not db_status["healthy"]:
                logging.error("Database recovery failed - initiating failover")
                self.initiate_database_failover()

    def handle_security_breach(self, alert_data):
        """Handle potential security breaches"""
        logging.critical("Security breach detected - executing security procedures")

        # Immediately notify security team
        self.send_security_alert(alert_data)

        # Collect forensic data
        forensic_data = self.collect_forensic_data()

        # Temporarily increase logging verbosity
        self.increase_audit_logging()

        # Block suspicious IPs if identified
        suspicious_ips = self.identify_suspicious_ips()
        for ip in suspicious_ips:
            self.block_ip_address(ip)

        # Create security incident ticket
        self.create_security_incident(alert_data, forensic_data)

    def handle_high_error_rate(self, alert_data):
        """Handle high error rates"""
        logging.warning("High error rate detected - analyzing errors")

        # Analyze recent errors
        error_analysis = self.analyze_recent_errors()

        # Check if it's a specific endpoint or tenant
        if error_analysis["pattern"] == "specific_endpoint":
            # Temporarily rate limit the problematic endpoint
            self.apply_emergency_rate_limit(error_analysis["endpoint"])
        elif error_analysis["pattern"] == "specific_tenant":
            # Temporarily throttle the problematic tenant
            self.apply_tenant_throttling(error_analysis["tenant_id"])

        # Scale up if needed
        if error_analysis["cause"] == "high_load":
            self.trigger_auto_scaling()

    def handle_disk_space_low(self, alert_data):
        """Handle low disk space"""
        logging.warning("Low disk space detected - cleaning up")

        # Clean up old logs
        subprocess.run([
            "find", "/var/log", "-name", "*.log", "-mtime", "+7", "-delete"
        ])

        # Clean up old backups
        subprocess.run([
            "find", "/opt/backups", "-name", "*.sql.gz", "-mtime", "+30", "-delete"
        ])

        # Compress large log files
        subprocess.run([
            "find", "/opt/mobile-automation-saas/logs",
            "-name", "*.log", "-size", "+100M",
            "-exec", "gzip", "{}", ";"
        ])

        # Check if cleanup was sufficient
        disk_usage = self.check_disk_usage()
        if disk_usage > 85:  # Still high
            logging.error("Disk cleanup insufficient - manual intervention required")
            self.escalate_incident(alert_data, "Disk cleanup insufficient")

    def send_notifications(self, alert_data):
        """Send notifications via multiple channels"""
        severity = alert_data.get("labels", {}).get("severity", "unknown")

        # Slack notification
        if self.slack_webhook:
            self.send_slack_notification(alert_data)

        # PagerDuty for critical alerts
        if severity == "critical" and self.pagerduty_key:
            self.send_pagerduty_alert(alert_data)

        # Email notification
        self.send_email_notification(alert_data)

    def send_slack_notification(self, alert_data):
        """Send Slack notification"""
        color_map = {
            "critical": "#FF0000",
            "warning": "#FFA500",
            "info": "#00FF00"
        }

        severity = alert_data.get("labels", {}).get("severity", "unknown")
        color = color_map.get(severity, "#808080")

        payload = {
            "attachments": [{
                "color": color,
                "title": f"Alert: {alert_data.get('alertname')}",
                "text": alert_data.get("annotations", {}).get("description"),
                "fields": [
                    {
                        "title": "Severity",
                        "value": severity,
                        "short": True
                    },
                    {
                        "title": "Time",
                        "value": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "short": True
                    }
                ]
            }]
        }

        try:
            requests.post(self.slack_webhook, json=payload, timeout=10)
        except Exception as e:
            logging.error(f"Failed to send Slack notification: {e}")

    def check_application_health(self):
        """Check application health"""
        try:
            response = requests.get("http://localhost:5000/health", timeout=5)
            return {
                "healthy": response.status_code == 200,
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds()
            }
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e)
            }

    def check_database_health(self):
        """Check database connectivity"""
        try:
            result = subprocess.run([
                "psql", "-h", "localhost", "-U", "saas_app",
                "-d", "mobile_automation_saas", "-c", "SELECT 1;"
            ], capture_output=True, timeout=10)

            return {
                "healthy": result.returncode == 0,
                "error_type": "none" if result.returncode == 0 else "connection"
            }
        except subprocess.TimeoutExpired:
            return {
                "healthy": False,
                "error_type": "timeout"
            }
        except Exception as e:
            return {
                "healthy": False,
                "error_type": "unknown",
                "error": str(e)
            }

# Initialize incident response manager
incident_manager = IncidentResponseManager()

if __name__ == "__main__":
    # This script can be called by Alertmanager webhook
    import sys

    if len(sys.argv) > 1:
        alert_data = json.loads(sys.argv[1])
        incident_manager.handle_alert(alert_data)

### 3.6 Compliance Considerations for Customer Data

**GDPR Compliance Implementation:**
```python
# /opt/mobile-automation-saas/app/compliance/gdpr.py

import json
import hashlib
from datetime import datetime, timedelta
from cryptography.fernet import Fernet

class GDPRComplianceManager:
    def __init__(self):
        self.encryption_key = self._load_encryption_key()
        self.fernet = Fernet(self.encryption_key)
        self.data_retention_periods = {
            "user_data": timedelta(days=2555),  # 7 years
            "audit_logs": timedelta(days=2555), # 7 years
            "test_data": timedelta(days=1095),  # 3 years
            "device_data": timedelta(days=365), # 1 year
            "session_data": timedelta(days=30)  # 30 days
        }

    def _load_encryption_key(self):
        """Load encryption key for PII data"""
        key_path = "/etc/mobile-automation-saas/gdpr_encryption_key"
        try:
            with open(key_path, 'rb') as f:
                return f.read()
        except FileNotFoundError:
            key = Fernet.generate_key()
            os.makedirs(os.path.dirname(key_path), exist_ok=True)
            with open(key_path, 'wb') as f:
                f.write(key)
            os.chmod(key_path, 0o600)
            return key

    def anonymize_user_data(self, user_id, reason="user_request"):
        """GDPR Article 17 - Right to erasure"""
        try:
            user = User.query.get(user_id)
            if not user:
                raise ValueError(f"User {user_id} not found")

            # Generate anonymized identifier
            anonymized_id = f"deleted_user_{hashlib.sha256(str(user_id).encode()).hexdigest()[:8]}"

            # Store original data for audit (encrypted)
            original_data = {
                "user_id": str(user_id),
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "phone": user.phone,
                "deletion_date": datetime.utcnow().isoformat(),
                "reason": reason
            }

            # Create deletion record
            deletion_record = DataDeletionRecord(
                original_user_id=user_id,
                anonymized_identifier=anonymized_id,
                deletion_reason=reason,
                original_data_encrypted=self.fernet.encrypt(
                    json.dumps(original_data).encode()
                ),
                deleted_at=datetime.utcnow()
            )
            db.session.add(deletion_record)

            # Anonymize user data
            user.email = f"{anonymized_id}@deleted.local"
            user.first_name = "Deleted"
            user.last_name = "User"
            user.phone = None
            user.deleted_at = datetime.utcnow()
            user.deletion_reason = reason

            # Anonymize related data while preserving audit trail
            self._anonymize_related_data(user_id, anonymized_id)

            db.session.commit()

            # Log the deletion
            AuditLog.create(
                user_id=user_id,
                action="gdpr_data_deletion",
                details={
                    "anonymized_id": anonymized_id,
                    "reason": reason,
                    "compliance_framework": "GDPR Article 17"
                }
            )

            return {
                "success": True,
                "anonymized_id": anonymized_id,
                "deletion_date": datetime.utcnow().isoformat()
            }

        except Exception as e:
            db.session.rollback()
            logging.error(f"GDPR deletion failed for user {user_id}: {e}")
            raise

    def _anonymize_related_data(self, user_id, anonymized_id):
        """Anonymize data in related tables"""
        # Anonymize audit logs (keep for compliance but remove PII)
        AuditLog.query.filter_by(user_id=user_id).update({
            "user_email": f"{anonymized_id}@deleted.local",
            "user_name": "Deleted User"
        })

        # Anonymize test execution data
        TestExecution.query.filter_by(user_id=user_id).update({
            "user_name": "Deleted User"
        })

        # Remove device associations but keep device data for tenant
        UserDevice.query.filter_by(user_id=user_id).delete()

        # Anonymize session data
        UserSession.query.filter_by(user_id=user_id).update({
            "user_identifier": anonymized_id
        })

    def export_user_data(self, user_id):
        """GDPR Article 20 - Right to data portability"""
        try:
            user = User.query.get(user_id)
            if not user:
                raise ValueError(f"User {user_id} not found")

            # Collect all user data
            export_data = {
                "export_metadata": {
                    "user_id": str(user_id),
                    "export_date": datetime.utcnow().isoformat(),
                    "compliance_framework": "GDPR Article 20",
                    "data_format": "JSON"
                },
                "personal_information": {
                    "email": user.email,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "phone": user.phone,
                    "created_at": user.created_at.isoformat() if user.created_at else None,
                    "last_login": user.last_login.isoformat() if user.last_login else None
                },
                "tenant_associations": [
                    {
                        "tenant_id": str(assoc.tenant_id),
                        "tenant_name": assoc.tenant.name,
                        "role": assoc.role,
                        "joined_at": assoc.created_at.isoformat()
                    }
                    for assoc in user.tenant_associations
                ],
                "devices": [
                    {
                        "device_id": str(device.id),
                        "device_name": device.name,
                        "platform": device.platform,
                        "added_at": device.created_at.isoformat()
                    }
                    for device in user.devices
                ],
                "test_executions": [
                    {
                        "execution_id": str(execution.id),
                        "test_name": execution.test_name,
                        "status": execution.status,
                        "executed_at": execution.created_at.isoformat(),
                        "duration": execution.duration_seconds
                    }
                    for execution in user.test_executions.limit(1000)  # Limit for performance
                ],
                "audit_logs": [
                    {
                        "timestamp": log.timestamp.isoformat(),
                        "action": log.action,
                        "ip_address": log.ip_address,
                        "user_agent": log.user_agent
                    }
                    for log in user.audit_logs.limit(1000)  # Limit for performance
                ]
            }

            # Create export record
            export_record = DataExportRecord(
                user_id=user_id,
                export_type="gdpr_full_export",
                exported_at=datetime.utcnow(),
                record_count=sum([
                    len(export_data["tenant_associations"]),
                    len(export_data["devices"]),
                    len(export_data["test_executions"]),
                    len(export_data["audit_logs"])
                ])
            )
            db.session.add(export_record)
            db.session.commit()

            # Log the export
            AuditLog.create(
                user_id=user_id,
                action="gdpr_data_export",
                details={
                    "export_id": str(export_record.id),
                    "record_count": export_record.record_count,
                    "compliance_framework": "GDPR Article 20"
                }
            )

            return json.dumps(export_data, indent=2, default=str)

        except Exception as e:
            logging.error(f"GDPR export failed for user {user_id}: {e}")
            raise

    def process_data_subject_request(self, request_type, user_email, details=None):
        """Process various GDPR data subject requests"""
        user = User.query.filter_by(email=user_email).first()
        if not user:
            raise ValueError(f"User with email {user_email} not found")

        request_handlers = {
            "access": lambda: self.export_user_data(user.id),
            "rectification": lambda: self.update_user_data(user.id, details),
            "erasure": lambda: self.anonymize_user_data(user.id, "user_request"),
            "portability": lambda: self.export_user_data(user.id),
            "restriction": lambda: self.restrict_data_processing(user.id),
            "objection": lambda: self.handle_processing_objection(user.id)
        }

        if request_type not in request_handlers:
            raise ValueError(f"Unsupported request type: {request_type}")

        # Create request record
        dsr_record = DataSubjectRequest(
            user_id=user.id,
            request_type=request_type,
            request_details=details,
            status="processing",
            created_at=datetime.utcnow()
        )
        db.session.add(dsr_record)
        db.session.commit()

        try:
            # Process the request
            result = request_handlers[request_type]()

            # Update request status
            dsr_record.status = "completed"
            dsr_record.completed_at = datetime.utcnow()
            dsr_record.result_summary = f"Request processed successfully"

            db.session.commit()

            return {
                "request_id": str(dsr_record.id),
                "status": "completed",
                "result": result
            }

        except Exception as e:
            dsr_record.status = "failed"
            dsr_record.error_message = str(e)
            db.session.commit()
            raise

    def cleanup_expired_data(self):
        """Automated cleanup of expired data per retention policies"""
        cleanup_results = {}

        for data_type, retention_period in self.data_retention_periods.items():
            cutoff_date = datetime.utcnow() - retention_period

            try:
                if data_type == "session_data":
                    deleted_count = UserSession.query.filter(
                        UserSession.created_at < cutoff_date
                    ).delete()

                elif data_type == "device_data":
                    # Only delete inactive devices
                    deleted_count = TenantDevice.query.filter(
                        TenantDevice.last_seen < cutoff_date,
                        TenantDevice.active == False
                    ).delete()

                elif data_type == "test_data":
                    # Archive old test data instead of deleting
                    old_tests = TestExecution.query.filter(
                        TestExecution.created_at < cutoff_date
                    ).all()

                    for test in old_tests:
                        self._archive_test_execution(test)

                    deleted_count = len(old_tests)

                cleanup_results[data_type] = {
                    "deleted_count": deleted_count,
                    "cutoff_date": cutoff_date.isoformat()
                }

            except Exception as e:
                cleanup_results[data_type] = {
                    "error": str(e),
                    "cutoff_date": cutoff_date.isoformat()
                }

        db.session.commit()

        # Log cleanup results
        AuditLog.create(
            action="automated_data_cleanup",
            details={
                "cleanup_results": cleanup_results,
                "compliance_framework": "GDPR Article 5"
            }
        )

        return cleanup_results

# Global GDPR compliance manager
gdpr_manager = GDPRComplianceManager()
```

**SOC 2 Type II Compliance Framework:**
```python
# /opt/mobile-automation-saas/app/compliance/soc2.py

class SOC2ComplianceManager:
    def __init__(self):
        self.control_objectives = {
            "CC1": "Control Environment",
            "CC2": "Communication and Information",
            "CC3": "Risk Assessment",
            "CC4": "Monitoring Activities",
            "CC5": "Control Activities",
            "CC6": "Logical and Physical Access Controls",
            "CC7": "System Operations",
            "CC8": "Change Management",
            "CC9": "Risk Mitigation"
        }

        self.security_controls = {
            "access_controls": self.audit_access_controls,
            "data_encryption": self.audit_encryption,
            "backup_procedures": self.audit_backups,
            "incident_response": self.audit_incident_response,
            "change_management": self.audit_change_management,
            "monitoring": self.audit_monitoring,
            "vendor_management": self.audit_vendor_management
        }

    def generate_compliance_report(self):
        """Generate comprehensive SOC 2 compliance report"""
        report = {
            "report_metadata": {
                "generated_at": datetime.utcnow().isoformat(),
                "report_period": {
                    "start": (datetime.utcnow() - timedelta(days=365)).isoformat(),
                    "end": datetime.utcnow().isoformat()
                },
                "framework": "SOC 2 Type II",
                "organization": "Mobile Automation SaaS Platform"
            },
            "executive_summary": {
                "overall_compliance_status": "compliant",
                "total_controls_tested": len(self.security_controls),
                "controls_passed": 0,
                "controls_failed": 0,
                "exceptions": []
            },
            "control_assessments": {}
        }

        # Assess each control
        for control_name, audit_func in self.security_controls.items():
            try:
                assessment_result = audit_func()
                report["control_assessments"][control_name] = assessment_result

                if assessment_result["status"] == "compliant":
                    report["executive_summary"]["controls_passed"] += 1
                else:
                    report["executive_summary"]["controls_failed"] += 1
                    report["executive_summary"]["exceptions"].append({
                        "control": control_name,
                        "issue": assessment_result.get("issues", [])
                    })

            except Exception as e:
                report["control_assessments"][control_name] = {
                    "status": "error",
                    "error": str(e),
                    "tested_at": datetime.utcnow().isoformat()
                }
                report["executive_summary"]["controls_failed"] += 1

        # Update overall status
        if report["executive_summary"]["controls_failed"] == 0:
            report["executive_summary"]["overall_compliance_status"] = "compliant"
        else:
            report["executive_summary"]["overall_compliance_status"] = "non_compliant"

        return report

    def audit_access_controls(self):
        """Audit access control implementation"""
        issues = []

        # Check MFA enforcement
        users_without_mfa = User.query.join(UserMFA, isouter=True).filter(
            UserMFA.enabled.is_(None) | (UserMFA.enabled == False)
        ).count()

        if users_without_mfa > 0:
            issues.append(f"{users_without_mfa} users without MFA enabled")

        # Check password policies
        weak_passwords = User.query.filter(
            User.password_strength_score < 3
        ).count()

        if weak_passwords > 0:
            issues.append(f"{weak_passwords} users with weak passwords")

        # Check privileged access
        admin_users = TenantUserRole.query.filter(
            TenantUserRole.role.in_(['tenant_owner', 'tenant_admin'])
        ).count()

        # Check session management
        old_sessions = UserSession.query.filter(
            UserSession.last_activity < datetime.utcnow() - timedelta(hours=24),
            UserSession.active == True
        ).count()

        if old_sessions > 0:
            issues.append(f"{old_sessions} stale active sessions")

        return {
            "control": "Access Controls",
            "status": "compliant" if not issues else "non_compliant",
            "issues": issues,
            "metrics": {
                "total_users": User.query.count(),
                "mfa_enabled_users": User.query.join(UserMFA).filter(UserMFA.enabled == True).count(),
                "admin_users": admin_users,
                "active_sessions": UserSession.query.filter(UserSession.active == True).count()
            },
            "tested_at": datetime.utcnow().isoformat()
        }

    def audit_encryption(self):
        """Audit data encryption implementation"""
        issues = []

        # Check database encryption
        try:
            result = db.session.execute(text("SHOW ssl;"))
            ssl_status = result.fetchone()[0]
            if ssl_status != "on":
                issues.append("Database SSL not enabled")
        except Exception as e:
            issues.append(f"Unable to verify database SSL: {e}")

        # Check application encryption
        encryption_tests = [
            ("JWT secrets", os.path.exists("/etc/mobile-automation-saas/jwt_private.pem")),
            ("MFA secrets", os.path.exists("/etc/mobile-automation-saas/mfa_key")),
            ("GDPR encryption", os.path.exists("/etc/mobile-automation-saas/gdpr_encryption_key"))
        ]

        for test_name, test_result in encryption_tests:
            if not test_result:
                issues.append(f"{test_name} encryption key not found")

        # Check TLS configuration
        try:
            import ssl
            context = ssl.create_default_context()
            with socket.create_connection(('localhost', 443), timeout=5) as sock:
                with context.wrap_socket(sock, server_hostname='localhost') as ssock:
                    if ssock.version() not in ['TLSv1.2', 'TLSv1.3']:
                        issues.append(f"Weak TLS version: {ssock.version()}")
        except Exception as e:
            issues.append(f"Unable to verify TLS configuration: {e}")

        return {
            "control": "Data Encryption",
            "status": "compliant" if not issues else "non_compliant",
            "issues": issues,
            "tested_at": datetime.utcnow().isoformat()
        }

    def audit_backups(self):
        """Audit backup procedures"""
        issues = []

        # Check recent backups
        backup_dir = "/opt/backups/postgresql"
        if os.path.exists(backup_dir):
            recent_backups = []
            for backup_type in ['daily', 'weekly', 'monthly']:
                backup_path = os.path.join(backup_dir, backup_type)
                if os.path.exists(backup_path):
                    files = [f for f in os.listdir(backup_path) if f.endswith('.gpg')]
                    if files:
                        latest_backup = max(files, key=lambda x: os.path.getctime(os.path.join(backup_path, x)))
                        backup_age = time.time() - os.path.getctime(os.path.join(backup_path, latest_backup))
                        recent_backups.append({
                            "type": backup_type,
                            "file": latest_backup,
                            "age_hours": backup_age / 3600
                        })

            # Check if daily backup is recent
            daily_backup = next((b for b in recent_backups if b["type"] == "daily"), None)
            if not daily_backup or daily_backup["age_hours"] > 25:
                issues.append("Daily backup is missing or too old")
        else:
            issues.append("Backup directory not found")

        # Check backup encryption
        if os.path.exists(backup_dir):
            unencrypted_backups = []
            for root, dirs, files in os.walk(backup_dir):
                for file in files:
                    if file.endswith('.sql') and not file.endswith('.gpg'):
                        unencrypted_backups.append(file)

            if unencrypted_backups:
                issues.append(f"{len(unencrypted_backups)} unencrypted backup files found")

        return {
            "control": "Backup Procedures",
            "status": "compliant" if not issues else "non_compliant",
            "issues": issues,
            "metrics": {
                "recent_backups": recent_backups if 'recent_backups' in locals() else []
            },
            "tested_at": datetime.utcnow().isoformat()
        }

# Global SOC 2 compliance manager
soc2_manager = SOC2ComplianceManager()
```

---

## Conclusion

This technical specification document provides comprehensive guidance for deploying and operating the Mobile Automation SaaS Platform at enterprise scale. The hybrid cloud architecture achieves significant cost savings while maintaining security, compliance, and performance standards required for a professional SaaS offering.

### Key Takeaways:

1. **Scalability**: The platform can support 10,000+ concurrent subscribers with proper hardware provisioning and database optimization.

2. **Cross-Platform Compatibility**: While iOS testing from Windows has limitations, multiple workaround solutions enable comprehensive device coverage.

3. **Security**: Multi-layered security approach with tenant isolation, encryption, monitoring, and compliance frameworks ensures enterprise-grade protection.

4. **Cost Efficiency**: Hybrid architecture reduces infrastructure costs by 97% compared to traditional cloud device farms while maintaining professional capabilities.

5. **Compliance**: Built-in GDPR and SOC 2 compliance features ensure regulatory requirements are met from day one.

The implementation provides a solid foundation for scaling the mobile automation platform to serve thousands of subscribers while maintaining security, performance, and compliance standards.
```
```
```