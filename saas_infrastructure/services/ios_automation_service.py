#!/usr/bin/env python3
"""
Standalone iOS Automation Service for Multi-Tenant SaaS Platform
Provides tenant-aware iOS automation interface that can be accessed directly
"""

import os
import sys
import logging
import json
import uuid
from datetime import datetime, timedelta
from pathlib import Path

from flask import Flask, request, jsonify, session, g, render_template, redirect, url_for, send_from_directory
from flask_jwt_extended import J<PERSON><PERSON>anager, verify_jwt_in_request, get_jwt_identity, get_jwt, decode_token
from flask_cors import CORS
import psycopg2.extras
from sqlalchemy import create_engine, text
from werkzeug.middleware.proxy_fix import ProxyFix

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class iOSAutomationService:
    def __init__(self, config=None):
        # Set template directory to the services templates folder
        template_dir = Path(__file__).parent / 'templates'
        self.app = Flask(__name__, template_folder=str(template_dir))
        self.setup_config(config)
        self.setup_extensions()
        self.setup_database()
        self.setup_routes()
        self.setup_original_app_integration()
        self.setup_device_bridge_integration()
        
    def setup_config(self, config):
        """Setup Flask application configuration"""
        # Default configuration
        self.app.config.update({
            'SECRET_KEY': os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production'),
            'JWT_SECRET_KEY': os.environ.get('JWT_SECRET_KEY', 'jwt-secret-key-change-in-production'),
            'JWT_ACCESS_TOKEN_EXPIRES': timedelta(hours=24),
            'DATABASE_URI': self.get_database_uri(),
        })
        
        # Override with custom config if provided
        if config:
            self.app.config.update(config)
    
    def get_database_uri(self):
        """Build database URI from environment variables"""
        database_url = os.environ.get('DATABASE_URL')
        if database_url:
            return database_url

        # Fallback to individual environment variables
        host = os.environ.get('DATABASE_HOST', 'localhost')
        port = os.environ.get('DATABASE_PORT', '5432')
        name = os.environ.get('DATABASE_NAME', 'mobile_automation_saas')
        user = os.environ.get('DATABASE_USER', 'mobile_automation_app')
        password = os.environ.get('DATABASE_PASSWORD', '')

        return f'postgresql://{user}:{password}@{host}:{port}/{name}'
    
    def setup_extensions(self):
        """Setup Flask extensions"""
        # JWT for authentication
        self.jwt = JWTManager(self.app)
        
        # CORS for API access
        CORS(self.app)
        
        # Proxy fix for deployment behind reverse proxy
        self.app.wsgi_app = ProxyFix(self.app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_prefix=1)
    
    def setup_database(self):
        """Setup database connection"""
        try:
            self.db_engine = create_engine(self.app.config['DATABASE_URI'])
            logger.info("Database connection established")
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            raise
    
    def setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.before_request
        def before_request():
            """Set up tenant context for each request"""
            # Extract tenant subdomain from URL path
            if request.path.startswith('/ios/'):
                path_parts = request.path.split('/')
                if len(path_parts) >= 3:
                    tenant_subdomain = path_parts[2]
                    tenant = self.get_tenant_by_subdomain(tenant_subdomain)
                    if tenant:
                        g.tenant_id = tenant['id']
                        g.tenant_data = tenant
                        g.tenant_subdomain = tenant_subdomain
                    else:
                        g.tenant_id = None
                        g.tenant_data = None
                        g.tenant_subdomain = None

        @self.app.route('/health')
        def health():
            """Health check endpoint"""
            return jsonify({'status': 'healthy', 'service': 'ios_automation'})

        @self.app.route('/ios/<tenant_subdomain>/')
        def ios_automation_index(tenant_subdomain):
            """Main iOS automation interface for tenant"""
            if not g.tenant_data:
                return jsonify({'error': f'Tenant "{tenant_subdomain}" not found'}), 404
            
            # Check for session token in query parameters
            session_token = request.args.get('token')
            if session_token:
                # Validate session token and establish session
                if self.validate_and_establish_session(session_token, 'ios'):
                    # Redirect to clean URL without token
                    return redirect(f'/ios/{tenant_subdomain}/')
                else:
                    return render_template('auth_error.html',
                                         error='Invalid or expired session token. Please try again.',
                                         tenant=g.tenant_data)

            # Check if user is already authenticated
            if not self.is_authenticated():
                return render_template('auth_error.html',
                                     error='Authentication required. Please access this page through the main dashboard.',
                                     tenant=g.tenant_data)
            
            # Render the original iOS automation interface
            return self.render_ios_interface()
        
        @self.app.route('/ios/<tenant_subdomain>/api/<path:api_path>', methods=['GET', 'POST', 'PUT', 'DELETE'])
        def ios_api_proxy(tenant_subdomain, api_path):
            """Proxy API calls to original iOS automation with tenant context"""
            if not g.tenant_data:
                return jsonify({'error': f'Tenant "{tenant_subdomain}" not found'}), 404
            
            if not self.is_authenticated():
                return jsonify({'error': 'Authentication required'}), 401
            
            # Forward to original iOS automation API with tenant filtering
            return self.proxy_to_original_ios_api(api_path)
        
        @self.app.route('/ios/<tenant_subdomain>/static/<path:filename>')
        def ios_static_files(tenant_subdomain, filename):
            """Serve static files for iOS automation"""
            if not g.tenant_data:
                return "Not Found", 404
            
            # Serve from original iOS app static directory
            static_path = Path(__file__).parent.parent.parent / "app" / "static"
            return send_from_directory(str(static_path), filename)
        
        @self.app.route('/health')
        def health_check():
            """Health check endpoint"""
            return jsonify({
                'status': 'healthy',
                'service': 'ios_automation',
                'timestamp': datetime.utcnow().isoformat()
            })
    
    def setup_original_app_integration(self):
        """Setup integration with original iOS automation app"""
        # Add original app directory to Python path
        original_app_path = Path(__file__).parent.parent.parent / "app"
        if str(original_app_path) not in sys.path:
            sys.path.insert(0, str(original_app_path))

        # Import original app components
        try:
            import app as original_ios_app
            from utils.appium_device_controller import AppiumDeviceController

            self.original_app = original_ios_app
            self.device_controller = AppiumDeviceController()

            logger.info("Original iOS app integration setup completed")
        except Exception as e:
            logger.error(f"Failed to setup original app integration: {e}")
            raise

    def setup_device_bridge_integration(self):
        """Setup integration with device bridge system"""
        try:
            # Import bridge client
            services_path = Path(__file__).parent
            if str(services_path) not in sys.path:
                sys.path.insert(0, str(services_path))

            from bridge_client import create_bridge_client

            # Create bridge client for communicating with SaaS app
            saas_base_url = os.environ.get('SAAS_BASE_URL', 'http://localhost:5000')
            self.bridge_client = create_bridge_client(saas_base_url, 'ios')

            logger.info("Device bridge integration setup completed")
        except Exception as e:
            logger.error(f"Failed to setup device bridge integration: {e}")
            raise
    
    def get_tenant_by_subdomain(self, subdomain):
        """Get tenant by subdomain"""
        try:
            with self.db_engine.connect() as conn:
                result = conn.execute(
                    text("SELECT id, name, subdomain, subscription_tier, is_active FROM tenants WHERE subdomain = :subdomain AND is_active = true"),
                    {"subdomain": subdomain}
                )
                row = result.fetchone()
                if row:
                    return {
                        'id': row[0],
                        'name': row[1],
                        'subdomain': row[2],
                        'subscription_tier': row[3],
                        'is_active': row[4]
                    }
                return None
        except Exception as e:
            logger.error(f"Error getting tenant by subdomain: {e}")
            return None
    
    def validate_and_establish_session(self, session_token, platform):
        """Validate session token and establish user session"""
        try:
            # Decode JWT token directly (since it's passed as query param, not header)
            try:
                decoded_token = decode_token(session_token)
                user_id = decoded_token['sub']
                claims = decoded_token
            except Exception as jwt_error:
                logger.error(f"JWT decode error: {jwt_error}")
                return False

            if not user_id or not claims:
                return False

            # Verify token in database
            with self.db_engine.connect() as conn:
                result = conn.execute(
                    text("""
                        SELECT user_id, tenant_id, expires_at
                        FROM automation_sessions
                        WHERE token = :token AND platform = :platform AND expires_at > NOW()
                    """),
                    {"token": session_token, "platform": platform}
                )
                session_row = result.fetchone()

                if not session_row:
                    logger.warning(f"Session token not found in database: {session_token[:20]}...")
                    return False

                # Verify tenant matches
                if session_row[1] != g.tenant_id:
                    logger.warning(f"Tenant mismatch: session={session_row[1]}, current={g.tenant_id}")
                    return False

                # Set up bridge client authentication
                self.bridge_client.set_authentication(session_token, session_row[1])

                # Establish Flask session
                session['authenticated'] = True
                session['user_id'] = session_row[0]
                session['tenant_id'] = session_row[1]
                session['platform'] = platform
                session['session_token'] = session_token

                logger.info(f"Session established for user {session_row[0]} on tenant {session_row[1]}")
                return True

        except Exception as e:
            logger.error(f"Error validating session token: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    def is_authenticated(self):
        """Check if user is authenticated"""
        return session.get('authenticated') and session.get('tenant_id') == g.tenant_id
    
    def render_ios_interface(self):
        """Render the original iOS automation interface"""
        try:
            # Load original iOS template
            template_path = Path(__file__).parent.parent.parent / "app" / "templates" / "index.html"
            
            if template_path.exists():
                with open(template_path, 'r', encoding='utf-8') as f:
                    template_content = f.read()
                
                # Update template paths for tenant-specific URLs
                import re
                tenant_subdomain = g.tenant_subdomain
                
                # Replace static file URLs
                template_content = re.sub(
                    r"\{\{\s*url_for\('static',\s*filename='([^']+)'\)\s*\}\}",
                    rf"/ios/{tenant_subdomain}/static/\1",
                    template_content
                )
                
                # Replace API URLs
                template_content = re.sub(
                    r'"/api/',
                    rf'"/ios/{tenant_subdomain}/api/',
                    template_content
                )
                
                return template_content
            else:
                return "<h1>iOS Automation</h1><p>Template not found</p>"
                
        except Exception as e:
            logger.error(f"Error rendering iOS interface: {e}")
            return f"<h1>iOS Automation</h1><p>Error: {str(e)}</p>"
    
    def get_tenant_devices(self):
        """Get iOS devices for the current tenant"""
        try:
            # Use bridge client to get devices from SaaS app
            devices = self.bridge_client.get_tenant_devices(g.tenant_id)
            return devices

        except Exception as e:
            logger.error(f"Error getting tenant devices: {e}")
            return []

    def send_device_command(self, device_udid, command, parameters=None):
        """Send command to a specific iOS device through the bridge"""
        try:
            # Use bridge client to send command through SaaS app
            result = self.bridge_client.send_device_command(device_udid, command, parameters)
            return result

        except Exception as e:
            logger.error(f"Error sending device command: {e}")
            return {'status': 'error', 'message': str(e)}

    def proxy_to_original_ios_api(self, api_path):
        """Proxy API calls to original iOS automation with tenant filtering"""
        try:
            # Handle device-related API calls
            if api_path.startswith('devices'):
                if api_path == 'devices':
                    # Return tenant-specific devices
                    devices = self.get_tenant_devices()
                    return jsonify({'devices': devices})
                elif api_path.startswith('devices/') and '/command' in api_path:
                    # Handle device commands
                    device_udid = api_path.split('/')[1]
                    command_data = request.get_json()
                    result = self.send_device_command(
                        device_udid,
                        command_data.get('command'),
                        command_data.get('parameters')
                    )
                    return jsonify(result)

            # For other API calls, integrate with original app
            # This would need more detailed implementation
            return jsonify({
                'message': f'iOS API proxy for {api_path}',
                'tenant': g.tenant_data['name'],
                'platform': 'ios'
            })

        except Exception as e:
            logger.error(f"Error proxying iOS API call: {e}")
            return jsonify({'error': 'API call failed'}), 500

def create_ios_automation_service(config=None):
    """Factory function to create iOS automation service"""
    return iOSAutomationService(config)

if __name__ == '__main__':
    # Create and run the service
    service = create_ios_automation_service()
    port = int(os.environ.get('IOS_SERVICE_PORT', 8080))
    service.app.run(debug=True, host='0.0.0.0', port=port)
