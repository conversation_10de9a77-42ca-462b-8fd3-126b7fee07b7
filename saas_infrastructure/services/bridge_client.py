#!/usr/bin/env python3
"""
Bridge Client for Standalone Automation Services
Provides communication interface between standalone services and device bridge system
"""

import json
import logging
import asyncio
import websockets
import requests
from datetime import datetime
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class BridgeClient:
    """Client for communicating with the device bridge system"""
    
    def __init__(self, saas_base_url: str, platform: str):
        self.saas_base_url = saas_base_url.rstrip('/')
        self.platform = platform  # 'ios' or 'android'
        self.session = requests.Session()
        
    def set_authentication(self, session_token: str, tenant_id: str):
        """Set authentication for bridge communication"""
        self.session.headers.update({
            'Authorization': f'Bearer {session_token}',
            'X-Tenant-ID': tenant_id,
            'X-Platform': self.platform
        })
    
    def get_tenant_devices(self, tenant_id: str) -> List[Dict[str, Any]]:
        """Get devices for a specific tenant and platform"""
        try:
            response = self.session.get(
                f"{self.saas_base_url}/api/devices",
                params={'platform': self.platform}
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('devices', [])
            else:
                logger.error(f"Failed to get devices: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"Error getting tenant devices: {e}")
            return []
    
    def send_device_command(self, device_udid: str, command: str, parameters: Optional[Dict] = None) -> Dict[str, Any]:
        """Send command to a specific device through the bridge"""
        try:
            payload = {
                'device_udid': device_udid,
                'command': command,
                'parameters': parameters or {},
                'platform': self.platform,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            response = self.session.post(
                f"{self.saas_base_url}/api/devices/{device_udid}/command",
                json=payload
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to send command: {response.status_code} - {response.text}")
                return {
                    'status': 'error',
                    'message': f'Command failed: {response.status_code}'
                }
                
        except Exception as e:
            logger.error(f"Error sending device command: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
    
    def get_device_status(self, device_udid: str) -> Dict[str, Any]:
        """Get status of a specific device"""
        try:
            response = self.session.get(
                f"{self.saas_base_url}/api/devices/{device_udid}/status"
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to get device status: {response.status_code}")
                return {'status': 'unknown'}
                
        except Exception as e:
            logger.error(f"Error getting device status: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def start_test_session(self, device_udid: str, test_config: Dict[str, Any]) -> Dict[str, Any]:
        """Start a test session on a specific device"""
        try:
            payload = {
                'device_udid': device_udid,
                'test_config': test_config,
                'platform': self.platform,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            response = self.session.post(
                f"{self.saas_base_url}/api/devices/{device_udid}/test/start",
                json=payload
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to start test session: {response.status_code}")
                return {
                    'status': 'error',
                    'message': f'Test session start failed: {response.status_code}'
                }
                
        except Exception as e:
            logger.error(f"Error starting test session: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
    
    def stop_test_session(self, device_udid: str, session_id: str) -> Dict[str, Any]:
        """Stop a test session on a specific device"""
        try:
            payload = {
                'device_udid': device_udid,
                'session_id': session_id,
                'platform': self.platform,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            response = self.session.post(
                f"{self.saas_base_url}/api/devices/{device_udid}/test/stop",
                json=payload
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to stop test session: {response.status_code}")
                return {
                    'status': 'error',
                    'message': f'Test session stop failed: {response.status_code}'
                }
                
        except Exception as e:
            logger.error(f"Error stopping test session: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
    
    def get_test_logs(self, device_udid: str, session_id: str) -> Dict[str, Any]:
        """Get test logs for a specific session"""
        try:
            response = self.session.get(
                f"{self.saas_base_url}/api/devices/{device_udid}/test/{session_id}/logs"
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to get test logs: {response.status_code}")
                return {'logs': [], 'error': 'Failed to retrieve logs'}
                
        except Exception as e:
            logger.error(f"Error getting test logs: {e}")
            return {'logs': [], 'error': str(e)}

class AsyncBridgeClient:
    """Async client for real-time communication with device bridge"""
    
    def __init__(self, saas_ws_url: str, platform: str):
        self.saas_ws_url = saas_ws_url
        self.platform = platform
        self.websocket = None
        self.event_handlers = {}
        
    async def connect(self, session_token: str, tenant_id: str):
        """Connect to the SaaS WebSocket for real-time updates"""
        try:
            headers = {
                'Authorization': f'Bearer {session_token}',
                'X-Tenant-ID': tenant_id,
                'X-Platform': self.platform
            }
            
            self.websocket = await websockets.connect(
                f"{self.saas_ws_url}/socket.io/",
                extra_headers=headers
            )
            
            logger.info(f"Connected to SaaS WebSocket for {self.platform}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to SaaS WebSocket: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from WebSocket"""
        if self.websocket:
            await self.websocket.close()
            self.websocket = None
    
    def on_event(self, event_type: str, handler):
        """Register event handler"""
        self.event_handlers[event_type] = handler
    
    async def listen_for_events(self):
        """Listen for events from the bridge system"""
        if not self.websocket:
            logger.error("WebSocket not connected")
            return
        
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    event_type = data.get('type')
                    
                    if event_type in self.event_handlers:
                        await self.event_handlers[event_type](data)
                    else:
                        logger.debug(f"Unhandled event type: {event_type}")
                        
                except json.JSONDecodeError as e:
                    logger.error(f"Invalid JSON received: {e}")
                except Exception as e:
                    logger.error(f"Error processing event: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.warning("WebSocket connection closed")
        except Exception as e:
            logger.error(f"Error in event listener: {e}")
    
    async def send_event(self, event_type: str, data: Dict[str, Any]):
        """Send event to the bridge system"""
        if not self.websocket:
            logger.error("WebSocket not connected")
            return False
        
        try:
            message = {
                'type': event_type,
                'platform': self.platform,
                'timestamp': datetime.utcnow().isoformat(),
                **data
            }
            
            await self.websocket.send(json.dumps(message))
            return True
            
        except Exception as e:
            logger.error(f"Error sending event: {e}")
            return False

def create_bridge_client(saas_base_url: str, platform: str) -> BridgeClient:
    """Factory function to create bridge client"""
    return BridgeClient(saas_base_url, platform)

def create_async_bridge_client(saas_ws_url: str, platform: str) -> AsyncBridgeClient:
    """Factory function to create async bridge client"""
    return AsyncBridgeClient(saas_ws_url, platform)
