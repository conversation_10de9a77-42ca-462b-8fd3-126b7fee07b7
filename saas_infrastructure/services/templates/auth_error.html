<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Error - Mobile Automation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-body text-center p-5">
                        <div class="mb-4">
                            <i class="fas fa-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
                        </div>
                        <h2 class="h4 mb-3">Authentication Required</h2>
                        <p class="text-muted mb-4">{{ error }}</p>
                        
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="retryAuthentication()">
                                <i class="fas fa-redo me-2"></i>
                                Retry Authentication
                            </button>
                            <button class="btn btn-outline-secondary" onclick="returnToDashboard()">
                                <i class="fas fa-arrow-left me-2"></i>
                                Return to Dashboard
                            </button>
                        </div>
                        
                        <div class="mt-4">
                            <small class="text-muted">
                                {% if tenant %}
                                Tenant: {{ tenant.name }} ({{ tenant.subdomain }})
                                {% endif %}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function retryAuthentication() {
            // Try to get a new session token from the parent window
            if (window.opener && window.opener.location.hostname === window.location.hostname) {
                try {
                    // Determine platform from URL
                    const platform = window.location.pathname.includes('/ios/') ? 'ios' : 'android';

                    // Ask parent window to generate new access token
                    window.opener.postMessage({
                        type: `request_${platform}_access`,
                        tenant: '{{ tenant.subdomain if tenant else "" }}'
                    }, '*');

                    // Listen for response
                    window.addEventListener('message', function(event) {
                        if (event.data.type === `${platform}_access_response` && event.data.access_url) {
                            window.location.href = event.data.access_url;
                        }
                    });
                } catch (e) {
                    console.error('Error communicating with parent window:', e);
                    window.location.reload();
                }
            } else {
                // Fallback: just reload the page
                window.location.reload();
            }
        }
        
        function returnToDashboard() {
            if (window.opener && window.opener.location.hostname === window.location.hostname) {
                window.opener.focus();
                window.close();
            } else {
                // Redirect to main dashboard
                {% if tenant %}
                window.location.href = 'http://{{ tenant.subdomain }}.{{ request.host.split(".")[1:] | join(".") if "." in request.host else request.host }}:5000/';
                {% else %}
                window.location.href = '/';
                {% endif %}
            }
        }
        
        // Auto-retry after 3 seconds
        setTimeout(function() {
            retryAuthentication();
        }, 3000);
    </script>
</body>
</html>
