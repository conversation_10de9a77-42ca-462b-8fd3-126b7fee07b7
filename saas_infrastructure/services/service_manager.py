#!/usr/bin/env python3
"""
Service Manager for Mobile Automation SaaS Platform
Manages startup and coordination of iOS and Android automation services
"""

import os
import sys
import time
import signal
import logging
import subprocess
import threading
from pathlib import Path
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ServiceManager:
    def __init__(self):
        self.services = {}
        self.running = False
        self.base_path = Path(__file__).parent
        
    def start_service(self, service_name, script_path, port, env_vars=None):
        """Start a service process"""
        try:
            # Prepare environment
            env = os.environ.copy()
            if env_vars:
                env.update(env_vars)
            
            # Start the service process
            logger.info(f"Starting {service_name} on port {port}")
            process = subprocess.Popen(
                [sys.executable, str(script_path)],
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.services[service_name] = {
                'process': process,
                'port': port,
                'script_path': script_path,
                'started_at': datetime.now(),
                'env_vars': env_vars or {}
            }
            
            # Start output monitoring thread
            threading.Thread(
                target=self._monitor_service_output,
                args=(service_name, process),
                daemon=True
            ).start()
            
            logger.info(f"{service_name} started successfully (PID: {process.pid})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start {service_name}: {e}")
            return False
    
    def stop_service(self, service_name):
        """Stop a service process"""
        if service_name not in self.services:
            logger.warning(f"Service {service_name} not found")
            return False
        
        try:
            service = self.services[service_name]
            process = service['process']
            
            logger.info(f"Stopping {service_name} (PID: {process.pid})")
            
            # Try graceful shutdown first
            process.terminate()
            
            # Wait for graceful shutdown
            try:
                process.wait(timeout=10)
                logger.info(f"{service_name} stopped gracefully")
            except subprocess.TimeoutExpired:
                # Force kill if graceful shutdown fails
                logger.warning(f"Force killing {service_name}")
                process.kill()
                process.wait()
            
            del self.services[service_name]
            return True
            
        except Exception as e:
            logger.error(f"Error stopping {service_name}: {e}")
            return False
    
    def restart_service(self, service_name):
        """Restart a service"""
        if service_name in self.services:
            service = self.services[service_name]
            port = service['port']
            script_path = service['script_path']
            env_vars = service['env_vars']
            
            self.stop_service(service_name)
            time.sleep(2)  # Brief pause
            return self.start_service(service_name, script_path, port, env_vars)
        else:
            logger.error(f"Cannot restart {service_name}: service not found")
            return False
    
    def get_service_status(self, service_name):
        """Get status of a service"""
        if service_name not in self.services:
            return {'status': 'not_running'}
        
        service = self.services[service_name]
        process = service['process']
        
        # Check if process is still running
        if process.poll() is None:
            return {
                'status': 'running',
                'pid': process.pid,
                'port': service['port'],
                'started_at': service['started_at'].isoformat(),
                'uptime': str(datetime.now() - service['started_at'])
            }
        else:
            return {
                'status': 'stopped',
                'exit_code': process.returncode
            }
    
    def get_all_status(self):
        """Get status of all services"""
        return {name: self.get_service_status(name) for name in self.services}
    
    def start_all_services(self):
        """Start all automation services"""
        logger.info("Starting all automation services...")
        
        # Get configuration from environment
        ios_port = int(os.environ.get('IOS_SERVICE_PORT', '8080'))
        android_port = int(os.environ.get('ANDROID_SERVICE_PORT', '8081'))
        
        # Start iOS automation service
        ios_script = self.base_path / 'ios_automation_service.py'
        ios_env = {
            'IOS_SERVICE_PORT': str(ios_port),
            'FLASK_ENV': os.environ.get('FLASK_ENV', 'production')
        }
        
        if not self.start_service('ios_automation', ios_script, ios_port, ios_env):
            logger.error("Failed to start iOS automation service")
            return False
        
        # Start Android automation service
        android_script = self.base_path / 'android_automation_service.py'
        android_env = {
            'ANDROID_SERVICE_PORT': str(android_port),
            'FLASK_ENV': os.environ.get('FLASK_ENV', 'production')
        }
        
        if not self.start_service('android_automation', android_script, android_port, android_env):
            logger.error("Failed to start Android automation service")
            self.stop_service('ios_automation')  # Clean up
            return False
        
        self.running = True
        logger.info("All automation services started successfully")
        return True
    
    def stop_all_services(self):
        """Stop all services"""
        logger.info("Stopping all services...")
        
        for service_name in list(self.services.keys()):
            self.stop_service(service_name)
        
        self.running = False
        logger.info("All services stopped")
    
    def _monitor_service_output(self, service_name, process):
        """Monitor service output in a separate thread"""
        try:
            while process.poll() is None:
                output = process.stdout.readline()
                if output:
                    logger.info(f"[{service_name}] {output.strip()}")
                
                error = process.stderr.readline()
                if error:
                    logger.error(f"[{service_name}] {error.strip()}")
                    
        except Exception as e:
            logger.error(f"Error monitoring {service_name} output: {e}")
    
    def health_check(self):
        """Perform health check on all services"""
        import requests
        
        results = {}
        
        for service_name, service in self.services.items():
            try:
                port = service['port']
                response = requests.get(f'http://localhost:{port}/health', timeout=5)
                
                if response.status_code == 200:
                    results[service_name] = {
                        'status': 'healthy',
                        'response_time': response.elapsed.total_seconds()
                    }
                else:
                    results[service_name] = {
                        'status': 'unhealthy',
                        'status_code': response.status_code
                    }
                    
            except Exception as e:
                results[service_name] = {
                    'status': 'unreachable',
                    'error': str(e)
                }
        
        return results
    
    def run_forever(self):
        """Run service manager indefinitely"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, shutting down...")
            self.stop_all_services()
            sys.exit(0)
        
        # Register signal handlers
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Start all services
        if not self.start_all_services():
            logger.error("Failed to start services")
            sys.exit(1)
        
        # Monitor services
        logger.info("Service manager running. Press Ctrl+C to stop.")
        
        try:
            while self.running:
                time.sleep(10)
                
                # Check if any services have died
                for service_name in list(self.services.keys()):
                    status = self.get_service_status(service_name)
                    if status['status'] == 'stopped':
                        logger.warning(f"Service {service_name} has stopped unexpectedly")
                        # Optionally restart the service
                        # self.restart_service(service_name)
                        
        except KeyboardInterrupt:
            logger.info("Keyboard interrupt received")
        finally:
            self.stop_all_services()

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Mobile Automation Service Manager')
    parser.add_argument('command', choices=['start', 'stop', 'restart', 'status', 'health'], 
                       help='Command to execute')
    parser.add_argument('--service', help='Specific service name (ios_automation, android_automation)')
    parser.add_argument('--daemon', action='store_true', help='Run as daemon')
    
    args = parser.parse_args()
    
    manager = ServiceManager()
    
    if args.command == 'start':
        if args.service:
            # Start specific service (would need additional logic)
            logger.error("Starting specific services not implemented yet")
        else:
            if args.daemon:
                manager.run_forever()
            else:
                manager.start_all_services()
                
    elif args.command == 'stop':
        manager.stop_all_services()
        
    elif args.command == 'restart':
        if args.service:
            manager.restart_service(args.service)
        else:
            manager.stop_all_services()
            time.sleep(2)
            manager.start_all_services()
            
    elif args.command == 'status':
        status = manager.get_all_status()
        for service_name, service_status in status.items():
            print(f"{service_name}: {service_status}")
            
    elif args.command == 'health':
        health = manager.health_check()
        for service_name, health_status in health.items():
            print(f"{service_name}: {health_status}")

if __name__ == '__main__':
    main()
