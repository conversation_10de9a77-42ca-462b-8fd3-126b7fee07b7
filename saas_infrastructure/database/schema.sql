-- Multi-Tenant SaaS Database Schema for Mobile Automation Platform
-- PostgreSQL 14+ with Row Level Security (RLS)

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- ============================================================================
-- TENANT MANAGEMENT SCHEMA
-- ============================================================================

-- Core tenant management table
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    subdomain VARCHAR(100) UNIQUE NOT NULL,
    subscription_tier VARCHAR(50) DEFAULT 'starter' CHECK (subscription_tier IN ('starter', 'professional', 'team', 'enterprise')),
    max_devices INTEGER DEFAULT 2,
    max_test_minutes INTEGER DEFAULT 500,
    max_concurrent_tests INTEGER DEFAULT 1,
    storage_limit_gb INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    billing_email VARCHAR(255),
    subscription_status VARCHAR(50) DEFAULT 'trial' CHECK (subscription_status IN ('trial', 'active', 'suspended', 'cancelled')),
    trial_ends_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days'),
    last_payment_at TIMESTAMP WITH TIME ZONE,
    next_billing_at TIMESTAMP WITH TIME ZONE
);

-- User management with tenant association
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('admin', 'user', 'viewer')),
    permissions JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, email)
);

-- Device bridge management
CREATE TABLE device_bridges (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    bridge_token VARCHAR(255) UNIQUE NOT NULL,
    bridge_name VARCHAR(255) NOT NULL,
    tunnel_endpoint VARCHAR(255),
    public_key TEXT,
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    connection_status VARCHAR(50) DEFAULT 'disconnected' CHECK (connection_status IN ('connected', 'disconnected', 'error')),
    device_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Connected devices registry
CREATE TABLE tenant_devices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    bridge_id UUID NOT NULL REFERENCES device_bridges(id) ON DELETE CASCADE,
    device_udid VARCHAR(255) NOT NULL,
    device_name VARCHAR(255) NOT NULL,
    device_model VARCHAR(255),
    platform VARCHAR(20) NOT NULL CHECK (platform IN ('ios', 'android')),
    os_version VARCHAR(50),
    screen_resolution VARCHAR(50),
    is_available BOOLEAN DEFAULT true,
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    capabilities JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, device_udid)
);

-- Test execution tracking
CREATE TABLE test_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    device_id UUID REFERENCES tenant_devices(id) ON DELETE SET NULL,
    bridge_id UUID REFERENCES device_bridges(id) ON DELETE SET NULL,
    test_suite_name VARCHAR(255) NOT NULL,
    test_case_name VARCHAR(255),
    execution_time_seconds INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    results JSONB DEFAULT '{}',
    error_message TEXT,
    screenshots_count INTEGER DEFAULT 0,
    report_url VARCHAR(500),
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
) PARTITION BY HASH (tenant_id);

-- Create partitions for test_executions (4 partitions for better performance)
CREATE TABLE test_executions_p0 PARTITION OF test_executions FOR VALUES WITH (MODULUS 4, REMAINDER 0);
CREATE TABLE test_executions_p1 PARTITION OF test_executions FOR VALUES WITH (MODULUS 4, REMAINDER 1);
CREATE TABLE test_executions_p2 PARTITION OF test_executions FOR VALUES WITH (MODULUS 4, REMAINDER 2);
CREATE TABLE test_executions_p3 PARTITION OF test_executions FOR VALUES WITH (MODULUS 4, REMAINDER 3);

-- Usage tracking for billing
CREATE TABLE usage_tracking (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    metric_type VARCHAR(50) NOT NULL CHECK (metric_type IN ('test_minutes', 'storage_gb', 'api_calls', 'device_hours')),
    metric_value DECIMAL(10,2) NOT NULL,
    billing_period DATE NOT NULL, -- YYYY-MM-01 format
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, metric_type, billing_period)
);

-- API tokens for programmatic access
CREATE TABLE api_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_name VARCHAR(255) NOT NULL,
    token_hash VARCHAR(255) UNIQUE NOT NULL,
    permissions JSONB DEFAULT '{}',
    last_used_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Two-Factor Authentication (2FA) management
CREATE TABLE user_mfa (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    mfa_type VARCHAR(50) NOT NULL DEFAULT 'totp' CHECK (mfa_type IN ('totp', 'sms', 'email')),
    secret_encrypted TEXT NOT NULL, -- Encrypted TOTP secret
    backup_codes_encrypted TEXT, -- Encrypted JSON array of backup codes
    enabled BOOLEAN DEFAULT false,
    verified_at TIMESTAMP WITH TIME ZONE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, user_id, mfa_type)
);

-- Hardware fingerprinting for enhanced security
CREATE TABLE device_fingerprints (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    fingerprint_hash VARCHAR(255) NOT NULL,
    device_name VARCHAR(255),
    browser_info JSONB,
    ip_address INET,
    is_trusted BOOLEAN DEFAULT false,
    first_seen_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_seen_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Security audit log
CREATE TABLE security_audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    details JSONB DEFAULT '{}',
    success BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Session management for enhanced security
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token_hash VARCHAR(255) UNIQUE NOT NULL,
    device_fingerprint_id UUID REFERENCES device_fingerprints(id) ON DELETE SET NULL,
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Audit log for security and compliance
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id UUID,
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
) PARTITION BY RANGE (created_at);

-- Create monthly partitions for audit_logs (example for current year)
CREATE TABLE audit_logs_2025_01 PARTITION OF audit_logs 
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
CREATE TABLE audit_logs_2025_02 PARTITION OF audit_logs 
FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');
CREATE TABLE audit_logs_2025_03 PARTITION OF audit_logs 
FOR VALUES FROM ('2025-03-01') TO ('2025-04-01');

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Tenant indexes
CREATE INDEX idx_tenants_subdomain ON tenants(subdomain);
CREATE INDEX idx_tenants_active ON tenants(is_active) WHERE is_active = true;
CREATE INDEX idx_tenants_subscription ON tenants(subscription_tier, subscription_status);

-- User indexes
CREATE INDEX idx_users_tenant_email ON users(tenant_id, email);
CREATE INDEX idx_users_tenant_active ON users(tenant_id, is_active) WHERE is_active = true;

-- Device bridge indexes
CREATE INDEX idx_bridges_tenant ON device_bridges(tenant_id);
CREATE INDEX idx_bridges_active ON device_bridges(tenant_id, is_active) WHERE is_active = true;
CREATE INDEX idx_bridges_token ON device_bridges(bridge_token);

-- Device indexes
CREATE INDEX idx_devices_tenant ON tenant_devices(tenant_id);
CREATE INDEX idx_devices_bridge ON tenant_devices(bridge_id);
CREATE INDEX idx_devices_available ON tenant_devices(tenant_id, is_available) WHERE is_available = true;
CREATE INDEX idx_devices_platform ON tenant_devices(tenant_id, platform);

-- Test execution indexes
CREATE INDEX idx_executions_tenant_status ON test_executions(tenant_id, status);
CREATE INDEX idx_executions_user ON test_executions(user_id);
CREATE INDEX idx_executions_device ON test_executions(device_id);
CREATE INDEX idx_executions_created ON test_executions(created_at DESC);

-- Usage tracking indexes
CREATE INDEX idx_usage_tenant_period ON usage_tracking(tenant_id, billing_period);
CREATE INDEX idx_usage_metric ON usage_tracking(metric_type, billing_period);

-- API token indexes
CREATE INDEX idx_tokens_tenant ON api_tokens(tenant_id);
CREATE INDEX idx_tokens_hash ON api_tokens(token_hash);
CREATE INDEX idx_tokens_active ON api_tokens(tenant_id, is_active) WHERE is_active = true;

-- Audit log indexes
CREATE INDEX idx_audit_tenant ON audit_logs(tenant_id);
CREATE INDEX idx_audit_user ON audit_logs(user_id);
CREATE INDEX idx_audit_action ON audit_logs(action);
CREATE INDEX idx_audit_created ON audit_logs(created_at DESC);

-- Automation sessions for standalone service access
CREATE TABLE automation_sessions (
    id SERIAL PRIMARY KEY,
    token TEXT NOT NULL UNIQUE,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    platform VARCHAR(20) NOT NULL CHECK (platform IN ('ios', 'android')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_used_at TIMESTAMP WITH TIME ZONE
);

-- Automation session indexes
CREATE INDEX idx_automation_sessions_token ON automation_sessions(token);
CREATE INDEX idx_automation_sessions_user_tenant ON automation_sessions(user_id, tenant_id);
CREATE INDEX idx_automation_sessions_expires_at ON automation_sessions(expires_at);
CREATE INDEX idx_automation_sessions_platform ON automation_sessions(platform);

-- ============================================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- ============================================================================

-- Enable RLS on all tenant-scoped tables
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE device_bridges ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenant_devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE automation_sessions ENABLE ROW LEVEL SECURITY;

-- Create function to get current tenant from session
CREATE OR REPLACE FUNCTION current_tenant_id() RETURNS UUID AS $$
BEGIN
    RETURN COALESCE(
        current_setting('app.current_tenant', true)::UUID,
        '00000000-0000-0000-0000-000000000000'::UUID
    );
END;
$$ LANGUAGE plpgsql STABLE;

-- Create function to get current user from session
CREATE OR REPLACE FUNCTION current_user_id() RETURNS UUID AS $$
BEGIN
    RETURN COALESCE(
        current_setting('app.current_user', true)::UUID,
        '00000000-0000-0000-0000-000000000000'::UUID
    );
END;
$$ LANGUAGE plpgsql STABLE;

-- Tenant policies (only accessible by tenant admin or system admin)
CREATE POLICY tenant_isolation ON tenants
    FOR ALL
    USING (id = current_tenant_id() OR current_setting('app.user_role', true) = 'system_admin');

-- User policies (users can only see users in their tenant)
CREATE POLICY user_tenant_isolation ON users
    FOR ALL
    USING (tenant_id = current_tenant_id());

-- Device bridge policies
CREATE POLICY bridge_tenant_isolation ON device_bridges
    FOR ALL
    USING (tenant_id = current_tenant_id());

-- Device policies
CREATE POLICY device_tenant_isolation ON tenant_devices
    FOR ALL
    USING (tenant_id = current_tenant_id());

-- Test execution policies
CREATE POLICY execution_tenant_isolation ON test_executions
    FOR ALL
    USING (tenant_id = current_tenant_id());

-- Usage tracking policies
CREATE POLICY usage_tenant_isolation ON usage_tracking
    FOR ALL
    USING (tenant_id = current_tenant_id());

-- API token policies
CREATE POLICY token_tenant_isolation ON api_tokens
    FOR ALL
    USING (tenant_id = current_tenant_id());

-- Audit log policies
CREATE POLICY audit_tenant_isolation ON audit_logs
    FOR ALL
    USING (tenant_id = current_tenant_id() OR current_setting('app.user_role', true) = 'system_admin');

-- ============================================================================
-- FUNCTIONS AND TRIGGERS
-- ============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add updated_at triggers
CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON tenants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bridges_updated_at BEFORE UPDATE ON device_bridges
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_devices_updated_at BEFORE UPDATE ON tenant_devices
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to track usage metrics
CREATE OR REPLACE FUNCTION track_usage_metric(
    p_tenant_id UUID,
    p_metric_type VARCHAR(50),
    p_metric_value DECIMAL(10,2),
    p_billing_period DATE DEFAULT DATE_TRUNC('month', CURRENT_DATE)::DATE
) RETURNS VOID AS $$
BEGIN
    INSERT INTO usage_tracking (tenant_id, metric_type, metric_value, billing_period)
    VALUES (p_tenant_id, p_metric_type, p_metric_value, p_billing_period)
    ON CONFLICT (tenant_id, metric_type, billing_period)
    DO UPDATE SET
        metric_value = usage_tracking.metric_value + EXCLUDED.metric_value,
        recorded_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Function to log audit events
CREATE OR REPLACE FUNCTION log_audit_event(
    p_tenant_id UUID,
    p_user_id UUID,
    p_action VARCHAR(100),
    p_resource_type VARCHAR(50) DEFAULT NULL,
    p_resource_id UUID DEFAULT NULL,
    p_details JSONB DEFAULT '{}',
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL
) RETURNS VOID AS $$
BEGIN
    INSERT INTO audit_logs (
        tenant_id, user_id, action, resource_type, resource_id,
        details, ip_address, user_agent
    ) VALUES (
        p_tenant_id, p_user_id, p_action, p_resource_type, p_resource_id,
        p_details, p_ip_address, p_user_agent
    );
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- INITIAL DATA AND SETUP
-- ============================================================================

-- Create system admin user (for initial setup)
INSERT INTO tenants (id, name, subdomain, subscription_tier, max_devices, max_test_minutes, is_active)
VALUES (
    '00000000-0000-0000-0000-000000000001',
    'System Administration',
    'system',
    'enterprise',
    999,
    999999,
    true
) ON CONFLICT (id) DO NOTHING;

-- Create default subscription tiers configuration
CREATE TABLE subscription_tiers (
    tier_name VARCHAR(50) PRIMARY KEY,
    max_devices INTEGER NOT NULL,
    max_test_minutes INTEGER NOT NULL,
    max_concurrent_tests INTEGER NOT NULL,
    storage_limit_gb INTEGER NOT NULL,
    monthly_price_usd DECIMAL(10,2) NOT NULL,
    features JSONB DEFAULT '{}'
);

INSERT INTO subscription_tiers VALUES
('starter', 2, 500, 1, 1, 19.00, '{"basic_reporting": true, "email_support": true}'),
('professional', 5, 2000, 3, 5, 49.00, '{"advanced_reporting": true, "priority_support": true, "api_access": true}'),
('team', 10, 5000, 5, 20, 99.00, '{"team_collaboration": true, "dedicated_support": true, "custom_integrations": true}'),
('enterprise', 999, 999999, 20, 100, 199.00, '{"unlimited_devices": true, "24_7_support": true, "white_label": true, "on_premise": true}');

-- Grant permissions to application user (to be created during deployment)
-- GRANT USAGE ON SCHEMA public TO mobile_automation_app;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO mobile_automation_app;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO mobile_automation_app;
