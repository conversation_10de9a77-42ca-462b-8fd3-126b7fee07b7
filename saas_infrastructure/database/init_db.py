#!/usr/bin/env python3
"""
Database initialization script for Mobile Automation SaaS Platform
Handles PostgreSQL database setup, schema creation, and initial data loading
"""

import os
import sys
import psycopg2
import psycopg2.extras
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import argparse
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DatabaseInitializer:
    def __init__(self, host='localhost', port=5432, admin_user='postgres', admin_password=None):
        self.host = host
        self.port = port
        self.admin_user = admin_user
        self.admin_password = admin_password
        self.db_name = 'mobile_automation_saas'
        self.app_user = 'mobile_automation_app'
        self.app_password = None
        
    def generate_app_password(self):
        """Generate a secure password for the application user"""
        import secrets
        import string
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        self.app_password = ''.join(secrets.choice(alphabet) for _ in range(16))
        return self.app_password
    
    def connect_as_admin(self):
        """Connect to PostgreSQL as admin user"""
        try:
            conn = psycopg2.connect(
                host=self.host,
                port=self.port,
                user=self.admin_user,
                password=self.admin_password,
                database='postgres'  # Connect to default database first
            )
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            return conn
        except psycopg2.Error as e:
            logger.error(f"Failed to connect as admin: {e}")
            raise
    
    def connect_to_app_db(self):
        """Connect to the application database"""
        try:
            conn = psycopg2.connect(
                host=self.host,
                port=self.port,
                user=self.app_user,
                password=self.app_password,
                database=self.db_name
            )
            return conn
        except psycopg2.Error as e:
            logger.error(f"Failed to connect to application database: {e}")
            raise
    
    def database_exists(self, cursor, db_name):
        """Check if database exists"""
        cursor.execute(
            "SELECT 1 FROM pg_database WHERE datname = %s",
            (db_name,)
        )
        return cursor.fetchone() is not None
    
    def user_exists(self, cursor, username):
        """Check if user exists"""
        cursor.execute(
            "SELECT 1 FROM pg_user WHERE usename = %s",
            (username,)
        )
        return cursor.fetchone() is not None
    
    def create_database(self):
        """Create the application database and user"""
        logger.info("Creating database and application user...")
        
        conn = self.connect_as_admin()
        cursor = conn.cursor()
        
        try:
            # Generate app password if not set
            if not self.app_password:
                self.generate_app_password()
            
            # Create application user if it doesn't exist
            if not self.user_exists(cursor, self.app_user):
                cursor.execute(f"""
                    CREATE USER {self.app_user} WITH 
                    PASSWORD '{self.app_password}'
                    CREATEDB
                    LOGIN;
                """)
                logger.info(f"Created application user: {self.app_user}")
            else:
                logger.info(f"Application user {self.app_user} already exists")
            
            # Create database if it doesn't exist
            if not self.database_exists(cursor, self.db_name):
                cursor.execute(f"""
                    CREATE DATABASE {self.db_name}
                    WITH OWNER = {self.app_user}
                    ENCODING = 'UTF8'
                    LC_COLLATE = 'en_US.UTF-8'
                    LC_CTYPE = 'en_US.UTF-8'
                    TEMPLATE = template0;
                """)
                logger.info(f"Created database: {self.db_name}")
            else:
                logger.info(f"Database {self.db_name} already exists")
            
            # Grant privileges
            cursor.execute(f"GRANT ALL PRIVILEGES ON DATABASE {self.db_name} TO {self.app_user};")
            
        except psycopg2.Error as e:
            logger.error(f"Error creating database: {e}")
            raise
        finally:
            cursor.close()
            conn.close()
    
    def load_schema(self):
        """Load the database schema"""
        logger.info("Loading database schema...")
        
        schema_file = Path(__file__).parent / 'schema.sql'
        if not schema_file.exists():
            raise FileNotFoundError(f"Schema file not found: {schema_file}")
        
        conn = self.connect_to_app_db()
        cursor = conn.cursor()
        
        try:
            with open(schema_file, 'r') as f:
                schema_sql = f.read()
            
            # Execute schema in chunks (split by semicolon)
            statements = [stmt.strip() for stmt in schema_sql.split(';') if stmt.strip()]
            
            for i, statement in enumerate(statements):
                try:
                    cursor.execute(statement)
                    conn.commit()
                except psycopg2.Error as e:
                    logger.warning(f"Statement {i+1} failed (may be expected): {e}")
                    conn.rollback()
            
            logger.info("Schema loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading schema: {e}")
            conn.rollback()
            raise
        finally:
            cursor.close()
            conn.close()
    
    def create_sample_tenant(self, tenant_name="Demo Tenant", subdomain="demo"):
        """Create a sample tenant for testing"""
        logger.info(f"Creating sample tenant: {tenant_name}")
        
        conn = self.connect_to_app_db()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        try:
            # Create tenant
            cursor.execute("""
                INSERT INTO tenants (name, subdomain, subscription_tier, max_devices, max_test_minutes)
                VALUES (%s, %s, %s, %s, %s)
                RETURNING id;
            """, (tenant_name, subdomain, 'professional', 5, 2000))
            
            tenant_id = cursor.fetchone()['id']
            
            # Create admin user for tenant
            import bcrypt
            password_hash = bcrypt.hashpw('admin123'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            cursor.execute("""
                INSERT INTO users (tenant_id, email, password_hash, first_name, last_name, role, email_verified)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                RETURNING id;
            """, (tenant_id, f'admin@{subdomain}.com', password_hash, 'Admin', 'User', 'admin', True))
            
            user_id = cursor.fetchone()['id']
            
            conn.commit()
            
            logger.info(f"Sample tenant created:")
            logger.info(f"  - Tenant ID: {tenant_id}")
            logger.info(f"  - Subdomain: {subdomain}")
            logger.info(f"  - Admin Email: admin@{subdomain}.com")
            logger.info(f"  - Admin Password: admin123")
            
            return tenant_id, user_id
            
        except psycopg2.Error as e:
            logger.error(f"Error creating sample tenant: {e}")
            conn.rollback()
            raise
        finally:
            cursor.close()
            conn.close()
    
    def verify_installation(self):
        """Verify the database installation"""
        logger.info("Verifying database installation...")
        
        conn = self.connect_to_app_db()
        cursor = conn.cursor()
        
        try:
            # Check if main tables exist
            tables = [
                'tenants', 'users', 'device_bridges', 'tenant_devices',
                'test_executions', 'usage_tracking', 'api_tokens', 'audit_logs'
            ]
            
            for table in tables:
                cursor.execute("""
                    SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_name = %s AND table_schema = 'public';
                """, (table,))
                
                if cursor.fetchone()[0] == 0:
                    raise Exception(f"Table {table} not found")
            
            # Check if RLS is enabled
            cursor.execute("""
                SELECT COUNT(*) FROM pg_class c
                JOIN pg_namespace n ON n.oid = c.relnamespace
                WHERE c.relname = 'tenants' AND n.nspname = 'public' AND c.relrowsecurity = true;
            """)
            
            if cursor.fetchone()[0] == 0:
                logger.warning("Row Level Security may not be enabled on tenants table")
            
            # Check tenant count
            cursor.execute("SELECT COUNT(*) FROM tenants;")
            tenant_count = cursor.fetchone()[0]
            
            logger.info(f"Database verification completed successfully")
            logger.info(f"  - All required tables exist")
            logger.info(f"  - {tenant_count} tenants in database")
            
        except Exception as e:
            logger.error(f"Database verification failed: {e}")
            raise
        finally:
            cursor.close()
            conn.close()
    
    def get_connection_info(self):
        """Get connection information for the application"""
        return {
            'host': self.host,
            'port': self.port,
            'database': self.db_name,
            'user': self.app_user,
            'password': self.app_password
        }

def main():
    parser = argparse.ArgumentParser(description='Initialize Mobile Automation SaaS Database')
    parser.add_argument('--host', default='localhost', help='PostgreSQL host')
    parser.add_argument('--port', type=int, default=5432, help='PostgreSQL port')
    parser.add_argument('--admin-user', default='postgres', help='PostgreSQL admin user')
    parser.add_argument('--admin-password', help='PostgreSQL admin password')
    parser.add_argument('--app-password', help='Application user password (auto-generated if not provided)')
    parser.add_argument('--create-sample', action='store_true', help='Create sample tenant')
    parser.add_argument('--verify-only', action='store_true', help='Only verify existing installation')
    
    args = parser.parse_args()
    
    # Get admin password from environment if not provided
    admin_password = args.admin_password or os.environ.get('POSTGRES_ADMIN_PASSWORD')
    if not admin_password and not args.verify_only:
        admin_password = input("Enter PostgreSQL admin password: ")
    
    try:
        initializer = DatabaseInitializer(
            host=args.host,
            port=args.port,
            admin_user=args.admin_user,
            admin_password=admin_password
        )
        
        if args.app_password:
            initializer.app_password = args.app_password
        
        if not args.verify_only:
            # Create database and user
            initializer.create_database()
            
            # Load schema
            initializer.load_schema()
            
            # Create sample tenant if requested
            if args.create_sample:
                initializer.create_sample_tenant()
        
        # Verify installation
        initializer.verify_installation()
        
        # Output connection info
        conn_info = initializer.get_connection_info()
        logger.info("Database initialization completed successfully!")
        logger.info("Connection information:")
        for key, value in conn_info.items():
            if key == 'password':
                logger.info(f"  {key}: {'*' * len(value) if value else 'Not set'}")
            else:
                logger.info(f"  {key}: {value}")
        
        # Save connection info to file
        env_file = Path(__file__).parent.parent / 'deployment' / '.env.database'
        env_file.parent.mkdir(exist_ok=True)
        
        with open(env_file, 'w') as f:
            f.write(f"DATABASE_HOST={conn_info['host']}\n")
            f.write(f"DATABASE_PORT={conn_info['port']}\n")
            f.write(f"DATABASE_NAME={conn_info['database']}\n")
            f.write(f"DATABASE_USER={conn_info['user']}\n")
            f.write(f"DATABASE_PASSWORD={conn_info['password']}\n")
        
        logger.info(f"Database configuration saved to: {env_file}")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
