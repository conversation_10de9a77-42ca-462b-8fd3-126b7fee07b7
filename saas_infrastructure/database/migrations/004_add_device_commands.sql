-- Migration: Add device commands table for tracking commands sent to devices
-- Version: 004
-- Description: Add device_commands table to track commands sent to devices through bridges

-- Create device_commands table
CREATE TABLE IF NOT EXISTS device_commands (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    device_id VARCHAR(255) NOT NULL,
    bridge_id INTEGER NOT NULL REFERENCES device_bridges(id) ON DELETE CASCADE,
    command_id VARCHAR(255) NOT NULL UNIQUE,
    command_type VARCHAR(100) NOT NULL,
    parameters JSONB DEFAULT '{}',
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'completed', 'failed', 'timeout')),
    response JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    sent_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_device_commands_tenant_id ON device_commands(tenant_id);
CREATE INDEX IF NOT EXISTS idx_device_commands_device_id ON device_commands(device_id);
CREATE INDEX IF NOT EXISTS idx_device_commands_bridge_id ON device_commands(bridge_id);
CREATE INDEX IF NOT EXISTS idx_device_commands_command_id ON device_commands(command_id);
CREATE INDEX IF NOT EXISTS idx_device_commands_status ON device_commands(status);
CREATE INDEX IF NOT EXISTS idx_device_commands_created_at ON device_commands(created_at);

-- Add RLS policy for tenant isolation
ALTER TABLE device_commands ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only access commands for their tenant
CREATE POLICY device_commands_tenant_isolation ON device_commands
    FOR ALL
    TO authenticated_users
    USING (tenant_id = current_setting('app.current_tenant_id')::INTEGER);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON device_commands TO mobile_automation_app;
GRANT USAGE, SELECT ON SEQUENCE device_commands_id_seq TO mobile_automation_app;

-- Add comments
COMMENT ON TABLE device_commands IS 'Commands sent to devices through device bridges';
COMMENT ON COLUMN device_commands.tenant_id IS 'ID of the tenant that owns this command';
COMMENT ON COLUMN device_commands.device_id IS 'UDID of the target device';
COMMENT ON COLUMN device_commands.bridge_id IS 'ID of the bridge that will execute the command';
COMMENT ON COLUMN device_commands.command_id IS 'Unique identifier for this command';
COMMENT ON COLUMN device_commands.command_type IS 'Type of command (start_test, stop_test, get_info, etc.)';
COMMENT ON COLUMN device_commands.parameters IS 'JSON parameters for the command';
COMMENT ON COLUMN device_commands.status IS 'Current status of the command';
COMMENT ON COLUMN device_commands.response IS 'JSON response from the device/bridge';
COMMENT ON COLUMN device_commands.created_at IS 'When the command was created';
COMMENT ON COLUMN device_commands.sent_at IS 'When the command was sent to the bridge';
COMMENT ON COLUMN device_commands.completed_at IS 'When the command was completed';
COMMENT ON COLUMN device_commands.error_message IS 'Error message if command failed';
