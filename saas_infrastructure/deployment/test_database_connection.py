#!/usr/bin/env python3
"""
Database Connection Test Script
Tests PostgreSQL connectivity for the Mobile Automation SaaS Platform
"""

import os
import sys
from sqlalchemy import create_engine, text
from dotenv import load_dotenv

def test_database_connection():
    """Test database connection and basic operations"""
    
    print("=" * 70)
    print("Mobile Automation SaaS Platform - Database Connection Test")
    print("=" * 70)
    
    # Load environment variables
    env_path = '/opt/mobile-automation-saas/saas_infrastructure/app/.env'
    if os.path.exists(env_path):
        load_dotenv(env_path)
        print(f"✓ Loaded environment variables from {env_path}")
    else:
        print(f"⚠ Environment file not found at {env_path}")
        print("Using default database configuration...")
        os.environ['DATABASE_URL'] = 'postgresql://saas_test_user:test_password_123@localhost:5432/mobile_automation_saas_test'
    
    database_url = os.environ.get('DATABASE_URL')
    if not database_url:
        print("✗ DATABASE_URL not found in environment variables")
        return False
    
    # Hide password in output
    safe_url = database_url.replace(':test_password_123', ':***')
    print(f"Database URL: {safe_url}")
    print()
    
    try:
        # Test 1: Basic connection
        print("Test 1: Basic database connection...")
        engine = create_engine(database_url)
        
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1 as test"))
            test_value = result.scalar()
            if test_value == 1:
                print("✓ Basic connection successful")
            else:
                print("✗ Basic connection test failed")
                return False
        
        # Test 2: Check database version
        print("\nTest 2: PostgreSQL version check...")
        with engine.connect() as conn:
            result = conn.execute(text("SELECT version()"))
            version = result.scalar()
            print(f"✓ PostgreSQL version: {version.split(',')[0]}")
        
        # Test 3: Check if tenants table exists
        print("\nTest 3: Tenants table existence...")
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'tenants'
                )
            """))
            table_exists = result.scalar()
            
            if table_exists:
                print("✓ Tenants table exists")
                
                # Test 4: Count tenants
                print("\nTest 4: Tenant data check...")
                result = conn.execute(text("SELECT COUNT(*) FROM tenants"))
                tenant_count = result.scalar()
                print(f"✓ Found {tenant_count} tenants in database")
                
                if tenant_count > 0:
                    # Test 5: List tenants
                    print("\nTest 5: Tenant details...")
                    result = conn.execute(text("""
                        SELECT id, name, subdomain, subscription_tier, is_active 
                        FROM tenants 
                        ORDER BY id
                    """))
                    tenants = result.fetchall()
                    
                    for tenant in tenants:
                        status = "Active" if tenant.is_active else "Inactive"
                        print(f"  - ID: {tenant.id}, Name: {tenant.name}, Subdomain: {tenant.subdomain}, Tier: {tenant.subscription_tier}, Status: {status}")
                    
                    # Test 6: Test tenant lookup
                    print("\nTest 6: Test tenant lookup...")
                    test_subdomains = ['testcompany1', 'testcompany2']
                    
                    for subdomain in test_subdomains:
                        result = conn.execute(text("""
                            SELECT id, name, subdomain, subscription_tier 
                            FROM tenants 
                            WHERE subdomain = :subdomain AND is_active = true
                        """), {"subdomain": subdomain})
                        
                        tenant = result.fetchone()
                        if tenant:
                            print(f"  ✓ Found tenant '{subdomain}': {tenant.name} (ID: {tenant.id})")
                        else:
                            print(f"  ⚠ Tenant '{subdomain}' not found or inactive")
                
            else:
                print("⚠ Tenants table does not exist")
                print("  This may be normal if the database schema hasn't been initialized yet")
        
        # Test 7: Check users table (if exists)
        print("\nTest 7: Users table check...")
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'users'
                )
            """))
            users_table_exists = result.scalar()
            
            if users_table_exists:
                result = conn.execute(text("SELECT COUNT(*) FROM users"))
                user_count = result.scalar()
                print(f"✓ Users table exists with {user_count} users")
                
                if user_count > 0:
                    # Show test users
                    result = conn.execute(text("""
                        SELECT u.email, t.subdomain, u.is_active
                        FROM users u
                        JOIN tenants t ON u.tenant_id = t.id
                        WHERE u.email IN ('<EMAIL>', '<EMAIL>')
                    """))
                    test_users = result.fetchall()
                    
                    for user in test_users:
                        status = "Active" if user.is_active else "Inactive"
                        print(f"  - {user.email} @ {user.subdomain} ({status})")
            else:
                print("⚠ Users table does not exist")
        
        print("\n" + "=" * 70)
        print("✓ ALL DATABASE TESTS PASSED")
        print("✓ Database connection is working correctly")
        print("=" * 70)
        return True
        
    except Exception as e:
        print(f"\n✗ Database connection failed: {e}")
        print("\nTroubleshooting steps:")
        print("1. Check if PostgreSQL is running: systemctl status postgresql")
        print("2. Verify database credentials in .env file")
        print("3. Check pg_hba.conf authentication settings")
        print("4. Test manual connection: PGPASSWORD='test_password_123' psql -h localhost -U saas_test_user -d mobile_automation_saas_test")
        print("=" * 70)
        return False

if __name__ == "__main__":
    success = test_database_connection()
    sys.exit(0 if success else 1)
