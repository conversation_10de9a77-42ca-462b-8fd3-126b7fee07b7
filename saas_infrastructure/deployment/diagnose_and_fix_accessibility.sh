#!/bin/bash

# Comprehensive Diagnostic and Fix Script for External Accessibility Issues
# Addresses binding, firewall, API routing, and multi-tenant functionality

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${CYAN}[STEP]${NC} $1"; }

# Check if running as root
if [[ $EUID -ne 0 ]]; then
    log_error "This script must be run as root"
    exit 1
fi

echo "========================================================================"
echo "Mobile Automation SaaS Platform - Accessibility Diagnostic & Fix"
echo "========================================================================"

# Step 1: Diagnose Current Issues
log_step "Step 1: Diagnosing current accessibility issues..."

# Check if application is running
log_info "Checking application status..."
APP_STATUS=$(supervisorctl status mobile-automation-saas 2>/dev/null || echo "NOT_RUNNING")
if [[ "$APP_STATUS" == *"RUNNING"* ]]; then
    log_success "Application is running under Supervisor"
else
    log_warning "Application is not running: $APP_STATUS"
fi

# Check what ports are being listened on
log_info "Checking port bindings..."
LISTENING_PORTS=$(netstat -tlnp | grep :5000 || echo "No port 5000 found")
echo "Port 5000 status: $LISTENING_PORTS"

if echo "$LISTENING_PORTS" | grep -q "127.0.0.1:5000"; then
    log_warning "Application is binding to localhost only (127.0.0.1:5000)"
    BINDING_ISSUE=true
else
    BINDING_ISSUE=false
fi

# Check firewall status
log_info "Checking firewall status..."
UFW_STATUS=$(ufw status 2>/dev/null || echo "ufw not available")
IPTABLES_STATUS=$(iptables -L INPUT -n | grep 5000 || echo "No iptables rules for port 5000")
echo "UFW Status: $UFW_STATUS"
echo "IPTables for port 5000: $IPTABLES_STATUS"

# Test local connectivity
log_info "Testing local connectivity..."
LOCAL_HEALTH=$(curl -s http://localhost:5000/health 2>/dev/null || echo "FAILED")
if [[ "$LOCAL_HEALTH" == *"healthy"* ]]; then
    log_success "Local health check passed"
else
    log_warning "Local health check failed: $LOCAL_HEALTH"
fi

# Step 2: Stop application for fixes
log_step "Step 2: Stopping application for comprehensive fixes..."
supervisorctl stop mobile-automation-saas 2>/dev/null || true
supervisorctl stop mobile-automation-saas-dev 2>/dev/null || true

# Step 3: Apply comprehensive fixes
log_step "Step 3: Applying comprehensive fixes..."

cd /opt/mobile-automation-saas

# Pull latest code with fixes
log_info "Updating code with latest fixes..."
git fetch origin saas-implement
git reset --hard origin/saas-implement

# Activate virtual environment and fix dependencies
log_info "Fixing Python dependencies..."
source venv/bin/activate

# Install/upgrade critical dependencies
pip install --upgrade setuptools>=65.0.0
pip uninstall -y eventlet || true
pip install eventlet>=0.33.3
pip uninstall -y gunicorn || true
pip install gunicorn==21.2.0
pip install -r saas_infrastructure/app/requirements.txt

# Step 4: Fix application binding configuration
log_step "Step 4: Fixing application binding to allow external access..."

# Update the SaaS app to bind to all interfaces
log_info "Updating application configuration for external access..."
cat > saas_infrastructure/app/.env << 'EOF'
# Environment Configuration
ENVIRONMENT=test
FLASK_ENV=test
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
DEBUG=True

# Database Configuration
DATABASE_URL=postgresql://saas_test_user:test_password_123@localhost:5432/mobile_automation_saas_test

# Security Configuration
SECRET_KEY=test-secret-key-change-in-production
JWT_SECRET_KEY=jwt-test-secret-key-change-in-production

# Logging
LOG_LEVEL=INFO
EOF

# Update Supervisor configuration for external binding
log_info "Updating Supervisor configuration..."
cat > /etc/supervisor/conf.d/mobile-automation-saas.conf << 'EOF'
[program:mobile-automation-saas]
command=/opt/mobile-automation-saas/venv/bin/python -m saas_infrastructure.app.saas_app
directory=/opt/mobile-automation-saas
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/mobile-automation-saas.log
stderr_logfile=/var/log/mobile-automation-saas.log
environment=PATH="/opt/mobile-automation-saas/venv/bin",PYTHONPATH="/opt/mobile-automation-saas",ENVIRONMENT="test",FLASK_ENV="test",FLASK_HOST="0.0.0.0",FLASK_PORT="5000",DATABASE_URL="postgresql://saas_test_user:test_password_123@localhost:5432/mobile_automation_saas_test"
stopasgroup=true
killasgroup=true
EOF

# Step 5: Configure firewall for external access
log_step "Step 5: Configuring firewall for external access..."

# Allow port 5000 through UFW if available
if command -v ufw >/dev/null 2>&1; then
    log_info "Configuring UFW firewall..."
    ufw allow 5000/tcp
    ufw --force enable
    log_success "UFW configured to allow port 5000"
fi

# Configure iptables as backup
log_info "Configuring iptables..."
iptables -I INPUT -p tcp --dport 5000 -j ACCEPT
iptables-save > /etc/iptables/rules.v4 2>/dev/null || true
log_success "IPTables configured to allow port 5000"

# Step 6: Set proper permissions
log_step "Step 6: Setting proper permissions..."
chown -R www-data:www-data /opt/mobile-automation-saas
chmod +x /opt/mobile-automation-saas/venv/bin/python

# Create log files with proper permissions
touch /var/log/mobile-automation-saas.log
chown www-data:www-data /var/log/mobile-automation-saas.log

# Step 7: Start application
log_step "Step 7: Starting application with new configuration..."
supervisorctl reread
supervisorctl update
supervisorctl start mobile-automation-saas

# Wait for application to start
log_info "Waiting for application to start..."
sleep 15

# Step 8: Verify application is running and accessible
log_step "Step 8: Verifying application accessibility..."

# Check application status
APP_STATUS=$(supervisorctl status mobile-automation-saas)
if [[ "$APP_STATUS" == *"RUNNING"* ]]; then
    log_success "Application is running: $APP_STATUS"
else
    log_error "Application failed to start: $APP_STATUS"
    log_info "Checking logs..."
    tail -20 /var/log/mobile-automation-saas.log
    exit 1
fi

# Check port binding
log_info "Verifying port binding..."
sleep 5
NEW_BINDING=$(netstat -tlnp | grep :5000)
echo "New port binding: $NEW_BINDING"

if echo "$NEW_BINDING" | grep -q "0.0.0.0:5000"; then
    log_success "Application is now binding to all interfaces (0.0.0.0:5000)"
else
    log_warning "Application may still be binding to localhost only"
fi

# Step 9: Test external accessibility
log_step "Step 9: Testing external accessibility..."

SERVER_IP=$(hostname -I | awk '{print $1}')
log_info "Server IP: $SERVER_IP"

# Test health endpoint
log_info "Testing health endpoint..."
for i in {1..10}; do
    HEALTH_RESPONSE=$(curl -s http://$SERVER_IP:5000/health 2>/dev/null || echo "FAILED")
    if [[ "$HEALTH_RESPONSE" == *"healthy"* ]]; then
        log_success "External health check passed"
        break
    else
        if [ $i -eq 10 ]; then
            log_error "External health check failed after 10 attempts"
            log_info "Response: $HEALTH_RESPONSE"
        else
            log_info "Waiting for external access... (attempt $i/10)"
            sleep 3
        fi
    fi
done

# Test API root endpoint
log_info "Testing API root endpoint..."
API_RESPONSE=$(curl -s http://$SERVER_IP:5000/api/ 2>/dev/null || echo "FAILED")
if [[ "$API_RESPONSE" == *"Mobile Automation SaaS API"* ]]; then
    log_success "API root endpoint accessible"
else
    log_warning "API root endpoint test failed: $API_RESPONSE"
fi

# Step 10: Test multi-tenant functionality
log_step "Step 10: Testing multi-tenant functionality..."

# Test tenant info endpoint
log_info "Testing tenant info endpoint..."
TENANT_RESPONSE=$(curl -s -H "X-Tenant-Subdomain: testcompany1" http://$SERVER_IP:5000/api/tenant/info 2>/dev/null || echo "FAILED")
if [[ "$TENANT_RESPONSE" == *"tenant"* ]]; then
    log_success "Tenant info endpoint working"
else
    log_warning "Tenant info test: $TENANT_RESPONSE"
fi

# Test authentication for both test users
log_info "Testing authentication for test users..."

# Test user 1
AUTH1_RESPONSE=$(curl -s -X POST http://$SERVER_IP:5000/api/auth/login \
    -H "Content-Type: application/json" \
    -H "X-Tenant-Subdomain: testcompany1" \
    -d '{"email": "<EMAIL>", "password": "testpass123"}' 2>/dev/null || echo "FAILED")

if [[ "$AUTH1_RESPONSE" == *"access_token"* ]]; then
    log_success "Test user 1 authentication successful"
else
    log_warning "Test user 1 authentication failed: $AUTH1_RESPONSE"
fi

# Test user 2
AUTH2_RESPONSE=$(curl -s -X POST http://$SERVER_IP:5000/api/auth/login \
    -H "Content-Type: application/json" \
    -H "X-Tenant-Subdomain: testcompany2" \
    -d '{"email": "<EMAIL>", "password": "testpass456"}' 2>/dev/null || echo "FAILED")

if [[ "$AUTH2_RESPONSE" == *"access_token"* ]]; then
    log_success "Test user 2 authentication successful"
else
    log_warning "Test user 2 authentication failed: $AUTH2_RESPONSE"
fi

# Step 11: Final status report
echo
echo "========================================================================"
log_success "ACCESSIBILITY DIAGNOSTIC & FIX COMPLETED"
echo "========================================================================"

log_info "Application Status:"
supervisorctl status mobile-automation-saas

echo
log_info "External Access Information:"
log_info "  Server IP: $SERVER_IP"
log_info "  Application URL: http://$SERVER_IP:5000"
log_info "  Health Check: http://$SERVER_IP:5000/health"
log_info "  API Root: http://$SERVER_IP:5000/api/"

echo
log_info "Test Users for Multi-Tenant Testing:"
log_info "  Tenant 1: <EMAIL> / testpass123 (testcompany1)"
log_info "  Tenant 2: <EMAIL> / testpass456 (testcompany2)"

echo
log_info "Port Binding Status:"
netstat -tlnp | grep :5000

echo
log_info "Recent Application Logs:"
tail -10 /var/log/mobile-automation-saas.log

echo
log_success "The Mobile Automation SaaS Platform should now be externally accessible!"
log_info "Run the validation script to perform comprehensive testing:"
log_info "  ./saas_infrastructure/deployment/validate_deployment.sh"
