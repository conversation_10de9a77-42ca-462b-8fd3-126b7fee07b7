{"environment": "test", "database": {"host": "localhost", "port": 5432, "name": "mobile_automation_saas_test", "user": "saas_test_user", "password": "test_password_123", "ssl_mode": "prefer"}, "app": {"host": "0.0.0.0", "port": 5000, "secret_key": "test_secret_key_for_development_only", "jwt_secret": "test_jwt_secret_for_development_only", "debug": true}, "cloudflare": {"tunnel_name": "mobile-automation-test-tunnel", "tunnel_token": "", "domain": "*************"}, "server": {"provider": "custom", "ip": "*************", "ssh_user": "root", "ssh_key": ""}, "ssl": {"enabled": false, "email": "<EMAIL>", "use_letsencrypt": false}, "monitoring": {"enabled": true, "log_level": "DEBUG"}, "test_mode": true, "create_test_users": true, "test_users": [{"email": "<EMAIL>", "password": "testpass123", "tenant_name": "Test Company 1", "tenant_subdomain": "testcompany1", "role": "admin"}, {"email": "<EMAIL>", "password": "testpass456", "tenant_name": "Test Company 2", "tenant_subdomain": "testcompany2", "role": "admin"}]}