#!/bin/bash

# Mobile Automation SaaS Platform - Environment Setup Script
# This script prepares a fresh server for deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

# Update system packages
update_system() {
    log_info "Updating system packages..."
    apt update && apt upgrade -y
    log_success "System packages updated"
}

# Install required system packages
install_system_packages() {
    log_info "Installing system packages..."
    
    packages=(
        "python3"
        "python3-pip"
        "python3-venv"
        "python3-dev"
        "postgresql-client"
        "nginx"
        "supervisor"
        "git"
        "curl"
        "wget"
        "unzip"
        "build-essential"
        "libpq-dev"
        "certbot"
        "python3-certbot-nginx"
    )
    
    apt install -y "${packages[@]}"
    log_success "System packages installed"
}

# Setup PostgreSQL
setup_postgresql() {
    log_info "Setting up PostgreSQL..."
    
    # Install PostgreSQL
    apt install -y postgresql postgresql-contrib
    
    # Start and enable PostgreSQL
    systemctl start postgresql
    systemctl enable postgresql
    
    log_success "PostgreSQL installed and started"
}

# Setup firewall
setup_firewall() {
    log_info "Setting up firewall..."
    
    # Install ufw if not present
    apt install -y ufw
    
    # Reset firewall rules
    ufw --force reset
    
    # Default policies
    ufw default deny incoming
    ufw default allow outgoing
    
    # Allow SSH
    ufw allow ssh
    
    # Allow HTTP and HTTPS
    ufw allow 80/tcp
    ufw allow 443/tcp
    
    # Enable firewall
    ufw --force enable
    
    log_success "Firewall configured"
}

# Create application user
create_app_user() {
    log_info "Creating application user..."
    
    # Create saas user if it doesn't exist
    if ! id "saas" &>/dev/null; then
        useradd -r -s /bin/bash -d /opt/mobile-automation-saas -m saas
        log_success "Created saas user"
    else
        log_warning "User 'saas' already exists"
    fi
    
    # Add saas user to www-data group
    usermod -a -G www-data saas
}

# Setup application directory
setup_app_directory() {
    log_info "Setting up application directory..."
    
    APP_DIR="/opt/mobile-automation-saas"
    
    # Create directory structure
    mkdir -p "$APP_DIR"/{app,logs,data,backups}
    
    # Set ownership
    chown -R saas:saas "$APP_DIR"
    
    # Set permissions
    chmod 755 "$APP_DIR"
    chmod 750 "$APP_DIR"/{logs,data,backups}
    
    log_success "Application directory created"
}

# Install Python dependencies globally
install_python_deps() {
    log_info "Installing Python dependencies..."
    
    # Upgrade pip
    python3 -m pip install --upgrade pip
    
    # Install common packages
    pip3 install virtualenv supervisor
    
    log_success "Python dependencies installed"
}

# Setup log rotation
setup_log_rotation() {
    log_info "Setting up log rotation..."
    
    cat > /etc/logrotate.d/mobile-automation-saas << 'EOF'
/opt/mobile-automation-saas/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 saas saas
    postrotate
        supervisorctl restart mobile-automation-saas > /dev/null 2>&1 || true
    endscript
}
EOF
    
    log_success "Log rotation configured"
}

# Setup system limits
setup_system_limits() {
    log_info "Setting up system limits..."
    
    cat >> /etc/security/limits.conf << 'EOF'
# Mobile Automation SaaS limits
saas soft nofile 65536
saas hard nofile 65536
saas soft nproc 32768
saas hard nproc 32768
EOF
    
    # Update systemd limits
    mkdir -p /etc/systemd/system.conf.d
    cat > /etc/systemd/system.conf.d/limits.conf << 'EOF'
[Manager]
DefaultLimitNOFILE=65536
DefaultLimitNPROC=32768
EOF
    
    log_success "System limits configured"
}

# Setup swap if needed
setup_swap() {
    log_info "Checking swap configuration..."
    
    # Check if swap exists
    if ! swapon --show | grep -q "/swapfile"; then
        log_info "Creating swap file..."
        
        # Create 2GB swap file
        fallocate -l 2G /swapfile
        chmod 600 /swapfile
        mkswap /swapfile
        swapon /swapfile
        
        # Add to fstab
        echo '/swapfile none swap sw 0 0' >> /etc/fstab
        
        log_success "Swap file created"
    else
        log_warning "Swap already configured"
    fi
}

# Optimize system for web application
optimize_system() {
    log_info "Optimizing system settings..."
    
    # Network optimizations
    cat >> /etc/sysctl.conf << 'EOF'
# Mobile Automation SaaS optimizations
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_tw_buckets = 400000
net.ipv4.tcp_tw_reuse = 1
net.ipv4.ip_local_port_range = 1024 65535
vm.swappiness = 10
EOF
    
    # Apply settings
    sysctl -p
    
    log_success "System optimizations applied"
}

# Install Cloudflared
install_cloudflared() {
    log_info "Installing Cloudflared..."
    
    # Download and install cloudflared
    wget -O /tmp/cloudflared.deb https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64.deb
    dpkg -i /tmp/cloudflared.deb
    rm /tmp/cloudflared.deb
    
    # Create cloudflared directory
    mkdir -p /etc/cloudflared
    chown root:root /etc/cloudflared
    chmod 755 /etc/cloudflared
    
    log_success "Cloudflared installed"
}

# Setup monitoring tools
setup_monitoring() {
    log_info "Setting up monitoring tools..."
    
    # Install htop, iotop, etc.
    apt install -y htop iotop nethogs ncdu tree
    
    # Install fail2ban for security
    apt install -y fail2ban
    
    # Configure fail2ban for nginx
    cat > /etc/fail2ban/jail.local << 'EOF'
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[nginx-http-auth]
enabled = true

[nginx-limit-req]
enabled = true
EOF
    
    systemctl enable fail2ban
    systemctl start fail2ban
    
    log_success "Monitoring tools installed"
}

# Create deployment script
create_deployment_script() {
    log_info "Creating deployment helper script..."
    
    cat > /usr/local/bin/deploy-saas << 'EOF'
#!/bin/bash
# Quick deployment helper script

cd /opt/mobile-automation-saas
sudo -u saas python3 deployment/deploy.py "$@"
EOF
    
    chmod +x /usr/local/bin/deploy-saas
    
    log_success "Deployment helper script created"
}

# Main setup function
main() {
    log_info "Starting Mobile Automation SaaS environment setup..."
    
    check_root
    update_system
    install_system_packages
    setup_postgresql
    setup_firewall
    create_app_user
    setup_app_directory
    install_python_deps
    setup_log_rotation
    setup_system_limits
    setup_swap
    optimize_system
    install_cloudflared
    setup_monitoring
    create_deployment_script
    
    log_success "Environment setup completed successfully!"
    echo
    log_info "Next steps:"
    echo "1. Clone the application code to /opt/mobile-automation-saas"
    echo "2. Configure deployment settings in deployment_config.json"
    echo "3. Run: deploy-saas --step all"
    echo
    log_info "Useful commands:"
    echo "- Check application status: supervisorctl status mobile-automation-saas"
    echo "- View application logs: tail -f /opt/mobile-automation-saas/logs/app.log"
    echo "- Restart application: supervisorctl restart mobile-automation-saas"
    echo "- Check nginx status: systemctl status nginx"
    echo
}

# Run main function
main "$@"
