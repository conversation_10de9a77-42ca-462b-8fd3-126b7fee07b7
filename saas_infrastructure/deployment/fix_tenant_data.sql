-- Fix Tenant Data Script
-- This script ensures the tenant table has the correct structure and test data

-- Create tenants table if it doesn't exist
CREATE TABLE IF NOT EXISTS tenants (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    subdomain VARCHAR(100) UNIQUE NOT NULL,
    subscription_tier VARCHAR(50) DEFAULT 'basic',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Create index for faster subdomain lookups
CREATE INDEX IF NOT EXISTS idx_tenants_subdomain ON tenants(subdomain);

-- Add is_active column if it doesn't exist (for backwards compatibility)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tenants' AND column_name = 'is_active') THEN
        ALTER TABLE tenants ADD COLUMN is_active BOOLEAN DEFAULT true;
    END IF;
END $$;

-- Update existing tenants to be active if is_active is NULL
UPDATE tenants SET is_active = true WHERE is_active IS NULL;

-- Insert or update test tenants
INSERT INTO tenants (name, subdomain, subscription_tier, is_active) 
VALUES 
    ('Test Company 1', 'testcompany1', 'basic', true),
    ('Test Company 2', 'testcompany2', 'premium', true)
ON CONFLICT (subdomain) 
DO UPDATE SET 
    name = EXCLUDED.name,
    subscription_tier = EXCLUDED.subscription_tier,
    is_active = EXCLUDED.is_active,
    updated_at = CURRENT_TIMESTAMP;

-- Verify tenant data
SELECT 'Tenant verification:' as info;
SELECT id, name, subdomain, subscription_tier, is_active, created_at 
FROM tenants 
WHERE subdomain IN ('testcompany1', 'testcompany2')
ORDER BY id;

-- Check if users table exists and has test users
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') THEN
        -- Show test users if they exist
        RAISE NOTICE 'Test users in database:';
        PERFORM * FROM users u 
        JOIN tenants t ON u.tenant_id = t.id 
        WHERE u.email IN ('<EMAIL>', '<EMAIL>');
    ELSE
        RAISE NOTICE 'Users table does not exist yet';
    END IF;
END $$;
