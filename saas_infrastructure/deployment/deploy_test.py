#!/usr/bin/env python3
"""
Simplified Test Deployment Script for Mobile Automation SaaS Platform
Deploys to single server at ************* for testing purposes
"""

import os
import sys
import json
import subprocess
import logging
import psycopg2
import bcrypt
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestSaaSDeployer:
    def __init__(self):
        self.config = {
            "environment": "test",
            "database": {
                "host": "localhost",
                "port": 5432,
                "name": "mobile_automation_saas_test",
                "user": "saas_test_user",
                "password": "test_password_123"
            },
            "app": {
                "host": "0.0.0.0",
                "port": 5000,
                "secret_key": "test_secret_key_for_development_only",
                "jwt_secret": "test_jwt_secret_for_development_only",
                "debug": True
            }
        }
        
    def run_command(self, cmd, check=True):
        """Run shell command with logging"""
        logger.info(f"Running: {' '.join(cmd) if isinstance(cmd, list) else cmd}")
        if isinstance(cmd, str):
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        else:
            result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0 and check:
            logger.error(f"Command failed: {result.stderr}")
            raise Exception(f"Command failed: {result.stderr}")
        
        return result
    
    def install_dependencies(self):
        """Install required system packages"""
        logger.info("Installing system dependencies...")
        
        # Update package list
        self.run_command("apt update")
        
        # Install packages
        packages = [
            "python3", "python3-pip", "python3-venv", "python3-dev",
            "postgresql", "postgresql-contrib", "postgresql-client",
            "nginx", "supervisor", "git", "curl", "wget", "build-essential", "libpq-dev"
        ]
        
        self.run_command(["apt", "install", "-y"] + packages)
        logger.info("System dependencies installed")
    
    def setup_postgresql(self):
        """Setup PostgreSQL database"""
        logger.info("Setting up PostgreSQL...")
        
        # Start PostgreSQL
        self.run_command("systemctl start postgresql")
        self.run_command("systemctl enable postgresql")
        
        # Create database and user
        try:
            # Connect as postgres user
            conn = psycopg2.connect(
                host="localhost",
                port=5432,
                database="postgres",
                user="postgres"
            )
            conn.autocommit = True
            cursor = conn.cursor()
            
            # Create database
            try:
                cursor.execute(f"CREATE DATABASE {self.config['database']['name']}")
                logger.info(f"Created database: {self.config['database']['name']}")
            except psycopg2.Error as e:
                if "already exists" in str(e):
                    logger.info("Database already exists")
                else:
                    raise
            
            # Create user
            try:
                cursor.execute(f"""
                    CREATE USER {self.config['database']['user']} 
                    WITH PASSWORD '{self.config['database']['password']}'
                """)
                logger.info(f"Created user: {self.config['database']['user']}")
            except psycopg2.Error as e:
                if "already exists" in str(e):
                    logger.info("User already exists")
                else:
                    raise
            
            # Grant privileges
            cursor.execute(f"""
                GRANT ALL PRIVILEGES ON DATABASE {self.config['database']['name']} 
                TO {self.config['database']['user']}
            """)
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            logger.error(f"PostgreSQL setup failed: {e}")
            raise
    
    def initialize_schema(self):
        """Initialize database schema"""
        logger.info("Initializing database schema...")
        
        schema_file = Path(__file__).parent.parent / "database" / "schema.sql"
        
        if not schema_file.exists():
            logger.error(f"Schema file not found: {schema_file}")
            return
        
        db_config = self.config["database"]
        cmd = [
            "psql",
            f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['name']}",
            "-f", str(schema_file)
        ]
        
        result = self.run_command(cmd, check=False)
        if result.returncode == 0:
            logger.info("Database schema initialized")
        else:
            logger.warning(f"Schema initialization had warnings: {result.stderr}")
    
    def create_test_users(self):
        """Create test users"""
        logger.info("Creating test users...")
        
        test_users = [
            {
                "email": "<EMAIL>",
                "password": "testpass123",
                "tenant_name": "Test Company 1",
                "tenant_subdomain": "testcompany1",
                "role": "admin"
            },
            {
                "email": "<EMAIL>",
                "password": "testpass456", 
                "tenant_name": "Test Company 2",
                "tenant_subdomain": "testcompany2",
                "role": "admin"
            }
        ]
        
        db_config = self.config["database"]
        
        try:
            conn = psycopg2.connect(
                host=db_config["host"],
                port=db_config["port"],
                database=db_config["name"],
                user=db_config["user"],
                password=db_config["password"]
            )
            conn.autocommit = True
            cursor = conn.cursor()
            
            for user in test_users:
                # Create tenant
                cursor.execute("""
                    INSERT INTO tenants (name, subdomain, subscription_tier, is_active)
                    VALUES (%s, %s, 'starter', true)
                    ON CONFLICT (subdomain) DO NOTHING
                    RETURNING id
                """, (user["tenant_name"], user["tenant_subdomain"]))
                
                result = cursor.fetchone()
                if result:
                    tenant_id = result[0]
                else:
                    cursor.execute("SELECT id FROM tenants WHERE subdomain = %s", 
                                 (user["tenant_subdomain"],))
                    tenant_id = cursor.fetchone()[0]
                
                # Create user
                password_hash = bcrypt.hashpw(
                    user["password"].encode('utf-8'), 
                    bcrypt.gensalt()
                ).decode('utf-8')
                
                cursor.execute("""
                    INSERT INTO users (tenant_id, email, password_hash, role, is_active, email_verified)
                    VALUES (%s, %s, %s, %s, true, true)
                    ON CONFLICT (tenant_id, email) DO NOTHING
                """, (tenant_id, user["email"], password_hash, user["role"]))
                
                logger.info(f"Created test user: {user['email']} for tenant: {user['tenant_name']}")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error creating test users: {e}")
            raise
    
    def setup_application(self):
        """Setup the SaaS application"""
        logger.info("Setting up application...")
        
        app_dir = "/opt/mobile-automation-saas"
        
        # Create directory
        self.run_command(f"mkdir -p {app_dir}")
        
        # Copy application files
        src_dir = Path(__file__).parent.parent
        self.run_command(f"cp -r {src_dir}/* {app_dir}/")
        
        # Create virtual environment
        venv_dir = f"{app_dir}/venv"
        self.run_command(f"python3 -m venv {venv_dir}")
        
        # Create requirements file if it doesn't exist
        requirements_file = f"{app_dir}/requirements.txt"
        if not os.path.exists(requirements_file):
            with open(requirements_file, 'w') as f:
                f.write("""Flask==2.3.3
Flask-JWT-Extended==4.5.3
Flask-SQLAlchemy==3.0.5
psycopg2-binary==2.9.7
bcrypt==4.0.1
websockets==11.0.3
requests==2.31.0
python-dotenv==1.0.0
""")
        
        # Install dependencies
        pip_cmd = f"{venv_dir}/bin/pip"
        self.run_command(f"{pip_cmd} install --upgrade pip")
        self.run_command(f"{pip_cmd} install -r {requirements_file}")
        
        # Create environment file
        env_file = f"{app_dir}/.env"
        with open(env_file, "w") as f:
            f.write(f"FLASK_SECRET_KEY={self.config['app']['secret_key']}\n")
            f.write(f"JWT_SECRET_KEY={self.config['app']['jwt_secret']}\n")
            f.write(f"DATABASE_URL=postgresql://{self.config['database']['user']}:{self.config['database']['password']}@{self.config['database']['host']}:{self.config['database']['port']}/{self.config['database']['name']}\n")
            f.write(f"ENVIRONMENT={self.config['environment']}\n")
            f.write(f"FLASK_DEBUG={str(self.config['app']['debug']).lower()}\n")
        
        logger.info("Application setup completed")
    
    def deploy(self):
        """Run full test deployment"""
        logger.info("Starting test deployment...")
        
        try:
            self.install_dependencies()
            self.setup_postgresql()
            self.initialize_schema()
            self.create_test_users()
            self.setup_application()
            
            logger.info("✓ Test deployment completed successfully!")
            logger.info("Test users created:")
            logger.info("  - <EMAIL> / testpass123 (Test Company 1)")
            logger.info("  - <EMAIL> / testpass456 (Test Company 2)")
            logger.info(f"Application installed at: /opt/mobile-automation-saas")
            logger.info("To start the application:")
            logger.info("  cd /opt/mobile-automation-saas")
            logger.info("  ./venv/bin/python -m saas_infrastructure.app.saas_app")
            
        except Exception as e:
            logger.error(f"Deployment failed: {e}")
            raise

if __name__ == "__main__":
    deployer = TestSaaSDeployer()
    deployer.deploy()
