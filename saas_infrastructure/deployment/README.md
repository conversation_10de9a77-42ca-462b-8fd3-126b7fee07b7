# Mobile Automation SaaS Platform - Deployment Guide

This guide covers the deployment of the Mobile Automation SaaS Platform, which transforms the existing local mobile automation tool into a multi-tenant cloud service.

## Architecture Overview

The platform uses a hybrid cloud architecture:
- **Cloud Component**: Multi-tenant web application hosted on cloud infrastructure
- **Local Component**: Device bridges that connect local iOS/Android devices to the cloud
- **Communication**: Secure WebSocket connections via Cloudflare tunnels

## Prerequisites

### Cloud Infrastructure
- Ubuntu 20.04+ server (recommended: Hetzner Cloud CX21 or equivalent)
- PostgreSQL database (managed service recommended)
- Domain name with Cloudflare DNS management
- SSL certificate (Let's Encrypt or Cloudflare)

### Local Requirements (for device bridges)
- macOS, Linux, or Windows machine with iOS/Android devices
- Python 3.8+
- iOS: Xcode Command Line Tools, libimobiledevice
- Android: Android SDK Platform Tools (ADB)

## Quick Start

### 1. Server Setup

```bash
# Clone the repository
git clone https://github.com/your-org/mobile-automation-saas.git
cd mobile-automation-saas/saas_infrastructure

# Run environment setup (as root)
chmod +x deployment/setup_environment.sh
sudo ./deployment/setup_environment.sh

# The script will:
# - Update system packages
# - Install required dependencies
# - Setup PostgreSQL
# - Configure firewall
# - Create application user and directories
# - Install monitoring tools
```

### 2. Configuration

```bash
# Copy and edit deployment configuration
cp deployment/deployment_config.example.json deployment/deployment_config.json
nano deployment/deployment_config.json
```

Key configuration items:
- Database connection details
- Cloudflare tunnel token
- Domain name
- SSL settings

### 3. Deploy Application

```bash
# Run automated deployment
sudo deploy-saas --step all

# Or run individual steps:
sudo deploy-saas --step secrets    # Generate secure secrets
sudo deploy-saas --step database   # Setup database
sudo deploy-saas --step app        # Deploy application
sudo deploy-saas --step nginx      # Configure reverse proxy
sudo deploy-saas --step supervisor # Setup process management
sudo deploy-saas --step tunnel     # Configure Cloudflare tunnel
```

### 4. Setup Device Bridges

On each machine with mobile devices:

```bash
# Download bridge installer
curl -O https://your-domain.com/bridge/install_bridge.py

# Run installer
python3 install_bridge.py

# Configure bridge
python3 bridge_config.py setup your-domain.com <EMAIL> password

# Start bridge service
systemctl --user start mobile-automation-bridge  # Linux
launchctl load ~/Library/LaunchAgents/com.mobileautomation.bridge.plist  # macOS
```

## Detailed Deployment Steps

### Database Setup

The platform uses PostgreSQL with Row Level Security (RLS) for multi-tenant isolation:

```sql
-- Key features:
-- - UUID primary keys for security
-- - Tenant isolation via RLS policies
-- - Partitioned tables for performance
-- - Audit logging
-- - Usage tracking for billing
```

### Application Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Device Bridge │────│  Cloudflare      │────│   SaaS App      │
│   (Local)       │    │  Tunnel          │    │   (Cloud)       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
        │                                               │
        │                                               │
   ┌────▼────┐                                     ┌────▼────┐
   │ iOS/    │                                     │ PostgreSQL │
   │ Android │                                     │ Database   │
   │ Devices │                                     └─────────┘
   └─────────┘
```

### Security Features

- **Multi-tenant isolation**: Row Level Security (RLS) in PostgreSQL
- **Authentication**: JWT tokens with tenant-scoped claims
- **Communication**: TLS-encrypted WebSocket connections
- **Rate limiting**: Nginx-based request throttling
- **Audit logging**: Comprehensive activity tracking
- **Input validation**: Strict parameter validation and sanitization

### Monitoring and Logging

The platform includes comprehensive monitoring:

```bash
# Application logs
tail -f /opt/mobile-automation-saas/logs/app.log

# Nginx logs
tail -f /var/log/nginx/saas_access.log

# System monitoring
htop
iotop
nethogs

# Application status
supervisorctl status mobile-automation-saas
systemctl status nginx
systemctl status postgresql
```

## Configuration Reference

### Environment Variables

```bash
# Application settings
FLASK_SECRET_KEY=your_secret_key
JWT_SECRET_KEY=your_jwt_secret
DATABASE_URL=postgresql://user:pass@host:port/db
REDIS_URL=redis://localhost:6379/0

# Cloudflare settings
CLOUDFLARE_TUNNEL_TOKEN=your_tunnel_token

# Environment
FLASK_ENV=production
LOG_LEVEL=INFO
```

### Database Configuration

```json
{
  "database": {
    "host": "localhost",
    "port": 5432,
    "name": "mobile_automation_saas",
    "user": "saas_user",
    "password": "generated_password",
    "ssl_mode": "require",
    "pool_size": 20,
    "max_overflow": 30
  }
}
```

### Cloudflare Tunnel Setup

1. Create tunnel in Cloudflare dashboard
2. Get tunnel token
3. Configure DNS records:
   ```
   Type: CNAME
   Name: your-domain.com
   Target: tunnel-id.cfargotunnel.com
   ```

## Scaling and Performance

### Horizontal Scaling

```bash
# Add more application instances
docker-compose up --scale saas_app=3

# Load balancer configuration
upstream saas_app {
    server app1:5000;
    server app2:5000;
    server app3:5000;
}
```

### Database Optimization

```sql
-- Partition large tables
CREATE TABLE test_executions_2024_01 PARTITION OF test_executions
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- Add indexes for performance
CREATE INDEX CONCURRENTLY idx_tenant_devices_tenant_id 
ON tenant_devices(tenant_id);
```

### Caching Strategy

- Redis for session storage
- Application-level caching for device lists
- CDN for static assets
- Database query result caching

## Backup and Recovery

### Automated Backups

```bash
# Database backup script (runs daily)
pg_dump -h localhost -U saas_user mobile_automation_saas > backup_$(date +%Y%m%d).sql

# Application data backup
tar -czf app_backup_$(date +%Y%m%d).tar.gz /opt/mobile-automation-saas/data
```

### Disaster Recovery

1. **Database Recovery**:
   ```bash
   psql -h localhost -U saas_user -d mobile_automation_saas < backup_20240101.sql
   ```

2. **Application Recovery**:
   ```bash
   tar -xzf app_backup_20240101.tar.gz -C /opt/mobile-automation-saas/
   supervisorctl restart mobile-automation-saas
   ```

## Troubleshooting

### Common Issues

1. **Bridge Connection Failed**:
   ```bash
   # Check tunnel status
   cloudflared tunnel info your-tunnel-name
   
   # Verify bridge token
   python3 bridge_config.py validate
   ```

2. **Database Connection Issues**:
   ```bash
   # Test database connection
   psql -h localhost -U saas_user -d mobile_automation_saas -c "SELECT 1;"
   
   # Check connection limits
   SELECT count(*) FROM pg_stat_activity;
   ```

3. **High Memory Usage**:
   ```bash
   # Monitor memory usage
   free -h
   
   # Restart application if needed
   supervisorctl restart mobile-automation-saas
   ```

### Log Analysis

```bash
# Application errors
grep ERROR /opt/mobile-automation-saas/logs/app.log

# Failed authentication attempts
grep "authentication failed" /var/log/nginx/saas_access.log

# Database slow queries
grep "slow query" /var/log/postgresql/postgresql.log
```

## Maintenance

### Regular Tasks

1. **Weekly**:
   - Review application logs
   - Check disk space usage
   - Verify backup integrity

2. **Monthly**:
   - Update system packages
   - Review security logs
   - Optimize database performance

3. **Quarterly**:
   - Security audit
   - Performance review
   - Capacity planning

### Updates and Patches

```bash
# Update application
cd /opt/mobile-automation-saas
git pull origin main
supervisorctl restart mobile-automation-saas

# Update system packages
apt update && apt upgrade -y
```

## Testing and Validation

After deployment, run the validation tests to ensure everything is working correctly:

```bash
# Run all validation tests
cd /opt/mobile-automation-saas
python3 tests/test_deployment.py --test all

# Run specific tests
python3 tests/test_deployment.py --test database
python3 tests/test_deployment.py --test auth
python3 tests/test_deployment.py --test websocket
```

### Test Coverage

The validation suite tests:
- Database connectivity and schema
- Application health endpoints
- Authentication and authorization
- API endpoint functionality
- WebSocket connectivity for device bridges
- Nginx configuration and security headers
- SSL certificate validity
- Cloudflare tunnel connectivity
- System resource usage

## Development and Local Testing

For local development, use Docker Compose:

```bash
# Start development environment
cd saas_infrastructure
docker-compose up -d

# View logs
docker-compose logs -f saas_app

# Run tests
docker-compose exec saas_app python -m pytest tests/

# Stop environment
docker-compose down
```

## Production Deployment Checklist

Before going live, ensure:

- [ ] Database backups are configured and tested
- [ ] SSL certificates are valid and auto-renewing
- [ ] Monitoring and alerting are set up
- [ ] Security headers are configured
- [ ] Rate limiting is enabled
- [ ] Log rotation is configured
- [ ] Firewall rules are properly set
- [ ] Cloudflare tunnel is stable
- [ ] All validation tests pass
- [ ] Performance testing completed
- [ ] Disaster recovery plan documented

## Support and Documentation

- **Application Logs**: `/opt/mobile-automation-saas/logs/`
- **Configuration**: `/opt/mobile-automation-saas/deployment/`
- **Database Schema**: `/opt/mobile-automation-saas/database/schema.sql`
- **API Documentation**: `https://your-domain.com/api/docs`
- **Validation Tests**: `/opt/mobile-automation-saas/tests/test_deployment.py`

For additional support, check the troubleshooting section or contact the development team.
