#!/bin/bash

# Mobile Automation SaaS Platform - Complete Deployment Package
# Run this script on the target server (*************) as root

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Check if running as root
if [[ $EUID -ne 0 ]]; then
    log_error "This script must be run as root"
    exit 1
fi

log_info "Starting Mobile Automation SaaS Platform deployment..."

# Pre-deployment checks
log_info "Performing pre-deployment checks..."

# Check if SSH key exists
if [ ! -f ~/.ssh/id_rsa ] && [ ! -f ~/.ssh/id_ed25519 ] && [ ! -f ~/.ssh/id_ecdsa ]; then
    log_warning "No SSH keys found in ~/.ssh/"
    log_warning "Please ensure SSH keys are configured for GitHub access"
    log_warning "You can test SSH access with: ssh -T **************"
fi

# Test basic connectivity to GitHub
log_info "Testing connectivity to GitHub..."
if ! ping -c 1 github.com >/dev/null 2>&1; then
    log_error "Cannot reach github.com - please check internet connectivity"
    exit 1
fi

log_success "Pre-deployment checks completed"

# Step 1: Update system and install dependencies
log_info "Step 1: Installing system dependencies..."
apt update && apt upgrade -y

packages=(
    "python3" "python3-pip" "python3-venv" "python3-dev"
    "postgresql" "postgresql-contrib" "postgresql-client"
    "nginx" "supervisor" "git" "curl" "wget" "unzip"
    "build-essential" "libpq-dev" "certbot" "python3-certbot-nginx"
)

apt install -y "${packages[@]}"
log_success "System dependencies installed"

# Step 2: Setup PostgreSQL
log_info "Step 2: Setting up PostgreSQL..."
systemctl start postgresql
systemctl enable postgresql

# Create database and user with proper permissions
log_info "Creating database and user..."
sudo -u postgres psql << 'EOF'
-- Create database
CREATE DATABASE mobile_automation_saas_test;

-- Create user
CREATE USER saas_test_user WITH PASSWORD 'test_password_123';

-- Grant database privileges
GRANT ALL PRIVILEGES ON DATABASE mobile_automation_saas_test TO saas_test_user;

-- Connect to the database to set schema permissions
\c mobile_automation_saas_test

-- Grant schema permissions (required for PostgreSQL 15+)
GRANT CREATE, USAGE ON SCHEMA public TO saas_test_user;
GRANT ALL PRIVILEGES ON SCHEMA public TO saas_test_user;

-- Grant default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO saas_test_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO saas_test_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO saas_test_user;

-- Make saas_test_user owner of the public schema
ALTER SCHEMA public OWNER TO saas_test_user;

\q
EOF

if [ $? -ne 0 ]; then
    log_error "Failed to create database and user"
    exit 1
fi

log_success "PostgreSQL database and user configured with proper permissions"

# Step 3: Setup SSH and clone repository
log_info "Step 3: Setting up SSH and cloning repository..."

# Add GitHub to known hosts to avoid SSH fingerprint prompt
log_info "Adding GitHub to SSH known hosts..."
mkdir -p ~/.ssh
ssh-keyscan -H github.com >> ~/.ssh/known_hosts 2>/dev/null

# Test SSH connection to GitHub
log_info "Testing SSH connection to GitHub..."
ssh -T ************** -o ConnectTimeout=10 -o StrictHostKeyChecking=no 2>&1 | grep -q "successfully authenticated"
if [ $? -ne 0 ]; then
    log_warning "SSH connection test to GitHub failed. Attempting connection anyway..."
    # Try a more permissive test
    timeout 10 ssh -T ************** -o StrictHostKeyChecking=no 2>/dev/null
    if [ $? -eq 124 ]; then
        log_error "SSH connection to GitHub timed out"
        log_error "Please verify that SSH keys are properly configured on this server"
        log_error "You can test manually with: ssh -T **************"
        exit 1
    fi
fi

log_success "SSH connection to GitHub verified"

# Setup application directory
cd /opt

# Remove existing directory if it exists
rm -rf mobile-automation-saas

# Clone repository using SSH
log_info "Cloning repository using SSH authentication..."
<NAME_EMAIL>:techietrends23/MobileApp-AutoTest.git mobile-automation-saas

if [ $? -ne 0 ]; then
    log_error "Failed to clone repository using SSH"
    log_error "Please ensure SSH keys are properly configured for GitHub access"
    log_error "Test SSH access manually: ssh -T **************"
    exit 1
fi

cd mobile-automation-saas
git checkout saas-implement

if [ $? -ne 0 ]; then
    log_error "Failed to checkout saas-implement branch"
    log_error "Please verify the branch exists in the repository"
    exit 1
fi

log_success "Repository cloned and branch checked out successfully"

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install Python dependencies
pip install --upgrade pip
pip install -r saas_infrastructure/app/requirements.txt

# Create environment file
cat > .env << 'EOF'
FLASK_SECRET_KEY=test_secret_key_for_development_only
JWT_SECRET_KEY=test_jwt_secret_for_development_only
DATABASE_URL=postgresql://saas_test_user:test_password_123@localhost:5432/mobile_automation_saas_test
ENVIRONMENT=test
FLASK_DEBUG=true
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=mobile_automation_saas_test
DATABASE_USER=saas_test_user
DATABASE_PASSWORD=test_password_123
EOF

log_success "Application setup completed"

# Step 4: Initialize database schema
log_info "Step 4: Initializing database schema..."

# First, create the schema as postgres superuser to ensure all permissions work
log_info "Creating database schema as postgres superuser..."
sudo -u postgres psql -d mobile_automation_saas_test -f saas_infrastructure/database/schema.sql

if [ $? -ne 0 ]; then
    log_error "Failed to initialize database schema"
    log_error "Attempting alternative approach with saas_test_user..."

    # Fallback: try with saas_test_user (should work with the permissions we set)
    psql postgresql://saas_test_user:test_password_123@localhost:5432/mobile_automation_saas_test \
         -f saas_infrastructure/database/schema.sql

    if [ $? -ne 0 ]; then
        log_error "Database schema initialization failed completely"
        exit 1
    fi
fi

# Grant ownership of all created objects to saas_test_user
log_info "Transferring ownership of database objects to saas_test_user..."
sudo -u postgres psql -d mobile_automation_saas_test << 'EOF'
-- Grant ownership of all tables to saas_test_user
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP
        EXECUTE 'ALTER TABLE ' || quote_ident(r.tablename) || ' OWNER TO saas_test_user';
    END LOOP;
END $$;

-- Grant ownership of all sequences to saas_test_user
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT sequence_name FROM information_schema.sequences WHERE sequence_schema = 'public') LOOP
        EXECUTE 'ALTER SEQUENCE ' || quote_ident(r.sequence_name) || ' OWNER TO saas_test_user';
    END LOOP;
END $$;

-- Grant all privileges on all tables and sequences
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO saas_test_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO saas_test_user;
EOF

log_success "Database schema initialized and ownership transferred"

# Step 5: Create test users
log_info "Step 5: Creating test users..."
python3 -c "
import sys
sys.path.append('/opt/mobile-automation-saas')
import psycopg2
import bcrypt

conn = psycopg2.connect(
    host='localhost',
    port=5432,
    database='mobile_automation_saas_test',
    user='saas_test_user',
    password='test_password_123'
)
conn.autocommit = True
cursor = conn.cursor()

# Create tenants
tenants = [
    ('Test Company 1', 'testcompany1'),
    ('Test Company 2', 'testcompany2')
]

for name, subdomain in tenants:
    cursor.execute('''
        INSERT INTO tenants (name, subdomain, subscription_tier, is_active)
        VALUES (%s, %s, 'starter', true)
        ON CONFLICT (subdomain) DO NOTHING
    ''', (name, subdomain))

# Create users
users = [
    ('<EMAIL>', 'testpass123', 'testcompany1', 'admin'),
    ('<EMAIL>', 'testpass456', 'testcompany2', 'admin')
]

for email, password, subdomain, role in users:
    # Get tenant ID
    cursor.execute('SELECT id FROM tenants WHERE subdomain = %s', (subdomain,))
    tenant_id = cursor.fetchone()[0]
    
    # Hash password
    password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    # Create user
    cursor.execute('''
        INSERT INTO users (tenant_id, email, password_hash, role, is_active, email_verified)
        VALUES (%s, %s, %s, %s, true, true)
        ON CONFLICT (tenant_id, email) DO NOTHING
    ''', (tenant_id, email, password_hash, role))

cursor.close()
conn.close()
print('Test users created successfully')
"

log_success "Test users created"

# Step 6: Setup Nginx
log_info "Step 6: Setting up Nginx..."
cat > /etc/nginx/sites-available/mobile-automation-saas << 'EOF'
server {
    listen 80;
    server_name *************;
    
    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /socket.io/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# Enable site
ln -sf /etc/nginx/sites-available/mobile-automation-saas /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Test and reload Nginx
nginx -t && systemctl reload nginx
log_success "Nginx configured"

# Step 7: Setup Supervisor
log_info "Step 7: Setting up Supervisor..."
# Create log files with proper permissions
touch /var/log/mobile-automation-saas.log
touch /var/log/mobile-automation-saas-access.log
touch /var/log/mobile-automation-saas-error.log
chown www-data:www-data /var/log/mobile-automation-saas*.log

# Create Supervisor configuration for production with Gunicorn
cat > /etc/supervisor/conf.d/mobile-automation-saas.conf << 'EOF'
[program:mobile-automation-saas]
command=/opt/mobile-automation-saas/venv/bin/gunicorn --config /opt/mobile-automation-saas/saas_infrastructure/app/gunicorn.conf.py saas_infrastructure.app.wsgi:application
directory=/opt/mobile-automation-saas
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/mobile-automation-saas.log
stderr_logfile=/var/log/mobile-automation-saas.log
environment=PATH="/opt/mobile-automation-saas/venv/bin",PYTHONPATH="/opt/mobile-automation-saas",ENVIRONMENT="test",FLASK_ENV="test",DATABASE_URL="postgresql://saas_test_user:test_password_123@localhost:5432/mobile_automation_saas_test",PORT="5000"
stopasgroup=true
killasgroup=true
EOF

# Create alternative configuration for development mode (fallback)
cat > /etc/supervisor/conf.d/mobile-automation-saas-dev.conf << 'EOF'
[program:mobile-automation-saas-dev]
command=/opt/mobile-automation-saas/venv/bin/python -m saas_infrastructure.app.saas_app
directory=/opt/mobile-automation-saas
user=www-data
autostart=false
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/mobile-automation-saas-dev.log
environment=PATH="/opt/mobile-automation-saas/venv/bin",PYTHONPATH="/opt/mobile-automation-saas",ENVIRONMENT="test",FLASK_ENV="test",DATABASE_URL="postgresql://saas_test_user:test_password_123@localhost:5432/mobile_automation_saas_test"
EOF

# Set permissions
chown -R www-data:www-data /opt/mobile-automation-saas
chmod +x /opt/mobile-automation-saas/venv/bin/python

# Reload Supervisor
supervisorctl reread
supervisorctl update
supervisorctl start mobile-automation-saas

log_success "Supervisor configured and application started"

# Step 8: Setup firewall
log_info "Step 8: Setting up firewall..."
ufw --force reset
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw --force enable

log_success "Firewall configured"

# Final status check
log_info "Checking deployment status..."
sleep 5

if supervisorctl status mobile-automation-saas | grep -q RUNNING; then
    log_success "Application is running!"
else
    log_warning "Application may not be running properly"
fi

if curl -s http://localhost:5000/health > /dev/null; then
    log_success "Health check passed!"
else
    log_warning "Health check failed"
fi

log_success "Deployment completed!"
echo
log_info "Access the application at: http://*************"
log_info "Test users:"
log_info "  - <EMAIL> / testpass123 (Test Company 1)"
log_info "  - <EMAIL> / testpass456 (Test Company 2)"
echo
log_info "Useful commands:"
log_info "  - Check status: supervisorctl status mobile-automation-saas"
log_info "  - View logs: tail -f /var/log/mobile-automation-saas.log"
log_info "  - Restart app: supervisorctl restart mobile-automation-saas"
echo
