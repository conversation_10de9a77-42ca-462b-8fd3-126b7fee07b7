#!/bin/bash

# Tenant Resolution Diagnostic and Fix Script
# Diagnoses and fixes tenant resolution issues in the Mobile Automation SaaS Platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${CYAN}[STEP]${NC} $1"; }

# Check if running as root
if [[ $EUID -ne 0 ]]; then
    log_error "This script must be run as root"
    exit 1
fi

echo "========================================================================"
echo "Mobile Automation SaaS Platform - Tenant Resolution Diagnostic"
echo "========================================================================"

# Step 1: Check database connectivity
log_step "Step 1: Checking database connectivity..."

DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="mobile_automation_saas_test"
DB_USER="saas_test_user"
DB_PASS="test_password_123"

# Test basic database connection
log_info "Testing PostgreSQL connection..."
if PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" >/dev/null 2>&1; then
    log_success "Database connection successful"
else
    log_error "Database connection failed"
    log_info "Checking PostgreSQL service status..."
    systemctl status postgresql || true
    exit 1
fi

# Step 2: Check tenant table structure and data
log_step "Step 2: Checking tenant table structure and data..."

log_info "Checking if tenants table exists..."
TENANT_TABLE_EXISTS=$(PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'tenants');" | tr -d ' ')

if [[ "$TENANT_TABLE_EXISTS" == "t" ]]; then
    log_success "Tenants table exists"
    
    # Check tenant table structure
    log_info "Checking tenant table structure..."
    PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "\d tenants"
    
    # Check existing tenant data
    log_info "Checking existing tenant data..."
    TENANT_COUNT=$(PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM tenants;" | tr -d ' ')
    log_info "Found $TENANT_COUNT tenants in database"
    
    if [[ "$TENANT_COUNT" -gt 0 ]]; then
        log_info "Existing tenants:"
        PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT id, name, subdomain, subscription_tier, created_at FROM tenants ORDER BY id;"
    fi
    
else
    log_warning "Tenants table does not exist - creating it..."
    
    # Create tenants table
    PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << 'EOF'
CREATE TABLE IF NOT EXISTS tenants (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    subdomain VARCHAR(100) UNIQUE NOT NULL,
    subscription_tier VARCHAR(50) DEFAULT 'basic',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Create index for faster subdomain lookups
CREATE INDEX IF NOT EXISTS idx_tenants_subdomain ON tenants(subdomain);
EOF
    
    log_success "Tenants table created"
fi

# Step 3: Ensure test tenants exist
log_step "Step 3: Ensuring test tenants exist..."

# Check if test tenants exist
TENANT1_EXISTS=$(PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT EXISTS (SELECT 1 FROM tenants WHERE subdomain = 'testcompany1');" | tr -d ' ')
TENANT2_EXISTS=$(PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT EXISTS (SELECT 1 FROM tenants WHERE subdomain = 'testcompany2');" | tr -d ' ')

if [[ "$TENANT1_EXISTS" != "t" ]]; then
    log_info "Creating test tenant 1 (testcompany1)..."
    PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        INSERT INTO tenants (name, subdomain, subscription_tier) 
        VALUES ('Test Company 1', 'testcompany1', 'basic')
        ON CONFLICT (subdomain) DO NOTHING;
    "
    log_success "Test tenant 1 created"
else
    log_success "Test tenant 1 already exists"
fi

if [[ "$TENANT2_EXISTS" != "t" ]]; then
    log_info "Creating test tenant 2 (testcompany2)..."
    PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        INSERT INTO tenants (name, subdomain, subscription_tier) 
        VALUES ('Test Company 2', 'testcompany2', 'premium')
        ON CONFLICT (subdomain) DO NOTHING;
    "
    log_success "Test tenant 2 created"
else
    log_success "Test tenant 2 already exists"
fi

# Display current tenants
log_info "Current test tenants:"
PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT id, name, subdomain, subscription_tier, created_at FROM tenants WHERE subdomain IN ('testcompany1', 'testcompany2') ORDER BY id;"

# Step 4: Check users table and test users
log_step "Step 4: Checking users table and test users..."

# Check if users table exists
USERS_TABLE_EXISTS=$(PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'users');" | tr -d ' ')

if [[ "$USERS_TABLE_EXISTS" == "t" ]]; then
    log_success "Users table exists"
    
    # Check test users
    log_info "Checking test users..."
    PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT u.id, u.email, u.is_active, t.name as tenant_name, t.subdomain 
        FROM users u 
        JOIN tenants t ON u.tenant_id = t.id 
        WHERE u.email IN ('<EMAIL>', '<EMAIL>')
        ORDER BY u.id;
    "
else
    log_warning "Users table does not exist"
fi

# Step 5: Check Row Level Security (RLS) policies
log_step "Step 5: Checking Row Level Security policies..."

log_info "Checking RLS status on tenants table..."
RLS_ENABLED=$(PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT relrowsecurity FROM pg_class WHERE relname = 'tenants';" | tr -d ' ')

if [[ "$RLS_ENABLED" == "t" ]]; then
    log_info "RLS is enabled on tenants table"
    
    # Check existing policies
    log_info "Checking existing RLS policies..."
    PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
        FROM pg_policies 
        WHERE tablename = 'tenants';
    "
else
    log_info "RLS is not enabled on tenants table (this is OK for testing)"
fi

# Step 6: Test direct database queries for tenant resolution
log_step "Step 6: Testing direct database queries for tenant resolution..."

log_info "Testing tenant lookup by subdomain..."
TENANT1_DATA=$(PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT id, name, subdomain, subscription_tier FROM tenants WHERE subdomain = 'testcompany1';" | head -1)
TENANT2_DATA=$(PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT id, name, subdomain, subscription_tier FROM tenants WHERE subdomain = 'testcompany2';" | head -1)

if [[ -n "$TENANT1_DATA" ]]; then
    log_success "Tenant 1 lookup successful: $TENANT1_DATA"
else
    log_error "Tenant 1 lookup failed"
fi

if [[ -n "$TENANT2_DATA" ]]; then
    log_success "Tenant 2 lookup successful: $TENANT2_DATA"
else
    log_error "Tenant 2 lookup failed"
fi

# Step 7: Check application configuration
log_step "Step 7: Checking application configuration..."

cd /opt/mobile-automation-saas

# Check if .env file exists and has correct database URL
if [[ -f "saas_infrastructure/app/.env" ]]; then
    log_success ".env file exists"
    log_info "Database configuration in .env:"
    grep -E "DATABASE_URL|FLASK_HOST|FLASK_PORT" saas_infrastructure/app/.env || log_warning "Database URL not found in .env"
else
    log_warning ".env file not found - creating it..."
    cat > saas_infrastructure/app/.env << 'EOF'
# Environment Configuration
ENVIRONMENT=test
FLASK_ENV=test
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
DEBUG=True

# Database Configuration
DATABASE_URL=postgresql://saas_test_user:test_password_123@localhost:5432/mobile_automation_saas_test

# Security Configuration
SECRET_KEY=test-secret-key-change-in-production
JWT_SECRET_KEY=jwt-test-secret-key-change-in-production

# Logging
LOG_LEVEL=INFO
EOF
    log_success ".env file created"
fi

# Step 8: Restart application with updated configuration
log_step "Step 8: Restarting application with updated configuration..."

log_info "Stopping application..."
supervisorctl stop mobile-automation-saas 2>/dev/null || true

log_info "Starting application..."
supervisorctl start mobile-automation-saas

# Wait for application to start
log_info "Waiting for application to start..."
sleep 10

# Check application status
APP_STATUS=$(supervisorctl status mobile-automation-saas)
if [[ "$APP_STATUS" == *"RUNNING"* ]]; then
    log_success "Application is running: $APP_STATUS"
else
    log_error "Application failed to start: $APP_STATUS"
    log_info "Checking logs..."
    tail -20 /var/log/mobile-automation-saas.log
fi

# Step 9: Test tenant resolution endpoints
log_step "Step 9: Testing tenant resolution endpoints..."

SERVER_IP=$(hostname -I | awk '{print $1}')
BASE_URL="http://$SERVER_IP:5000"

# Test tenant 1
log_info "Testing tenant 1 resolution..."
TENANT1_RESPONSE=$(curl -s -H "X-Tenant-Subdomain: testcompany1" "$BASE_URL/api/tenant/info" 2>/dev/null || echo "FAILED")
if echo "$TENANT1_RESPONSE" | grep -q "tenant"; then
    log_success "Tenant 1 resolution working"
    echo "Response: $TENANT1_RESPONSE"
else
    log_warning "Tenant 1 resolution failed: $TENANT1_RESPONSE"
fi

# Test tenant 2
log_info "Testing tenant 2 resolution..."
TENANT2_RESPONSE=$(curl -s -H "X-Tenant-Subdomain: testcompany2" "$BASE_URL/api/tenant/info" 2>/dev/null || echo "FAILED")
if echo "$TENANT2_RESPONSE" | grep -q "tenant"; then
    log_success "Tenant 2 resolution working"
    echo "Response: $TENANT2_RESPONSE"
else
    log_warning "Tenant 2 resolution failed: $TENANT2_RESPONSE"
fi

# Step 10: Test authentication with tenant context
log_step "Step 10: Testing authentication with tenant context..."

# Test user 1 authentication
log_info "Testing user 1 authentication..."
AUTH1_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/login" \
    -H "Content-Type: application/json" \
    -H "X-Tenant-Subdomain: testcompany1" \
    -d '{"email": "<EMAIL>", "password": "testpass123"}' 2>/dev/null || echo "FAILED")

if echo "$AUTH1_RESPONSE" | grep -q "access_token"; then
    log_success "User 1 authentication successful"
else
    log_warning "User 1 authentication failed: $AUTH1_RESPONSE"
fi

# Test user 2 authentication
log_info "Testing user 2 authentication..."
AUTH2_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/login" \
    -H "Content-Type: application/json" \
    -H "X-Tenant-Subdomain: testcompany2" \
    -d '{"email": "<EMAIL>", "password": "testpass456"}' 2>/dev/null || echo "FAILED")

if echo "$AUTH2_RESPONSE" | grep -q "access_token"; then
    log_success "User 2 authentication successful"
else
    log_warning "User 2 authentication failed: $AUTH2_RESPONSE"
fi

# Final summary
echo
echo "========================================================================"
log_success "TENANT RESOLUTION DIAGNOSTIC COMPLETED"
echo "========================================================================"

log_info "Database Status:"
log_info "  - PostgreSQL: Connected"
log_info "  - Tenants table: Exists with $(PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM tenants;" | tr -d ' ') tenants"
log_info "  - Test tenants: testcompany1 and testcompany2 available"

log_info "Application Status:"
supervisorctl status mobile-automation-saas

log_info "Test Commands:"
log_info "  curl -H \"X-Tenant-Subdomain: testcompany1\" $BASE_URL/api/tenant/info"
log_info "  curl -H \"X-Tenant-Subdomain: testcompany2\" $BASE_URL/api/tenant/info"

echo
log_success "Tenant resolution diagnostic and fixes completed!"
