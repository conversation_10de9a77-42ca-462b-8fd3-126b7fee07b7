#!/bin/bash

# Deployment Validation Script for Mobile Automation SaaS Platform
# This script validates that the deployment is working correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Configuration
APP_HOST="localhost"
APP_PORT="5000"
DB_NAME="mobile_automation_saas_test"
DB_USER="saas_test_user"
DB_PASSWORD="test_password_123"

log_info "Starting deployment validation for Mobile Automation SaaS Platform..."

# Test 1: Check if PostgreSQL is running and accessible
log_info "Test 1: Checking PostgreSQL database..."
if systemctl is-active --quiet postgresql; then
    log_success "PostgreSQL service is running"
else
    log_error "PostgreSQL service is not running"
    exit 1
fi

# Test database connection
if psql postgresql://$DB_USER:$DB_PASSWORD@localhost:5432/$DB_NAME -c "SELECT current_user, current_database();" >/dev/null 2>&1; then
    log_success "Database connection successful"
else
    log_error "Database connection failed"
    exit 1
fi

# Test 2: Check if tables exist
log_info "Test 2: Checking database schema..."
TABLE_COUNT=$(psql postgresql://$DB_USER:$DB_PASSWORD@localhost:5432/$DB_NAME -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" | tr -d ' ')

if [ "$TABLE_COUNT" -gt 0 ]; then
    log_success "Found $TABLE_COUNT tables in database"
else
    log_error "No tables found in database"
    exit 1
fi

# Test 3: Check if test users exist
log_info "Test 3: Checking test users..."
USER_COUNT=$(psql postgresql://$DB_USER:$DB_PASSWORD@localhost:5432/$DB_NAME -t -c "SELECT COUNT(*) FROM users;" | tr -d ' ')

if [ "$USER_COUNT" -gt 0 ]; then
    log_success "Found $USER_COUNT test users in database"
else
    log_error "No test users found in database"
    exit 1
fi

# Test 4: Check if Supervisor is managing the application
log_info "Test 4: Checking Supervisor process management..."
if supervisorctl status mobile-automation-saas | grep -q "RUNNING"; then
    log_success "Application is running under Supervisor"
elif supervisorctl status mobile-automation-saas-dev | grep -q "RUNNING"; then
    log_success "Application is running under Supervisor (dev mode)"
else
    log_warning "Application is not running under Supervisor"
    log_info "Attempting to start application..."
    supervisorctl start mobile-automation-saas || supervisorctl start mobile-automation-saas-dev
    sleep 5
fi

# Test 5: Check if application is responding
log_info "Test 5: Checking application health..."
for i in {1..30}; do
    if curl -s http://$APP_HOST:$APP_PORT/health >/dev/null 2>&1; then
        log_success "Application health check passed"
        break
    else
        if [ $i -eq 30 ]; then
            log_error "Application health check failed after 30 attempts"
            log_info "Checking application logs..."
            tail -20 /var/log/mobile-automation-saas.log
            exit 1
        fi
        log_info "Waiting for application to start... (attempt $i/30)"
        sleep 2
    fi
done

# Test 6: Test health endpoint response
log_info "Test 6: Testing health endpoint response..."
HEALTH_RESPONSE=$(curl -s http://$APP_HOST:$APP_PORT/health)
if echo "$HEALTH_RESPONSE" | grep -q "status.*healthy"; then
    log_success "Health endpoint returned healthy status"
else
    log_warning "Health endpoint response: $HEALTH_RESPONSE"
fi

# Test 7: Test authentication endpoint
log_info "Test 7: Testing authentication endpoint..."
AUTH_RESPONSE=$(curl -s -X POST http://$APP_HOST:$APP_PORT/api/auth/login \
    -H "Content-Type: application/json" \
    -H "X-Tenant-Subdomain: testcompany1" \
    -d '{"email": "<EMAIL>", "password": "testpass123"}')

if echo "$AUTH_RESPONSE" | grep -q "access_token"; then
    log_success "Authentication test passed"
else
    log_warning "Authentication test failed. Response: $AUTH_RESPONSE"
fi

# Test 8: Check Nginx configuration
log_info "Test 8: Checking Nginx configuration..."
if nginx -t >/dev/null 2>&1; then
    log_success "Nginx configuration is valid"
else
    log_warning "Nginx configuration has issues"
fi

if systemctl is-active --quiet nginx; then
    log_success "Nginx service is running"
else
    log_warning "Nginx service is not running"
fi

# Test 9: Check log files
log_info "Test 9: Checking log files..."
if [ -f /var/log/mobile-automation-saas.log ]; then
    LOG_SIZE=$(stat -c%s /var/log/mobile-automation-saas.log)
    log_success "Application log file exists (size: $LOG_SIZE bytes)"
    
    # Check for recent errors
    if tail -50 /var/log/mobile-automation-saas.log | grep -i error >/dev/null; then
        log_warning "Recent errors found in application log"
        log_info "Last 10 error lines:"
        tail -50 /var/log/mobile-automation-saas.log | grep -i error | tail -10
    else
        log_success "No recent errors in application log"
    fi
else
    log_warning "Application log file not found"
fi

# Test 10: Test multi-tenant functionality
log_info "Test 10: Testing multi-tenant functionality..."

# Test tenant 1
TENANT1_RESPONSE=$(curl -s http://$APP_HOST:$APP_PORT/api/tenant/info \
    -H "X-Tenant-Subdomain: testcompany1")

if echo "$TENANT1_RESPONSE" | grep -q "testcompany1"; then
    log_success "Tenant 1 (testcompany1) is accessible"
else
    log_warning "Tenant 1 test failed. Response: $TENANT1_RESPONSE"
fi

# Test tenant 2
TENANT2_RESPONSE=$(curl -s http://$APP_HOST:$APP_PORT/api/tenant/info \
    -H "X-Tenant-Subdomain: testcompany2")

if echo "$TENANT2_RESPONSE" | grep -q "testcompany2"; then
    log_success "Tenant 2 (testcompany2) is accessible"
else
    log_warning "Tenant 2 test failed. Response: $TENANT2_RESPONSE"
fi

# Summary
echo
log_info "=== DEPLOYMENT VALIDATION SUMMARY ==="
log_success "✓ PostgreSQL database is running and accessible"
log_success "✓ Database schema is properly initialized"
log_success "✓ Test users are created"
log_success "✓ Application is running under process management"
log_success "✓ Health endpoint is responding"
log_success "✓ Basic functionality is working"

echo
log_info "=== ACCESS INFORMATION ==="
log_info "Application URL: http://$(hostname -I | awk '{print $1}'):$APP_PORT"
log_info "Health Check: http://$(hostname -I | awk '{print $1}'):$APP_PORT/health"
echo
log_info "Test Users:"
log_info "  User 1: <EMAIL> / testpass123 (Tenant: testcompany1)"
log_info "  User 2: <EMAIL> / testpass456 (Tenant: testcompany2)"
echo
log_info "API Testing:"
log_info "  Use header: X-Tenant-Subdomain: testcompany1 (or testcompany2)"
log_info "  Login endpoint: POST /api/auth/login"
log_info "  Tenant info: GET /api/tenant/info"

echo
log_success "Deployment validation completed successfully!"
log_info "The Mobile Automation SaaS Platform is ready for testing."
