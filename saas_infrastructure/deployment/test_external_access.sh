#!/bin/bash

# External Connectivity Test Script for Mobile Automation SaaS Platform
# Run this script from any external machine to test accessibility

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Configuration
SERVER_IP="${1:-*************}"
SERVER_PORT="${2:-5000}"
BASE_URL="http://$SERVER_IP:$SERVER_PORT"

echo "========================================================================"
echo "Mobile Automation SaaS Platform - External Connectivity Test"
echo "========================================================================"
echo "Testing server: $BASE_URL"
echo

# Test 1: Basic connectivity
log_info "Test 1: Basic connectivity test..."
if curl -s --connect-timeout 10 "$BASE_URL" >/dev/null 2>&1; then
    log_success "Basic connectivity successful"
else
    log_error "Basic connectivity failed - server may not be accessible"
    exit 1
fi

# Test 2: Health endpoint
log_info "Test 2: Health endpoint test..."
HEALTH_RESPONSE=$(curl -s --connect-timeout 10 "$BASE_URL/health" 2>/dev/null)
if echo "$HEALTH_RESPONSE" | grep -q "healthy"; then
    log_success "Health endpoint working: $HEALTH_RESPONSE"
else
    log_error "Health endpoint failed: $HEALTH_RESPONSE"
fi

# Test 3: API root endpoint
log_info "Test 3: API root endpoint test..."
API_RESPONSE=$(curl -s --connect-timeout 10 "$BASE_URL/api/" 2>/dev/null)
if echo "$API_RESPONSE" | grep -q "Mobile Automation SaaS API"; then
    log_success "API root endpoint working"
    echo "API Info: $(echo "$API_RESPONSE" | jq -r '.message' 2>/dev/null || echo "$API_RESPONSE")"
else
    log_warning "API root endpoint issue: $API_RESPONSE"
fi

# Test 4: Tenant info endpoint (without authentication)
log_info "Test 4: Tenant info endpoint test..."
TENANT_RESPONSE=$(curl -s --connect-timeout 10 \
    -H "X-Tenant-Subdomain: testcompany1" \
    "$BASE_URL/api/tenant/info" 2>/dev/null)
if echo "$TENANT_RESPONSE" | grep -q "tenant"; then
    log_success "Tenant info endpoint working"
else
    log_warning "Tenant info endpoint: $TENANT_RESPONSE"
fi

# Test 5: Authentication endpoint for test user 1
log_info "Test 5: Authentication <NAME_EMAIL>..."
AUTH1_RESPONSE=$(curl -s --connect-timeout 10 -X POST "$BASE_URL/api/auth/login" \
    -H "Content-Type: application/json" \
    -H "X-Tenant-Subdomain: testcompany1" \
    -d '{"email": "<EMAIL>", "password": "testpass123"}' 2>/dev/null)

if echo "$AUTH1_RESPONSE" | grep -q "access_token"; then
    log_success "Test user 1 authentication successful"
    TOKEN1=$(echo "$AUTH1_RESPONSE" | jq -r '.access_token' 2>/dev/null)
    echo "  Token received: ${TOKEN1:0:50}..."
else
    log_warning "Test user 1 authentication failed: $AUTH1_RESPONSE"
fi

# Test 6: Authentication endpoint for test user 2
log_info "Test 6: Authentication <NAME_EMAIL>..."
AUTH2_RESPONSE=$(curl -s --connect-timeout 10 -X POST "$BASE_URL/api/auth/login" \
    -H "Content-Type: application/json" \
    -H "X-Tenant-Subdomain: testcompany2" \
    -d '{"email": "<EMAIL>", "password": "testpass456"}' 2>/dev/null)

if echo "$AUTH2_RESPONSE" | grep -q "access_token"; then
    log_success "Test user 2 authentication successful"
    TOKEN2=$(echo "$AUTH2_RESPONSE" | jq -r '.access_token' 2>/dev/null)
    echo "  Token received: ${TOKEN2:0:50}..."
else
    log_warning "Test user 2 authentication failed: $AUTH2_RESPONSE"
fi

# Test 7: Authenticated API call (if we have a token)
if [ ! -z "$TOKEN1" ]; then
    log_info "Test 7: Authenticated API call test..."
    DEVICES_RESPONSE=$(curl -s --connect-timeout 10 \
        -H "Authorization: Bearer $TOKEN1" \
        -H "X-Tenant-Subdomain: testcompany1" \
        "$BASE_URL/api/devices" 2>/dev/null)
    
    if echo "$DEVICES_RESPONSE" | grep -q "devices"; then
        log_success "Authenticated devices endpoint working"
    else
        log_warning "Authenticated devices endpoint: $DEVICES_RESPONSE"
    fi
fi

# Test 8: Cross-tenant isolation test
if [ ! -z "$TOKEN1" ] && [ ! -z "$TOKEN2" ]; then
    log_info "Test 8: Cross-tenant isolation test..."
    
    # Try to use tenant 1 token with tenant 2 subdomain
    ISOLATION_RESPONSE=$(curl -s --connect-timeout 10 \
        -H "Authorization: Bearer $TOKEN1" \
        -H "X-Tenant-Subdomain: testcompany2" \
        "$BASE_URL/api/devices" 2>/dev/null)
    
    if echo "$ISOLATION_RESPONSE" | grep -q "error\|unauthorized\|forbidden"; then
        log_success "Tenant isolation working correctly"
    else
        log_warning "Tenant isolation may have issues: $ISOLATION_RESPONSE"
    fi
fi

# Test 9: Port accessibility test
log_info "Test 9: Port accessibility test..."
if nc -z -w5 "$SERVER_IP" "$SERVER_PORT" 2>/dev/null; then
    log_success "Port $SERVER_PORT is accessible on $SERVER_IP"
else
    log_warning "Port $SERVER_PORT may not be accessible (netcat test failed)"
fi

# Summary
echo
echo "========================================================================"
log_info "EXTERNAL CONNECTIVITY TEST SUMMARY"
echo "========================================================================"
echo "Server: $BASE_URL"
echo "All tests completed. Check the results above for any issues."
echo
echo "If tests are failing:"
echo "1. Ensure the server is running: ssh root@$SERVER_IP 'supervisorctl status mobile-automation-saas'"
echo "2. Check firewall settings: ssh root@$SERVER_IP 'ufw status'"
echo "3. Verify port binding: ssh root@$SERVER_IP 'netstat -tlnp | grep :$SERVER_PORT'"
echo "4. Run the diagnostic script on the server:"
echo "   ssh root@$SERVER_IP 'cd /opt/mobile-automation-saas && ./saas_infrastructure/deployment/diagnose_and_fix_accessibility.sh'"
echo
echo "For successful deployment, all tests should pass."
