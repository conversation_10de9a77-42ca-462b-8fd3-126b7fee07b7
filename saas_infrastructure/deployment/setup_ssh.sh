#!/bin/bash

# SSH Setup Script for GitHub Access
# Run this script before the main deployment if SSH keys are not configured

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

log_info "Setting up SSH access for GitHub..."

# Check if running as root
if [[ $EUID -ne 0 ]]; then
    log_error "This script should be run as root"
    exit 1
fi

# Create .ssh directory if it doesn't exist
mkdir -p ~/.ssh
chmod 700 ~/.ssh

# Check if SSH keys already exist
if [ -f ~/.ssh/id_ed25519 ] || [ -f ~/.ssh/id_rsa ] || [ -f ~/.ssh/id_ecdsa ]; then
    log_info "SSH keys already exist:"
    ls -la ~/.ssh/id_*
    
    # Test existing SSH connection
    log_info "Testing existing SSH connection to GitHub..."
    if ssh -T ************** -o ConnectTimeout=10 -o StrictHostKeyChecking=no 2>&1 | grep -q "successfully authenticated"; then
        log_success "SSH connection to GitHub is working!"
        exit 0
    else
        log_warning "Existing SSH keys don't seem to work with GitHub"
        log_warning "You may need to add the public key to your GitHub account"
    fi
else
    log_info "No SSH keys found. Generating new SSH key..."
    
    # Prompt for email
    read -p "Enter your email address for the SSH key: " email
    if [ -z "$email" ]; then
        email="deployment@$(hostname)"
        log_info "Using default email: $email"
    fi
    
    # Generate SSH key
    ssh-keygen -t ed25519 -C "$email" -f ~/.ssh/id_ed25519 -N ""
    
    if [ $? -eq 0 ]; then
        log_success "SSH key generated successfully"
    else
        log_error "Failed to generate SSH key"
        exit 1
    fi
fi

# Add GitHub to known hosts
log_info "Adding GitHub to SSH known hosts..."
ssh-keyscan -H github.com >> ~/.ssh/known_hosts 2>/dev/null

# Start SSH agent and add key
log_info "Starting SSH agent and adding key..."
eval "$(ssh-agent -s)"

# Add all available private keys
for key in ~/.ssh/id_ed25519 ~/.ssh/id_rsa ~/.ssh/id_ecdsa; do
    if [ -f "$key" ]; then
        ssh-add "$key" 2>/dev/null || true
    fi
done

# Display public key for GitHub setup
log_info "Your SSH public key(s) for GitHub:"
echo "----------------------------------------"
for pubkey in ~/.ssh/id_ed25519.pub ~/.ssh/id_rsa.pub ~/.ssh/id_ecdsa.pub; do
    if [ -f "$pubkey" ]; then
        echo "File: $pubkey"
        cat "$pubkey"
        echo
    fi
done
echo "----------------------------------------"

log_warning "IMPORTANT: Add the above public key(s) to your GitHub account:"
log_warning "1. Go to GitHub.com > Settings > SSH and GPG keys"
log_warning "2. Click 'New SSH key'"
log_warning "3. Paste the public key content"
log_warning "4. Save the key"
echo

# Test SSH connection
log_info "Testing SSH connection to GitHub..."
log_info "This may prompt you to verify GitHub's fingerprint - type 'yes' if prompted"

# Try SSH connection with timeout
timeout 30 ssh -T ************** -o StrictHostKeyChecking=no 2>&1 | tee /tmp/ssh_test.log

if grep -q "successfully authenticated" /tmp/ssh_test.log; then
    log_success "SSH connection to GitHub is working!"
    log_success "You can now run the deployment script"
elif grep -q "Permission denied" /tmp/ssh_test.log; then
    log_error "SSH connection failed - Permission denied"
    log_error "Please ensure you've added the public key to your GitHub account"
    log_error "Then test manually with: ssh -T **************"
    exit 1
else
    log_warning "SSH connection test was inconclusive"
    log_warning "Please test manually with: ssh -T **************"
    log_warning "You should see a message like 'Hi username! You've successfully authenticated...'"
fi

# Clean up
rm -f /tmp/ssh_test.log

log_info "SSH setup completed!"
log_info "If the connection test failed, please:"
log_info "1. Add the public key shown above to your GitHub account"
log_info "2. Test with: ssh -T **************"
log_info "3. Run the deployment script once SSH is working"
