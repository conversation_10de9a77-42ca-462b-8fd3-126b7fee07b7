# Mobile Automation SaaS Platform - Test Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the Mobile Automation SaaS Platform to a test environment on server *************.

## Prerequisites

- Server: Ubuntu 20.04+ or Debian 11+
- Root access to the server
- Internet connectivity
- At least 2GB RAM and 20GB disk space
- **SSH keys configured for GitHub access** (required for repository cloning)

### SSH Key Setup (Required)

Before running the deployment, ensure SSH keys are configured on the server:

1. **Check if SSH keys exist:**
   ```bash
   ls -la ~/.ssh/
   ```

2. **If no SSH keys exist, generate them:**
   ```bash
   ssh-keygen -t ed25519 -C "<EMAIL>"
   # Or for older systems:
   ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
   ```

3. **Add the public key to your GitHub account:**
   ```bash
   cat ~/.ssh/id_ed25519.pub
   # Copy the output and add it to GitHub Settings > SSH and GPG keys
   ```

4. **Test SSH connection to GitHub:**
   ```bash
   ssh -T **************
   # Should return: "Hi username! You've successfully authenticated..."
   ```

## Quick Deployment (Automated)

### Option 1: Using the Complete Deployment Package (RECOMMENDED)

1. **Connect to the server:**
   ```bash
   ssh root@*************
   ```

2. **Download and run the deployment package:**
   ```bash
   wget -O deploy_package.sh https://raw.githubusercontent.com/techietrends23/MobileApp-AutoTest/saas-implement/saas_infrastructure/deployment/deploy_package.sh
   chmod +x deploy_package.sh
   ./deploy_package.sh
   ```

   This single script will:
   - Install all system dependencies
   - Setup PostgreSQL database
   - Clone the repository and setup the application
   - Create test users and tenants
   - Configure Nginx and Supervisor
   - Start the application automatically

### Option 2: Using the Test Deployment Script

1. **Connect to the server and clone repository:**
   ```bash
   ssh root@*************

   # Add GitHub to known hosts and test SSH connection
   ssh-keyscan -H github.com >> ~/.ssh/known_hosts
   ssh -T **************

   # Clone repository using SSH
   cd /opt
   <NAME_EMAIL>:techietrends23/MobileApp-AutoTest.git mobile-automation-saas
   cd mobile-automation-saas
   git checkout saas-implement
   ```

2. **Run the test deployment script:**
   ```bash
   cd saas_infrastructure/deployment
   python3 deploy_test.py
   ```

3. **Start the application:**
   ```bash
   cd /opt/mobile-automation-saas
   ./saas_infrastructure/start_saas.sh
   ```

### Option 2: Using the Environment Setup Script

1. **Connect to the server and clone repository (same as above)**

2. **Run environment setup:**
   ```bash
   cd saas_infrastructure/deployment
   chmod +x setup_environment.sh
   ./setup_environment.sh
   ```

3. **Run deployment:**
   ```bash
   python3 deploy.py --config test_deployment_config.json
   ```

## Manual Deployment (Fallback)

If automated deployment fails, follow these manual steps:

### Step 1: System Preparation

```bash
# Update system
apt update && apt upgrade -y

# Install required packages
apt install -y python3 python3-pip python3-venv python3-dev \
               postgresql postgresql-contrib postgresql-client \
               nginx supervisor git curl wget build-essential libpq-dev

# Start services
systemctl start postgresql
systemctl enable postgresql
systemctl start nginx
systemctl enable nginx
```

### Step 2: Database Setup

```bash
# Switch to postgres user
sudo -u postgres psql

# In PostgreSQL shell:
CREATE DATABASE mobile_automation_saas_test;
CREATE USER saas_test_user WITH PASSWORD 'test_password_123';
GRANT ALL PRIVILEGES ON DATABASE mobile_automation_saas_test TO saas_test_user;

-- Connect to the database to set schema permissions
\c mobile_automation_saas_test

-- Grant schema permissions (required for PostgreSQL 15+)
GRANT CREATE, USAGE ON SCHEMA public TO saas_test_user;
GRANT ALL PRIVILEGES ON SCHEMA public TO saas_test_user;

-- Grant default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO saas_test_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO saas_test_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO saas_test_user;

-- Make saas_test_user owner of the public schema
ALTER SCHEMA public OWNER TO saas_test_user;

\q
```

### Step 3: Application Setup

```bash
# Create application directory
mkdir -p /opt/mobile-automation-saas
cd /opt/mobile-automation-saas

# Add GitHub to known hosts
ssh-keyscan -H github.com >> ~/.ssh/known_hosts

# Test SSH connection to GitHub
ssh -T **************

# Clone repository using SSH (if not done already)
<NAME_EMAIL>:techietrends23/MobileApp-AutoTest.git .
git checkout saas-implement

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install Python dependencies
pip install --upgrade pip
pip install Flask==2.3.3 Flask-JWT-Extended==4.5.3 Flask-SQLAlchemy==3.0.5 \
            psycopg2-binary==2.9.7 bcrypt==4.0.1 websockets==11.0.3 \
            requests==2.31.0 python-dotenv==1.0.0

# Create environment file
cat > .env << 'EOF'
FLASK_SECRET_KEY=test_secret_key_for_development_only
JWT_SECRET_KEY=test_jwt_secret_for_development_only
DATABASE_URL=postgresql://saas_test_user:test_password_123@localhost:5432/mobile_automation_saas_test
ENVIRONMENT=test
FLASK_DEBUG=true
EOF
```

### Step 4: Database Schema Initialization

```bash
# Initialize database schema as postgres superuser (recommended)
cd /opt/mobile-automation-saas
sudo -u postgres psql -d mobile_automation_saas_test -f saas_infrastructure/database/schema.sql

# Transfer ownership to saas_test_user
sudo -u postgres psql -d mobile_automation_saas_test << 'EOF'
-- Grant ownership of all tables to saas_test_user
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP
        EXECUTE 'ALTER TABLE ' || quote_ident(r.tablename) || ' OWNER TO saas_test_user';
    END LOOP;
END $$;

-- Grant ownership of all sequences to saas_test_user
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT sequence_name FROM information_schema.sequences WHERE sequence_schema = 'public') LOOP
        EXECUTE 'ALTER SEQUENCE ' || quote_ident(r.sequence_name) || ' OWNER TO saas_test_user';
    END LOOP;
END $$;

-- Grant all privileges on all tables and sequences
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO saas_test_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO saas_test_user;
EOF
```

**Alternative (if the above fails):**
```bash
# Try with saas_test_user directly
psql postgresql://saas_test_user:test_password_123@localhost:5432/mobile_automation_saas_test \
     -f saas_infrastructure/database/schema.sql
```

### Step 5: Create Test Users

```bash
# Connect to database and create test data
psql postgresql://saas_test_user:test_password_123@localhost:5432/mobile_automation_saas_test

# In PostgreSQL shell, run:
INSERT INTO tenants (name, subdomain, subscription_tier, is_active) 
VALUES ('Test Company 1', 'testcompany1', 'starter', true);

INSERT INTO tenants (name, subdomain, subscription_tier, is_active) 
VALUES ('Test Company 2', 'testcompany2', 'starter', true);

# Get tenant IDs
SELECT id, name FROM tenants;

# Create users (replace TENANT_ID_1 and TENANT_ID_2 with actual IDs)
INSERT INTO users (tenant_id, email, password_hash, role, is_active, email_verified)
VALUES ('TENANT_ID_1', '<EMAIL>', '$2b$12$hash_for_testpass123', 'admin', true, true);

INSERT INTO users (tenant_id, email, password_hash, role, is_active, email_verified)
VALUES ('TENANT_ID_2', '<EMAIL>', '$2b$12$hash_for_testpass456', 'admin', true, true);

\q
```

### Step 6: Start the Application

```bash
cd /opt/mobile-automation-saas
source venv/bin/activate
python -m saas_infrastructure.app.saas_app
```

## Testing the Deployment

### 1. Check Application Status

```bash
# Check if application is running
curl http://localhost:5000/health

# Check Supervisor status
supervisorctl status mobile-automation-saas

# Check database connection
psql postgresql://saas_test_user:test_password_123@localhost:5432/mobile_automation_saas_test -c "SELECT COUNT(*) FROM tenants;"
```

### 2. Access the Application

The application will be available at:
- **Direct access:** `http://*************:5000`
- **Through Nginx:** `http://*************` (port 80)

### 3. Test User Login

Use these test accounts to validate multi-tenant functionality:

- **Test User 1 (Test Company 1):**
  - Email: `<EMAIL>`
  - Password: `testpass123`
  - Tenant Subdomain: `testcompany1`
  - Access URL: `http://testcompany1.*************` (if DNS configured) or use header `X-Tenant-Subdomain: testcompany1`

- **Test User 2 (Test Company 2):**
  - Email: `<EMAIL>`
  - Password: `testpass456`
  - Tenant Subdomain: `testcompany2`
  - Access URL: `http://testcompany2.*************` (if DNS configured) or use header `X-Tenant-Subdomain: testcompany2`

### 4. Test Multi-Tenant Isolation

1. **Login as Test User 1:**
   - Access the application with tenant header or subdomain
   - <NAME_EMAIL> credentials
   - Note the tenant-specific dashboard and device URLs

2. **Login as Test User 2:**
   - Access with different tenant context
   - <NAME_EMAIL> credentials
   - Verify completely separate tenant data and interface

3. **Verify Isolation:**
   - Each tenant should see only their own devices, tests, and data
   - URLs should be tenant-specific
   - No cross-tenant data leakage

### 5. Test API Endpoints

```bash
# Health check
curl http://*************:5000/health

# Login API (replace with actual tenant subdomain)
curl -X POST http://*************:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Subdomain: testcompany1" \
  -d '{"email": "<EMAIL>", "password": "testpass123"}'

# Get tenant devices (with JWT token from login)
curl http://*************:5000/api/devices \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-Tenant-Subdomain: testcompany1"
```

## Troubleshooting

### Common Issues

1. **SSH Authentication Failed:**
   ```bash
   # Test SSH connection to GitHub
   ssh -T **************

   # Check if SSH keys exist
   ls -la ~/.ssh/

   # Generate new SSH key if needed
   ssh-keygen -t ed25519 -C "<EMAIL>"

   # Add public key to GitHub account
   cat ~/.ssh/id_ed25519.pub

   # Add GitHub to known hosts
   ssh-keyscan -H github.com >> ~/.ssh/known_hosts
   ```

2. **Git Clone Failed:**
   ```bash
   # Verify SSH connection works
   ssh -T **************

   # Try cloning manually
   <NAME_EMAIL>:techietrends23/MobileApp-AutoTest.git

   # Check SSH agent
   eval "$(ssh-agent -s)"
   ssh-add ~/.ssh/id_ed25519
   ```

3. **PostgreSQL Permission Errors:**

   **Quick Fix (Recommended):**
   ```bash
   # Use the automated fix script
   wget -O fix_postgresql_permissions.sh https://raw.githubusercontent.com/techietrends23/MobileApp-AutoTest/saas-implement/saas_infrastructure/deployment/fix_postgresql_permissions.sh
   chmod +x fix_postgresql_permissions.sh
   ./fix_postgresql_permissions.sh
   ```

   **Manual Fix:**
   ```bash
   # Check PostgreSQL version (newer versions have stricter permissions)
   sudo -u postgres psql -c "SELECT version();"

   # Fix schema permissions manually
   sudo -u postgres psql -d mobile_automation_saas_test << 'EOF'
   GRANT CREATE, USAGE ON SCHEMA public TO saas_test_user;
   GRANT ALL PRIVILEGES ON SCHEMA public TO saas_test_user;
   ALTER SCHEMA public OWNER TO saas_test_user;
   EOF

   # Check current permissions
   sudo -u postgres psql -d mobile_automation_saas_test -c "\dp"
   ```

4. **Database Connection Failed:**
   ```bash
   # Check PostgreSQL status
   systemctl status postgresql

   # Check if database exists
   sudo -u postgres psql -l | grep mobile_automation

   # Test connection as saas_test_user
   psql postgresql://saas_test_user:test_password_123@localhost:5432/mobile_automation_saas_test -c "SELECT current_user;"
   ```

5. **Schema Creation Failed:**
   ```bash
   # Check if tables were created
   sudo -u postgres psql -d mobile_automation_saas_test -c "\dt"

   # Manually create schema if needed
   sudo -u postgres psql -d mobile_automation_saas_test -f saas_infrastructure/database/schema.sql

   # Fix ownership after manual creation
   sudo -u postgres psql -d mobile_automation_saas_test << 'EOF'
   GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO saas_test_user;
   GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO saas_test_user;
   EOF
   ```

4. **Flask-SocketIO Production Server Error:**

   **Quick Fix (Recommended):**
   ```bash
   # Use the automated fix script
   wget -O fix_production_server.sh https://raw.githubusercontent.com/techietrends23/MobileApp-AutoTest/saas-implement/saas_infrastructure/deployment/fix_production_server.sh
   chmod +x fix_production_server.sh
   ./fix_production_server.sh
   ```

   **Manual Fix:**
   ```bash
   # Stop current application
   supervisorctl stop mobile-automation-saas

   # Update application code
   cd /opt/mobile-automation-saas
   git pull origin saas-implement

   # Update Supervisor to use Gunicorn
   supervisorctl reread
   supervisorctl update
   supervisorctl start mobile-automation-saas
   ```

5. **Application Won't Start:**
   ```bash
   # Check Python dependencies
   cd /opt/mobile-automation-saas
   source venv/bin/activate
   pip list

   # Check environment variables
   cat .env

   # Check application logs
   tail -50 /var/log/mobile-automation-saas.log
   ```

6. **Permission Errors:**
   ```bash
   # Fix ownership
   chown -R www-data:www-data /opt/mobile-automation-saas
   chmod +x /opt/mobile-automation-saas/venv/bin/python
   ```

### Log Files

- Application logs: `/opt/mobile-automation-saas/logs/app.log`
- PostgreSQL logs: `/var/log/postgresql/`
- Nginx logs: `/var/log/nginx/`

## External Accessibility Issues

If the application is running but not accessible externally:

### Quick Fix (Recommended)
```bash
# Run the comprehensive diagnostic and fix script
cd /opt/mobile-automation-saas
./saas_infrastructure/deployment/diagnose_and_fix_accessibility.sh
```

### Manual Diagnosis and Fix

1. **Check Application Binding:**
   ```bash
   # Check what interfaces the app is binding to
   netstat -tlnp | grep :5000

   # Should show 0.0.0.0:5000, not 127.0.0.1:5000
   ```

2. **Fix Binding Configuration:**
   ```bash
   # Update environment variables
   echo "FLASK_HOST=0.0.0.0" >> /opt/mobile-automation-saas/saas_infrastructure/app/.env
   echo "FLASK_PORT=5000" >> /opt/mobile-automation-saas/saas_infrastructure/app/.env

   # Restart application
   supervisorctl restart mobile-automation-saas
   ```

3. **Configure Firewall:**
   ```bash
   # Allow port 5000 through UFW
   ufw allow 5000/tcp
   ufw --force enable

   # Configure iptables as backup
   iptables -I INPUT -p tcp --dport 5000 -j ACCEPT
   iptables-save > /etc/iptables/rules.v4
   ```

4. **Test External Access:**
   ```bash
   # From external machine, test connectivity
   curl http://*************:5000/health

   # Or use the provided test script
   ./saas_infrastructure/deployment/test_external_access.sh *************
   ```

### Validation Commands

```bash
# Check application status
supervisorctl status mobile-automation-saas

# Check port binding
netstat -tlnp | grep :5000

# Check firewall status
ufw status
iptables -L INPUT -n | grep 5000

# Test local connectivity
curl http://localhost:5000/health

# Test external connectivity (from another machine)
curl http://*************:5000/health
```

## Multi-Tenant Testing

After resolving accessibility issues, test multi-tenant functionality:

### Test Users
- **Tenant 1:** <EMAIL> / testpass123 (testcompany1)
- **Tenant 2:** <EMAIL> / testpass456 (testcompany2)

### Authentication Test
```bash
# Test tenant 1 authentication
curl -X POST http://*************:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Subdomain: testcompany1" \
  -d '{"email": "<EMAIL>", "password": "testpass123"}'

# Test tenant 2 authentication
curl -X POST http://*************:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Subdomain: testcompany2" \
  -d '{"email": "<EMAIL>", "password": "testpass456"}'
```

### API Endpoints Test
```bash
# Health check
curl http://*************:5000/health

# API root
curl http://*************:5000/api/

# Tenant info
curl -H "X-Tenant-Subdomain: testcompany1" http://*************:5000/api/tenant/info
```

## Next Steps

After successful deployment and external accessibility:

1. Test device bridge connectivity
2. Integrate existing iOS/Android dashboards
3. Validate multi-tenant functionality
4. Test user authentication and authorization
5. Configure SSL/TLS for production use
6. Set up monitoring and logging
7. Configure backup and disaster recovery
5. Verify dashboard isolation between tenants

## Security Notes

This is a test deployment with relaxed security settings:
- Debug mode enabled
- Simple passwords
- No SSL/TLS encryption
- No firewall restrictions

For production deployment, implement proper security measures.
