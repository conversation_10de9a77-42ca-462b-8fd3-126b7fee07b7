#!/bin/bash

# PostgreSQL Permissions Fix Script
# Run this script if you encounter PostgreSQL permission errors during deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Check if running as root
if [[ $EUID -ne 0 ]]; then
    log_error "This script must be run as root"
    exit 1
fi

log_info "Fixing PostgreSQL permissions for SaaS deployment..."

# Database configuration
DB_NAME="mobile_automation_saas_test"
DB_USER="saas_test_user"
DB_PASSWORD="test_password_123"

# Check if PostgreSQL is running
if ! systemctl is-active --quiet postgresql; then
    log_info "Starting PostgreSQL service..."
    systemctl start postgresql
    systemctl enable postgresql
fi

# Check if database exists
log_info "Checking if database exists..."
if ! sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME"; then
    log_info "Database $DB_NAME does not exist. Creating it..."
    
    sudo -u postgres psql << EOF
CREATE DATABASE $DB_NAME;
CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';
GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;
EOF
    
    log_success "Database and user created"
fi

# Fix schema permissions
log_info "Fixing schema permissions..."
sudo -u postgres psql -d "$DB_NAME" << 'EOF'
-- Grant schema permissions (required for PostgreSQL 15+)
GRANT CREATE, USAGE ON SCHEMA public TO saas_test_user;
GRANT ALL PRIVILEGES ON SCHEMA public TO saas_test_user;

-- Grant default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO saas_test_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO saas_test_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO saas_test_user;

-- Make saas_test_user owner of the public schema
ALTER SCHEMA public OWNER TO saas_test_user;
EOF

if [ $? -eq 0 ]; then
    log_success "Schema permissions fixed"
else
    log_error "Failed to fix schema permissions"
    exit 1
fi

# Check if schema file exists
SCHEMA_FILE="/opt/mobile-automation-saas/saas_infrastructure/database/schema.sql"
if [ ! -f "$SCHEMA_FILE" ]; then
    log_error "Schema file not found at $SCHEMA_FILE"
    log_error "Please ensure the repository is cloned to /opt/mobile-automation-saas"
    exit 1
fi

# Create database schema
log_info "Creating database schema..."
sudo -u postgres psql -d "$DB_NAME" -f "$SCHEMA_FILE"

if [ $? -eq 0 ]; then
    log_success "Database schema created successfully"
else
    log_warning "Schema creation with postgres user failed, trying with saas_test_user..."
    
    # Try with saas_test_user
    psql postgresql://$DB_USER:$DB_PASSWORD@localhost:5432/$DB_NAME -f "$SCHEMA_FILE"
    
    if [ $? -eq 0 ]; then
        log_success "Database schema created with saas_test_user"
    else
        log_error "Failed to create database schema"
        exit 1
    fi
fi

# Transfer ownership of all objects to saas_test_user
log_info "Transferring ownership of database objects..."
sudo -u postgres psql -d "$DB_NAME" << 'EOF'
-- Grant ownership of all tables to saas_test_user
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP
        EXECUTE 'ALTER TABLE ' || quote_ident(r.tablename) || ' OWNER TO saas_test_user';
    END LOOP;
END $$;

-- Grant ownership of all sequences to saas_test_user
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT sequence_name FROM information_schema.sequences WHERE sequence_schema = 'public') LOOP
        EXECUTE 'ALTER SEQUENCE ' || quote_ident(r.sequence_name) || ' OWNER TO saas_test_user';
    END LOOP;
END $$;

-- Grant all privileges on all tables and sequences
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO saas_test_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO saas_test_user;
EOF

log_success "Ownership transferred successfully"

# Test database connection
log_info "Testing database connection..."
psql postgresql://$DB_USER:$DB_PASSWORD@localhost:5432/$DB_NAME -c "SELECT current_user, current_database();"

if [ $? -eq 0 ]; then
    log_success "Database connection test successful"
else
    log_error "Database connection test failed"
    exit 1
fi

# Check if tables were created
log_info "Verifying tables were created..."
TABLE_COUNT=$(psql postgresql://$DB_USER:$DB_PASSWORD@localhost:5432/$DB_NAME -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';")

if [ "$TABLE_COUNT" -gt 0 ]; then
    log_success "Found $TABLE_COUNT tables in the database"
    
    # List the tables
    log_info "Tables created:"
    psql postgresql://$DB_USER:$DB_PASSWORD@localhost:5432/$DB_NAME -c "\dt"
else
    log_error "No tables found in the database"
    exit 1
fi

log_success "PostgreSQL permissions fix completed successfully!"
log_info "You can now continue with the deployment or run the test user creation script"

# Display connection info
echo
log_info "Database connection details:"
log_info "  Host: localhost"
log_info "  Port: 5432"
log_info "  Database: $DB_NAME"
log_info "  User: $DB_USER"
log_info "  Password: $DB_PASSWORD"
log_info "  Connection URL: postgresql://$DB_USER:$DB_PASSWORD@localhost:5432/$DB_NAME"
