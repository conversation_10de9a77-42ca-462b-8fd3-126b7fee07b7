#!/bin/bash

# PostgreSQL Authentication Fix Script
# Diagnoses and fixes PostgreSQL authentication issues for the Mobile Automation SaaS Platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${CYAN}[STEP]${NC} $1"; }

# Check if running as root
if [[ $EUID -ne 0 ]]; then
    log_error "This script must be run as root"
    exit 1
fi

echo "========================================================================"
echo "Mobile Automation SaaS Platform - PostgreSQL Authentication Fix"
echo "========================================================================"

# Database configuration
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="mobile_automation_saas_test"
DB_USER="saas_test_user"
DB_PASS="test_password_123"

# Step 1: Check PostgreSQL service status
log_step "Step 1: Checking PostgreSQL service status..."

if systemctl is-active --quiet postgresql; then
    log_success "PostgreSQL service is running"
    POSTGRES_VERSION=$(sudo -u postgres psql -t -c "SELECT version();" | head -1 | awk '{print $2}')
    log_info "PostgreSQL version: $POSTGRES_VERSION"
else
    log_error "PostgreSQL service is not running"
    log_info "Starting PostgreSQL service..."
    systemctl start postgresql
    systemctl enable postgresql
    sleep 5
    if systemctl is-active --quiet postgresql; then
        log_success "PostgreSQL service started successfully"
    else
        log_error "Failed to start PostgreSQL service"
        exit 1
    fi
fi

# Step 2: Check if database and user exist
log_step "Step 2: Checking database and user existence..."

# Check if database exists
DB_EXISTS=$(sudo -u postgres psql -t -c "SELECT 1 FROM pg_database WHERE datname='$DB_NAME';" | tr -d ' ')
if [[ "$DB_EXISTS" == "1" ]]; then
    log_success "Database '$DB_NAME' exists"
else
    log_warning "Database '$DB_NAME' does not exist - creating it..."
    sudo -u postgres createdb "$DB_NAME"
    log_success "Database '$DB_NAME' created"
fi

# Check if user exists
USER_EXISTS=$(sudo -u postgres psql -t -c "SELECT 1 FROM pg_roles WHERE rolname='$DB_USER';" | tr -d ' ')
if [[ "$USER_EXISTS" == "1" ]]; then
    log_success "User '$DB_USER' exists"
else
    log_warning "User '$DB_USER' does not exist - creating it..."
    sudo -u postgres psql -c "CREATE USER $DB_USER WITH PASSWORD '$DB_PASS';"
    log_success "User '$DB_USER' created"
fi

# Step 3: Set correct password and permissions
log_step "Step 3: Setting correct password and permissions..."

log_info "Setting password for user '$DB_USER'..."
sudo -u postgres psql -c "ALTER USER $DB_USER WITH PASSWORD '$DB_PASS';"

log_info "Granting permissions to user '$DB_USER'..."
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;"
sudo -u postgres psql -d "$DB_NAME" -c "GRANT ALL ON SCHEMA public TO $DB_USER;"
sudo -u postgres psql -d "$DB_NAME" -c "GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO $DB_USER;"
sudo -u postgres psql -d "$DB_NAME" -c "GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO $DB_USER;"

# Grant default privileges for future objects
sudo -u postgres psql -d "$DB_NAME" -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO $DB_USER;"
sudo -u postgres psql -d "$DB_NAME" -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO $DB_USER;"

log_success "Permissions granted to user '$DB_USER'"

# Step 4: Configure pg_hba.conf for proper authentication
log_step "Step 4: Configuring PostgreSQL authentication (pg_hba.conf)..."

# Find PostgreSQL configuration directory
PG_VERSION=$(sudo -u postgres psql -t -c "SHOW server_version_num;" | tr -d ' ')
PG_MAJOR_VERSION=$((PG_VERSION / 10000))

# Common PostgreSQL config paths
PG_CONFIG_PATHS=(
    "/etc/postgresql/$PG_MAJOR_VERSION/main"
    "/etc/postgresql/$PG_MAJOR_VERSION/main"
    "/var/lib/pgsql/$PG_MAJOR_VERSION/data"
    "/usr/local/pgsql/data"
)

PG_HBA_CONF=""
for path in "${PG_CONFIG_PATHS[@]}"; do
    if [[ -f "$path/pg_hba.conf" ]]; then
        PG_HBA_CONF="$path/pg_hba.conf"
        break
    fi
done

if [[ -z "$PG_HBA_CONF" ]]; then
    # Try to find it using PostgreSQL
    PG_HBA_CONF=$(sudo -u postgres psql -t -c "SHOW hba_file;" | tr -d ' ')
fi

if [[ -f "$PG_HBA_CONF" ]]; then
    log_success "Found pg_hba.conf at: $PG_HBA_CONF"
    
    # Backup original pg_hba.conf
    cp "$PG_HBA_CONF" "$PG_HBA_CONF.backup.$(date +%Y%m%d_%H%M%S)"
    log_info "Backed up original pg_hba.conf"
    
    # Check if our configuration already exists
    if grep -q "# Mobile Automation SaaS Platform" "$PG_HBA_CONF"; then
        log_info "SaaS Platform configuration already exists in pg_hba.conf"
    else
        log_info "Adding SaaS Platform configuration to pg_hba.conf..."
        
        # Add configuration for local connections
        cat >> "$PG_HBA_CONF" << EOF

# Mobile Automation SaaS Platform - Local connections
local   $DB_NAME        $DB_USER                                md5
host    $DB_NAME        $DB_USER        127.0.0.1/32            md5
host    $DB_NAME        $DB_USER        ::1/128                 md5
EOF
        log_success "Added SaaS Platform configuration to pg_hba.conf"
    fi
    
    # Reload PostgreSQL configuration
    log_info "Reloading PostgreSQL configuration..."
    sudo -u postgres psql -c "SELECT pg_reload_conf();"
    log_success "PostgreSQL configuration reloaded"
    
else
    log_error "Could not find pg_hba.conf file"
    log_info "Please manually configure PostgreSQL authentication"
fi

# Step 5: Test database connectivity
log_step "Step 5: Testing database connectivity..."

log_info "Testing connection as postgres user..."
if sudo -u postgres psql -d "$DB_NAME" -c "SELECT 1;" >/dev/null 2>&1; then
    log_success "Connection as postgres user successful"
else
    log_error "Connection as postgres user failed"
fi

log_info "Testing connection with application credentials..."
export PGPASSWORD="$DB_PASS"
if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" >/dev/null 2>&1; then
    log_success "Connection with application credentials successful"
else
    log_error "Connection with application credentials failed"
    log_info "Trying alternative authentication methods..."
    
    # Try with different authentication methods
    log_info "Testing with explicit password prompt..."
    echo "$DB_PASS" | psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" >/dev/null 2>&1 && log_success "Password authentication working" || log_warning "Password authentication still failing"
fi

# Step 6: Fix application database configuration
log_step "Step 6: Fixing application database configuration..."

cd /opt/mobile-automation-saas

# Create proper .env file with correct DATABASE_URL
log_info "Creating/updating .env file with correct database configuration..."
cat > saas_infrastructure/app/.env << EOF
# Environment Configuration
ENVIRONMENT=test
FLASK_ENV=test
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
DEBUG=True

# Database Configuration - Fixed authentication
DATABASE_URL=postgresql://$DB_USER:$DB_PASS@$DB_HOST:$DB_PORT/$DB_NAME

# Security Configuration
SECRET_KEY=test-secret-key-change-in-production
JWT_SECRET_KEY=jwt-test-secret-key-change-in-production

# Logging
LOG_LEVEL=INFO
EOF

log_success "Updated .env file with correct database configuration"

# Verify the DATABASE_URL format
log_info "Database URL: postgresql://$DB_USER:***@$DB_HOST:$DB_PORT/$DB_NAME"

# Step 7: Test database connection with Python/SQLAlchemy
log_step "Step 7: Testing database connection with Python..."

# Create a simple test script
cat > /tmp/test_db_connection.py << EOF
import os
import sys
sys.path.append('/opt/mobile-automation-saas')

from sqlalchemy import create_engine, text
from dotenv import load_dotenv

# Load environment variables
load_dotenv('/opt/mobile-automation-saas/saas_infrastructure/app/.env')

database_url = os.environ.get('DATABASE_URL')
print(f"Testing connection with URL: {database_url.replace(':test_password_123', ':***')}")

try:
    engine = create_engine(database_url)
    with engine.connect() as conn:
        result = conn.execute(text("SELECT 1 as test"))
        print("✓ Database connection successful!")
        
        # Test tenant table access
        try:
            result = conn.execute(text("SELECT COUNT(*) FROM tenants"))
            count = result.scalar()
            print(f"✓ Tenants table accessible, found {count} tenants")
        except Exception as e:
            print(f"⚠ Tenants table not accessible: {e}")
            
except Exception as e:
    print(f"✗ Database connection failed: {e}")
    sys.exit(1)
EOF

# Run the test script
log_info "Running Python database connection test..."
cd /opt/mobile-automation-saas
source venv/bin/activate
python /tmp/test_db_connection.py

# Clean up test script
rm -f /tmp/test_db_connection.py

# Step 8: Create/update tenant data
log_step "Step 8: Creating/updating tenant data..."

log_info "Creating tenant data fix script..."
cat > /tmp/fix_tenant_data.sql << 'EOF'
-- Fix Tenant Data Script
-- This script ensures the tenant table has the correct structure and test data

-- Create tenants table if it doesn't exist
CREATE TABLE IF NOT EXISTS tenants (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    subdomain VARCHAR(100) UNIQUE NOT NULL,
    subscription_tier VARCHAR(50) DEFAULT 'basic',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Create index for faster subdomain lookups
CREATE INDEX IF NOT EXISTS idx_tenants_subdomain ON tenants(subdomain);

-- Add is_active column if it doesn't exist (for backwards compatibility)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'tenants' AND column_name = 'is_active') THEN
        ALTER TABLE tenants ADD COLUMN is_active BOOLEAN DEFAULT true;
    END IF;
END $$;

-- Update existing tenants to be active if is_active is NULL
UPDATE tenants SET is_active = true WHERE is_active IS NULL;

-- Insert or update test tenants
INSERT INTO tenants (name, subdomain, subscription_tier, is_active)
VALUES
    ('Test Company 1', 'testcompany1', 'basic', true),
    ('Test Company 2', 'testcompany2', 'premium', true)
ON CONFLICT (subdomain)
DO UPDATE SET
    name = EXCLUDED.name,
    subscription_tier = EXCLUDED.subscription_tier,
    is_active = EXCLUDED.is_active,
    updated_at = CURRENT_TIMESTAMP;

-- Verify tenant data
SELECT 'Tenant verification:' as info;
SELECT id, name, subdomain, subscription_tier, is_active, created_at
FROM tenants
WHERE subdomain IN ('testcompany1', 'testcompany2')
ORDER BY id;
EOF

log_info "Running tenant data fix script..."
export PGPASSWORD="$DB_PASS"
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f /tmp/fix_tenant_data.sql

# Clean up temporary file
rm -f /tmp/fix_tenant_data.sql

# Step 9: Restart application
log_step "Step 9: Restarting application with fixed configuration..."

log_info "Stopping application..."
supervisorctl stop mobile-automation-saas 2>/dev/null || true

log_info "Starting application..."
supervisorctl start mobile-automation-saas

# Wait for application to start
log_info "Waiting for application to start..."
sleep 15

# Check application status
APP_STATUS=$(supervisorctl status mobile-automation-saas)
if [[ "$APP_STATUS" == *"RUNNING"* ]]; then
    log_success "Application is running: $APP_STATUS"
else
    log_error "Application failed to start: $APP_STATUS"
    log_info "Checking logs..."
    tail -20 /var/log/mobile-automation-saas.log
fi

# Step 10: Test tenant endpoints
log_step "Step 10: Testing tenant endpoints..."

SERVER_IP=$(hostname -I | awk '{print $1}')
BASE_URL="http://$SERVER_IP:5000"

# Wait a bit more for the application to fully initialize
sleep 10

# Test tenant 1
log_info "Testing tenant 1 endpoint..."
TENANT1_RESPONSE=$(curl -s -H "X-Tenant-Subdomain: testcompany1" "$BASE_URL/api/tenant/info" 2>/dev/null || echo "FAILED")
if echo "$TENANT1_RESPONSE" | grep -q '"tenant"'; then
    log_success "Tenant 1 endpoint working!"
    echo "Response: $TENANT1_RESPONSE"
else
    log_warning "Tenant 1 endpoint issue: $TENANT1_RESPONSE"
fi

# Test tenant 2
log_info "Testing tenant 2 endpoint..."
TENANT2_RESPONSE=$(curl -s -H "X-Tenant-Subdomain: testcompany2" "$BASE_URL/api/tenant/info" 2>/dev/null || echo "FAILED")
if echo "$TENANT2_RESPONSE" | grep -q '"tenant"'; then
    log_success "Tenant 2 endpoint working!"
    echo "Response: $TENANT2_RESPONSE"
else
    log_warning "Tenant 2 endpoint issue: $TENANT2_RESPONSE"
fi

# Final summary
echo
echo "========================================================================"
log_success "POSTGRESQL AUTHENTICATION FIX COMPLETED"
echo "========================================================================"

log_info "Database Configuration:"
log_info "  - Host: $DB_HOST:$DB_PORT"
log_info "  - Database: $DB_NAME"
log_info "  - User: $DB_USER"
log_info "  - Authentication: md5 (password-based)"

log_info "Application Status:"
supervisorctl status mobile-automation-saas

log_info "Test Commands:"
log_info "  # Manual database test:"
log_info "  PGPASSWORD='$DB_PASS' psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c 'SELECT * FROM tenants;'"
log_info ""
log_info "  # Tenant endpoint tests:"
log_info "  curl -H 'X-Tenant-Subdomain: testcompany1' $BASE_URL/api/tenant/info"
log_info "  curl -H 'X-Tenant-Subdomain: testcompany2' $BASE_URL/api/tenant/info"

echo
log_success "PostgreSQL authentication should now be working correctly!"
log_info "Check application logs if issues persist: tail -f /var/log/mobile-automation-saas.log"
