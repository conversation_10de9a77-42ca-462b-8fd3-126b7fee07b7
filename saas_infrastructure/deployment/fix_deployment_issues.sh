#!/bin/bash

# Comprehensive Fix Script for Deployment Issues
# Addresses eventlet compatibility, distutils, and API routing problems

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Check if running as root
if [[ $EUID -ne 0 ]]; then
    log_error "This script must be run as root"
    exit 1
fi

log_info "Fixing deployment issues: eventlet, distutils, and API routing..."

# Step 1: Stop current application
log_info "Step 1: Stopping current application..."
supervisorctl stop mobile-automation-saas 2>/dev/null || true
supervisorctl stop mobile-automation-saas-dev 2>/dev/null || true

# Step 2: Fix Python environment and dependencies
log_info "Step 2: Fixing Python environment..."
cd /opt/mobile-automation-saas

# Activate virtual environment
source venv/bin/activate

# Install/upgrade distutils for Python 3.12 compatibility
log_info "Installing setuptools for distutils compatibility..."
pip install --upgrade setuptools

# Fix eventlet version compatibility
log_info "Fixing eventlet version..."
pip uninstall -y eventlet || true
pip install eventlet>=0.33.3

# Reinstall gunicorn with proper dependencies
log_info "Reinstalling Gunicorn with dependencies..."
pip uninstall -y gunicorn || true
pip install gunicorn[eventlet]==21.2.0

# Install all requirements again to ensure compatibility
log_info "Reinstalling all requirements..."
pip install -r saas_infrastructure/app/requirements.txt

# Step 3: Update Gunicorn configuration for compatibility
log_info "Step 3: Updating Gunicorn configuration..."
cat > saas_infrastructure/app/gunicorn.conf.py << 'EOF'
# Gunicorn configuration for Mobile Automation SaaS Platform
import os
import multiprocessing

# Server socket
bind = f"0.0.0.0:{os.environ.get('PORT', '5000')}"
backlog = 2048

# Worker processes - use sync worker instead of eventlet for compatibility
workers = 1  # Start with single worker for debugging
worker_class = "sync"  # Use sync worker instead of eventlet
worker_connections = 1000
timeout = 120
keepalive = 2

# Restart workers after this many requests
max_requests = 1000
max_requests_jitter = 50

# Logging
accesslog = "/var/log/mobile-automation-saas-access.log"
errorlog = "/var/log/mobile-automation-saas-error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'

# Process naming
proc_name = "mobile-automation-saas"

# Server mechanics
daemon = False
pidfile = "/var/run/mobile-automation-saas.pid"
user = "www-data"
group = "www-data"

# Environment
raw_env = [
    f"ENVIRONMENT={os.environ.get('ENVIRONMENT', 'test')}",
    f"FLASK_ENV={os.environ.get('FLASK_ENV', 'test')}",
    f"DATABASE_URL={os.environ.get('DATABASE_URL', 'postgresql://saas_test_user:test_password_123@localhost:5432/mobile_automation_saas_test')}",
]

# Preload application
preload_app = True
reload = False
graceful_timeout = 30
listen = 1024
EOF

# Step 4: Update Supervisor configuration to use development mode for now
log_info "Step 4: Updating Supervisor configuration..."
cat > /etc/supervisor/conf.d/mobile-automation-saas.conf << 'EOF'
[program:mobile-automation-saas]
command=/opt/mobile-automation-saas/venv/bin/python -m saas_infrastructure.app.saas_app
directory=/opt/mobile-automation-saas
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/mobile-automation-saas.log
stderr_logfile=/var/log/mobile-automation-saas.log
environment=PATH="/opt/mobile-automation-saas/venv/bin",PYTHONPATH="/opt/mobile-automation-saas",ENVIRONMENT="test",FLASK_ENV="test",DATABASE_URL="postgresql://saas_test_user:test_password_123@localhost:5432/mobile_automation_saas_test",FLASK_HOST="0.0.0.0",FLASK_PORT="5000"
stopasgroup=true
killasgroup=true
EOF

# Step 5: Set proper permissions
log_info "Step 5: Setting permissions..."
chown -R www-data:www-data /opt/mobile-automation-saas
chmod +x /opt/mobile-automation-saas/venv/bin/python

# Create and set permissions for log files
touch /var/log/mobile-automation-saas.log
touch /var/log/mobile-automation-saas-access.log
touch /var/log/mobile-automation-saas-error.log
chown www-data:www-data /var/log/mobile-automation-saas*.log

# Step 6: Test the application configuration
log_info "Step 6: Testing application configuration..."

# Test import and basic functionality
sudo -u www-data bash -c "
cd /opt/mobile-automation-saas
source venv/bin/activate
export PYTHONPATH=/opt/mobile-automation-saas
export ENVIRONMENT=test
export DATABASE_URL=postgresql://saas_test_user:test_password_123@localhost:5432/mobile_automation_saas_test
python -c 'from saas_infrastructure.app.saas_app import create_app; app, socketio = create_app(); print(\"Application import successful\")'
"

if [ $? -eq 0 ]; then
    log_success "Application configuration test passed"
else
    log_error "Application configuration test failed"
    exit 1
fi

# Step 7: Start application
log_info "Step 7: Starting application..."
supervisorctl reread
supervisorctl update
supervisorctl start mobile-automation-saas

# Wait for application to start
sleep 10

# Check if application is running
if supervisorctl status mobile-automation-saas | grep -q "RUNNING"; then
    log_success "Application started successfully"
else
    log_error "Application failed to start"
    log_info "Checking logs..."
    tail -20 /var/log/mobile-automation-saas.log
    exit 1
fi

# Step 8: Test application endpoints
log_info "Step 8: Testing application endpoints..."

# Wait for application to be fully ready
sleep 15

# Test health endpoint
log_info "Testing health endpoint..."
for i in {1..10}; do
    if curl -s http://localhost:5000/health | grep -q "healthy"; then
        log_success "Health endpoint is working"
        break
    else
        if [ $i -eq 10 ]; then
            log_error "Health endpoint test failed"
            exit 1
        fi
        log_info "Waiting for health endpoint... (attempt $i/10)"
        sleep 3
    fi
done

# Test basic application response
log_info "Testing basic application response..."
RESPONSE=$(curl -s -w "%{http_code}" http://localhost:5000/ -o /dev/null)
if [ "$RESPONSE" = "200" ] || [ "$RESPONSE" = "404" ]; then
    log_success "Application is responding (HTTP $RESPONSE)"
else
    log_warning "Application response: HTTP $RESPONSE"
fi

# Test API endpoints
log_info "Testing API structure..."
API_RESPONSE=$(curl -s http://localhost:5000/api/ || echo "endpoint_not_found")
if [[ "$API_RESPONSE" != *"endpoint_not_found"* ]]; then
    log_success "API endpoints are accessible"
else
    log_warning "API endpoints may need configuration"
fi

# Step 9: Display status and next steps
echo
log_success "=== DEPLOYMENT ISSUES FIX COMPLETED ==="
log_info "Application Status:"
supervisorctl status mobile-automation-saas

echo
log_info "Application Information:"
SERVER_IP=$(hostname -I | awk '{print $1}')
log_info "  Application URL: http://$SERVER_IP:5000"
log_info "  Health Check: http://$SERVER_IP:5000/health"

echo
log_info "Recent Application Logs:"
tail -10 /var/log/mobile-automation-saas.log

echo
log_info "Next Steps:"
log_info "1. Verify application is accessible from external IP"
log_info "2. Test API endpoints manually"
log_info "3. Check tenant configuration and routing"
log_info "4. Run validation script again: ./validate_deployment.sh"

echo
log_success "Deployment issues have been addressed!"
log_info "The application should now be accessible and functional."
