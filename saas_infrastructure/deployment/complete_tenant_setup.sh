#!/bin/bash

# Complete Tenant Setup Script
# Completes the tenant setup process after database authentication is fixed

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${CYAN}[STEP]${NC} $1"; }

# Check if running as root
if [[ $EUID -ne 0 ]]; then
    log_error "This script must be run as root"
    exit 1
fi

echo "========================================================================"
echo "Mobile Automation SaaS Platform - Complete Tenant Setup"
echo "========================================================================"

# Database configuration
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="mobile_automation_saas_test"
DB_USER="saas_test_user"
DB_PASS="test_password_123"

# Step 1: Verify database connection is working
log_step "Step 1: Verifying database connection..."

export PGPASSWORD="$DB_PASS"
if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" >/dev/null 2>&1; then
    log_success "Database connection verified"
else
    log_error "Database connection failed. Please run fix_postgresql_auth.sh first"
    exit 1
fi

# Step 2: Create/update tenant data
log_step "Step 2: Creating/updating tenant data..."

log_info "Creating tenant data fix script..."
cat > /tmp/fix_tenant_data.sql << 'EOF'
-- Fix Tenant Data Script
-- This script ensures the tenant table has the correct structure and test data

-- Create tenants table if it doesn't exist
CREATE TABLE IF NOT EXISTS tenants (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    subdomain VARCHAR(100) UNIQUE NOT NULL,
    subscription_tier VARCHAR(50) DEFAULT 'basic',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Create index for faster subdomain lookups
CREATE INDEX IF NOT EXISTS idx_tenants_subdomain ON tenants(subdomain);

-- Add is_active column if it doesn't exist (for backwards compatibility)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tenants' AND column_name = 'is_active') THEN
        ALTER TABLE tenants ADD COLUMN is_active BOOLEAN DEFAULT true;
    END IF;
END $$;

-- Update existing tenants to be active if is_active is NULL
UPDATE tenants SET is_active = true WHERE is_active IS NULL;

-- Insert or update test tenants
INSERT INTO tenants (name, subdomain, subscription_tier, is_active) 
VALUES 
    ('Test Company 1', 'testcompany1', 'basic', true),
    ('Test Company 2', 'testcompany2', 'premium', true)
ON CONFLICT (subdomain) 
DO UPDATE SET 
    name = EXCLUDED.name,
    subscription_tier = EXCLUDED.subscription_tier,
    is_active = EXCLUDED.is_active,
    updated_at = CURRENT_TIMESTAMP;

-- Verify tenant data
SELECT 'Tenant verification:' as info;
SELECT id, name, subdomain, subscription_tier, is_active, created_at 
FROM tenants 
WHERE subdomain IN ('testcompany1', 'testcompany2')
ORDER BY id;
EOF

log_info "Running tenant data fix script..."
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f /tmp/fix_tenant_data.sql

# Clean up temporary file
rm -f /tmp/fix_tenant_data.sql
log_success "Tenant data updated successfully"

# Step 3: Create test users if they don't exist
log_step "Step 3: Creating test users..."

log_info "Creating users table and test users..."
cat > /tmp/create_test_users.sql << 'EOF'
-- Create users table if it doesn't exist
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role VARCHAR(50) DEFAULT 'user',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create index for faster email lookups
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);

-- Get tenant IDs
DO $$
DECLARE
    tenant1_id INTEGER;
    tenant2_id INTEGER;
BEGIN
    -- Get tenant IDs
    SELECT id INTO tenant1_id FROM tenants WHERE subdomain = 'testcompany1';
    SELECT id INTO tenant2_id FROM tenants WHERE subdomain = 'testcompany2';
    
    -- Insert test users (password hash for 'testpass123' and 'testpass456')
    -- Using bcrypt hash for the passwords
    INSERT INTO users (tenant_id, email, password_hash, first_name, last_name, role, is_active)
    VALUES 
        (tenant1_id, '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5u', 'Test', 'User1', 'admin', true),
        (tenant2_id, '<EMAIL>', '$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p02T5u1ZiuQqkpG6l1jn/HL6', 'Test', 'User2', 'admin', true)
    ON CONFLICT (email) 
    DO UPDATE SET 
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name,
        role = EXCLUDED.role,
        is_active = EXCLUDED.is_active,
        updated_at = CURRENT_TIMESTAMP;
        
    RAISE NOTICE 'Test users created/updated successfully';
END $$;

-- Verify user data
SELECT 'User verification:' as info;
SELECT u.id, u.email, u.first_name, u.last_name, u.role, u.is_active, t.subdomain as tenant_subdomain
FROM users u
JOIN tenants t ON u.tenant_id = t.id
WHERE u.email IN ('<EMAIL>', '<EMAIL>')
ORDER BY u.id;
EOF

psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f /tmp/create_test_users.sql

# Clean up temporary file
rm -f /tmp/create_test_users.sql
log_success "Test users created successfully"

# Step 4: Restart application
log_step "Step 4: Restarting application..."

cd /opt/mobile-automation-saas

log_info "Stopping application..."
supervisorctl stop mobile-automation-saas 2>/dev/null || true

log_info "Starting application..."
supervisorctl start mobile-automation-saas

# Wait for application to start
log_info "Waiting for application to start..."
sleep 15

# Check application status
APP_STATUS=$(supervisorctl status mobile-automation-saas)
if [[ "$APP_STATUS" == *"RUNNING"* ]]; then
    log_success "Application is running: $APP_STATUS"
else
    log_error "Application failed to start: $APP_STATUS"
    log_info "Checking logs..."
    tail -20 /var/log/mobile-automation-saas.log
    exit 1
fi

# Step 5: Test tenant endpoints
log_step "Step 5: Testing tenant endpoints..."

SERVER_IP=$(hostname -I | awk '{print $1}')
BASE_URL="http://$SERVER_IP:5000"

# Wait a bit more for the application to fully initialize
sleep 10

# Test tenant 1
log_info "Testing tenant 1 endpoint..."
TENANT1_RESPONSE=$(curl -s -H "X-Tenant-Subdomain: testcompany1" "$BASE_URL/api/tenant/info" 2>/dev/null || echo "FAILED")
if echo "$TENANT1_RESPONSE" | grep -q '"tenant"'; then
    log_success "Tenant 1 endpoint working!"
    echo "Response: $TENANT1_RESPONSE"
else
    log_warning "Tenant 1 endpoint issue: $TENANT1_RESPONSE"
fi

# Test tenant 2
log_info "Testing tenant 2 endpoint..."
TENANT2_RESPONSE=$(curl -s -H "X-Tenant-Subdomain: testcompany2" "$BASE_URL/api/tenant/info" 2>/dev/null || echo "FAILED")
if echo "$TENANT2_RESPONSE" | grep -q '"tenant"'; then
    log_success "Tenant 2 endpoint working!"
    echo "Response: $TENANT2_RESPONSE"
else
    log_warning "Tenant 2 endpoint issue: $TENANT2_RESPONSE"
fi

# Step 6: Test authentication
log_step "Step 6: Testing authentication..."

# Test user 1 authentication
log_info "Testing user 1 authentication..."
AUTH1_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/login" \
    -H "Content-Type: application/json" \
    -H "X-Tenant-Subdomain: testcompany1" \
    -d '{"email": "<EMAIL>", "password": "testpass123"}' 2>/dev/null || echo "FAILED")

if echo "$AUTH1_RESPONSE" | grep -q "access_token"; then
    log_success "User 1 authentication successful"
    echo "Response contains access token"
else
    log_warning "User 1 authentication failed: $AUTH1_RESPONSE"
fi

# Test user 2 authentication
log_info "Testing user 2 authentication..."
AUTH2_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/login" \
    -H "Content-Type: application/json" \
    -H "X-Tenant-Subdomain: testcompany2" \
    -d '{"email": "<EMAIL>", "password": "testpass456"}' 2>/dev/null || echo "FAILED")

if echo "$AUTH2_RESPONSE" | grep -q "access_token"; then
    log_success "User 2 authentication successful"
    echo "Response contains access token"
else
    log_warning "User 2 authentication failed: $AUTH2_RESPONSE"
fi

# Final summary
echo
echo "========================================================================"
log_success "TENANT SETUP COMPLETED SUCCESSFULLY"
echo "========================================================================"

log_info "Database Status:"
log_info "  - PostgreSQL: Connected and working"
log_info "  - Tenants: testcompany1 and testcompany2 available"
log_info "  - Users: testuser1@example.<NAME_EMAIL> created"

log_info "Application Status:"
supervisorctl status mobile-automation-saas

log_info "Test Commands:"
log_info "  # Tenant endpoint tests:"
log_info "  curl -H 'X-Tenant-Subdomain: testcompany1' $BASE_URL/api/tenant/info"
log_info "  curl -H 'X-Tenant-Subdomain: testcompany2' $BASE_URL/api/tenant/info"
log_info ""
log_info "  # Authentication tests:"
log_info "  curl -X POST $BASE_URL/api/auth/login -H 'Content-Type: application/json' -H 'X-Tenant-Subdomain: testcompany1' -d '{\"email\": \"<EMAIL>\", \"password\": \"testpass123\"}'"
log_info "  curl -X POST $BASE_URL/api/auth/login -H 'Content-Type: application/json' -H 'X-Tenant-Subdomain: testcompany2' -d '{\"email\": \"<EMAIL>\", \"password\": \"testpass456\"}'"

echo
log_success "The Mobile Automation SaaS Platform is now fully functional!"
log_info "All tenant endpoints should be working correctly."
