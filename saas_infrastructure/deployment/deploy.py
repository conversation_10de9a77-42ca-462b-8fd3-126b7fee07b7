#!/usr/bin/env python3
"""
Automated Deployment Script for Mobile Automation SaaS Platform
Handles cloud infrastructure setup, database initialization, and application deployment
"""

import os
import sys
import json
import subprocess
import logging
import argparse
import secrets
from pathlib import Path
import psycopg2
import requests

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SaaSDeployer:
    def __init__(self, config_file=None):
        self.config_file = config_file or "deployment_config.json"
        self.config = {}
        self.load_config()
        
    def load_config(self):
        """Load deployment configuration"""
        config_path = Path(self.config_file)
        if config_path.exists():
            with open(config_path, 'r') as f:
                self.config = json.load(f)
        else:
            # Create default config
            self.config = self.get_default_config()
            self.save_config()
            logger.info(f"Created default configuration at {config_path}")
    
    def save_config(self):
        """Save deployment configuration"""
        with open(self.config_file, 'w') as f:
            json.dump(self.config, f, indent=2)
    
    def get_default_config(self):
        """Get default deployment configuration"""
        return {
            "environment": "test",  # Changed to test environment
            "database": {
                "host": "localhost",
                "port": 5432,
                "name": "mobile_automation_saas",
                "user": "saas_user",
                "password": "",  # Will be generated
                "ssl_mode": "prefer"  # Relaxed for test environment
            },
            "app": {
                "host": "0.0.0.0",
                "port": 5000,
                "secret_key": "",  # Will be generated
                "jwt_secret": "",  # Will be generated
                "debug": True  # Enable debug for test environment
            },
            "cloudflare": {
                "tunnel_name": "mobile-automation-test-tunnel",
                "tunnel_token": "",  # Optional for test environment
                "domain": "*************"  # Use IP for test environment
            },
            "server": {
                "provider": "custom",  # Custom server deployment
                "ip": "*************",  # Test server IP
                "ssh_user": "root",
                "ssh_key": ""  # SSH key path
            },
            "ssl": {
                "enabled": False,  # Disable SSL for test environment
                "email": "<EMAIL>",
                "use_letsencrypt": False
            },
            "monitoring": {
                "enabled": True,
                "log_level": "DEBUG"  # More verbose logging for test
            },
            "test_mode": True,  # Flag for test environment
            "create_test_users": True  # Auto-create test users
        }
    
    def generate_secrets(self):
        """Generate secure secrets for the application"""
        if not self.config["app"]["secret_key"]:
            self.config["app"]["secret_key"] = secrets.token_urlsafe(32)
            logger.info("Generated Flask secret key")
        
        if not self.config["app"]["jwt_secret"]:
            self.config["app"]["jwt_secret"] = secrets.token_urlsafe(32)
            logger.info("Generated JWT secret key")
        
        if not self.config["database"]["password"]:
            self.config["database"]["password"] = secrets.token_urlsafe(16)
            logger.info("Generated database password")
        
        self.save_config()
    
    def setup_database(self):
        """Setup PostgreSQL database"""
        logger.info("Setting up PostgreSQL database...")
        
        db_config = self.config["database"]
        
        try:
            # Connect as superuser to create database and user
            conn = psycopg2.connect(
                host=db_config["host"],
                port=db_config["port"],
                database="postgres",
                user="postgres",
                password=os.environ.get("POSTGRES_PASSWORD", "")
            )
            conn.autocommit = True
            cursor = conn.cursor()
            
            # Create database
            cursor.execute(f"CREATE DATABASE {db_config['name']}")
            logger.info(f"Created database: {db_config['name']}")
            
            # Create user
            cursor.execute(f"""
                CREATE USER {db_config['user']} 
                WITH PASSWORD '{db_config['password']}'
            """)
            logger.info(f"Created database user: {db_config['user']}")
            
            # Grant privileges
            cursor.execute(f"""
                GRANT ALL PRIVILEGES ON DATABASE {db_config['name']} 
                TO {db_config['user']}
            """)
            logger.info("Granted database privileges")
            
            cursor.close()
            conn.close()
            
            # Initialize schema
            self.initialize_database_schema()
            
        except psycopg2.Error as e:
            if "already exists" in str(e):
                logger.info("Database already exists, skipping creation")
                self.initialize_database_schema()
            else:
                logger.error(f"Database setup failed: {e}")
                raise
    
    def initialize_database_schema(self):
        """Initialize database schema"""
        logger.info("Initializing database schema...")

        db_config = self.config["database"]
        schema_file = Path(__file__).parent.parent / "database" / "schema.sql"

        if not schema_file.exists():
            logger.error(f"Schema file not found: {schema_file}")
            return

        try:
            # Run schema initialization
            cmd = [
                "psql",
                f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['name']}",
                "-f", str(schema_file)
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                logger.info("Database schema initialized successfully")

                # Create test users if in test mode
                if self.config.get("create_test_users", False):
                    self.create_test_users()

            else:
                logger.error(f"Schema initialization failed: {result.stderr}")
                raise Exception("Schema initialization failed")

        except Exception as e:
            logger.error(f"Error initializing schema: {e}")
            raise

    def create_test_users(self):
        """Create test users and tenants for testing"""
        logger.info("Creating test users and tenants...")

        if not self.config.get("test_users"):
            logger.warning("No test users defined in configuration")
            return

        db_config = self.config["database"]

        try:
            conn = psycopg2.connect(
                host=db_config["host"],
                port=db_config["port"],
                database=db_config["name"],
                user=db_config["user"],
                password=db_config["password"]
            )
            conn.autocommit = True
            cursor = conn.cursor()

            for test_user in self.config["test_users"]:
                # Create tenant
                cursor.execute("""
                    INSERT INTO tenants (name, subdomain, subscription_tier, is_active)
                    VALUES (%s, %s, 'starter', true)
                    ON CONFLICT (subdomain) DO NOTHING
                    RETURNING id
                """, (test_user["tenant_name"], test_user["tenant_subdomain"]))

                result = cursor.fetchone()
                if result:
                    tenant_id = result[0]
                    logger.info(f"Created tenant: {test_user['tenant_name']}")
                else:
                    # Get existing tenant ID
                    cursor.execute("SELECT id FROM tenants WHERE subdomain = %s",
                                 (test_user["tenant_subdomain"],))
                    tenant_id = cursor.fetchone()[0]
                    logger.info(f"Using existing tenant: {test_user['tenant_name']}")

                # Create user with hashed password
                import bcrypt
                password_hash = bcrypt.hashpw(
                    test_user["password"].encode('utf-8'),
                    bcrypt.gensalt()
                ).decode('utf-8')

                cursor.execute("""
                    INSERT INTO users (tenant_id, email, password_hash, role, is_active, email_verified)
                    VALUES (%s, %s, %s, %s, true, true)
                    ON CONFLICT (tenant_id, email) DO NOTHING
                """, (tenant_id, test_user["email"], password_hash, test_user["role"]))

                logger.info(f"Created test user: {test_user['email']}")

            cursor.close()
            conn.close()

            logger.info("Test users and tenants created successfully")

        except Exception as e:
            logger.error(f"Error creating test users: {e}")
            raise
    
    def setup_cloudflare_tunnel(self):
        """Setup Cloudflare tunnel"""
        logger.info("Setting up Cloudflare tunnel...")
        
        cf_config = self.config["cloudflare"]
        
        if not cf_config.get("tunnel_token"):
            logger.warning("Cloudflare tunnel token not provided, skipping tunnel setup")
            return
        
        try:
            # Install cloudflared if not present
            if not self.check_command_exists("cloudflared"):
                self.install_cloudflared()
            
            # Create tunnel configuration
            tunnel_config = {
                "tunnel": cf_config["tunnel_name"],
                "credentials-file": f"/etc/cloudflared/{cf_config['tunnel_name']}.json",
                "ingress": [
                    {
                        "hostname": cf_config["domain"],
                        "service": f"http://localhost:{self.config['app']['port']}"
                    },
                    {
                        "service": "http_status:404"
                    }
                ]
            }
            
            # Write tunnel config
            os.makedirs("/etc/cloudflared", exist_ok=True)
            with open("/etc/cloudflared/config.yml", "w") as f:
                import yaml
                yaml.dump(tunnel_config, f)
            
            # Install tunnel as service
            subprocess.run([
                "cloudflared", "service", "install", cf_config["tunnel_token"]
            ], check=True)
            
            logger.info("Cloudflare tunnel configured successfully")
            
        except Exception as e:
            logger.error(f"Cloudflare tunnel setup failed: {e}")
            raise
    
    def install_cloudflared(self):
        """Install cloudflared binary"""
        logger.info("Installing cloudflared...")
        
        # Download and install cloudflared
        if sys.platform.startswith("linux"):
            subprocess.run([
                "wget", "-O", "/tmp/cloudflared.deb",
                "https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64.deb"
            ], check=True)
            
            subprocess.run([
                "dpkg", "-i", "/tmp/cloudflared.deb"
            ], check=True)
        else:
            logger.error("Cloudflared installation only supported on Linux")
            raise Exception("Unsupported platform for cloudflared installation")
    
    def check_command_exists(self, command):
        """Check if a command exists"""
        try:
            subprocess.run([command, "--version"], 
                         capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def install_dependencies(self):
        """Install system dependencies"""
        logger.info("Installing system dependencies...")
        
        # Update package list
        subprocess.run(["apt", "update"], check=True)
        
        # Install required packages
        packages = [
            "python3", "python3-pip", "python3-venv",
            "postgresql-client", "nginx", "supervisor",
            "git", "curl", "wget"
        ]
        
        subprocess.run(["apt", "install", "-y"] + packages, check=True)
        logger.info("System dependencies installed")
    
    def setup_application(self):
        """Setup the SaaS application"""
        logger.info("Setting up SaaS application...")
        
        app_dir = "/opt/mobile-automation-saas"
        
        # Create application directory
        os.makedirs(app_dir, exist_ok=True)
        
        # Copy application files
        src_dir = Path(__file__).parent.parent
        subprocess.run([
            "cp", "-r", str(src_dir), app_dir
        ], check=True)
        
        # Create virtual environment
        venv_dir = f"{app_dir}/venv"
        subprocess.run([
            "python3", "-m", "venv", venv_dir
        ], check=True)
        
        # Install Python dependencies
        pip_cmd = f"{venv_dir}/bin/pip"
        subprocess.run([
            pip_cmd, "install", "-r", f"{app_dir}/app/requirements.txt"
        ], check=True)
        
        # Create environment file
        env_file = f"{app_dir}/.env"
        with open(env_file, "w") as f:
            f.write(f"FLASK_SECRET_KEY={self.config['app']['secret_key']}\n")
            f.write(f"JWT_SECRET_KEY={self.config['app']['jwt_secret']}\n")
            f.write(f"DATABASE_URL=postgresql://{self.config['database']['user']}:{self.config['database']['password']}@{self.config['database']['host']}:{self.config['database']['port']}/{self.config['database']['name']}\n")
            f.write(f"ENVIRONMENT={self.config['environment']}\n")
        
        logger.info("Application setup completed")
    
    def setup_nginx(self):
        """Setup Nginx reverse proxy"""
        logger.info("Setting up Nginx...")
        
        nginx_config = f"""
server {{
    listen 80;
    server_name {self.config['cloudflare']['domain']};
    
    location / {{
        proxy_pass http://localhost:{self.config['app']['port']};
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }}
    
    location /socket.io/ {{
        proxy_pass http://localhost:{self.config['app']['port']};
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }}
}}
"""
        
        # Write Nginx config
        with open("/etc/nginx/sites-available/mobile-automation-saas", "w") as f:
            f.write(nginx_config)
        
        # Enable site
        os.symlink(
            "/etc/nginx/sites-available/mobile-automation-saas",
            "/etc/nginx/sites-enabled/mobile-automation-saas"
        )
        
        # Test and reload Nginx
        subprocess.run(["nginx", "-t"], check=True)
        subprocess.run(["systemctl", "reload", "nginx"], check=True)
        
        logger.info("Nginx configured successfully")
    
    def setup_supervisor(self):
        """Setup Supervisor for process management"""
        logger.info("Setting up Supervisor...")
        
        supervisor_config = f"""
[program:mobile-automation-saas]
command=/opt/mobile-automation-saas/venv/bin/python -m saas_infrastructure.app.saas_app
directory=/opt/mobile-automation-saas
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/mobile-automation-saas.log
environment=PATH="/opt/mobile-automation-saas/venv/bin"
"""
        
        # Write Supervisor config
        with open("/etc/supervisor/conf.d/mobile-automation-saas.conf", "w") as f:
            f.write(supervisor_config)
        
        # Reload Supervisor
        subprocess.run(["supervisorctl", "reread"], check=True)
        subprocess.run(["supervisorctl", "update"], check=True)
        subprocess.run(["supervisorctl", "start", "mobile-automation-saas"], check=True)
        
        logger.info("Supervisor configured successfully")
    
    def deploy(self):
        """Run full deployment"""
        logger.info("Starting SaaS platform deployment...")
        
        try:
            # Generate secrets
            self.generate_secrets()
            
            # Install dependencies
            self.install_dependencies()
            
            # Setup database
            self.setup_database()
            
            # Setup application
            self.setup_application()
            
            # Setup Nginx
            self.setup_nginx()
            
            # Setup Supervisor
            self.setup_supervisor()
            
            # Setup Cloudflare tunnel
            self.setup_cloudflare_tunnel()
            
            logger.info("✓ Deployment completed successfully!")
            logger.info(f"Application should be accessible at: https://{self.config['cloudflare']['domain']}")
            
        except Exception as e:
            logger.error(f"Deployment failed: {e}")
            raise

def main():
    parser = argparse.ArgumentParser(description='Deploy Mobile Automation SaaS Platform')
    parser.add_argument('--config', help='Configuration file path')
    parser.add_argument('--step', choices=[
        'secrets', 'deps', 'database', 'app', 'nginx', 'supervisor', 'tunnel', 'all'
    ], default='all', help='Deployment step to run')
    
    args = parser.parse_args()
    
    deployer = SaaSDeployer(args.config)
    
    if args.step == 'all':
        deployer.deploy()
    elif args.step == 'secrets':
        deployer.generate_secrets()
    elif args.step == 'deps':
        deployer.install_dependencies()
    elif args.step == 'database':
        deployer.setup_database()
    elif args.step == 'app':
        deployer.setup_application()
    elif args.step == 'nginx':
        deployer.setup_nginx()
    elif args.step == 'supervisor':
        deployer.setup_supervisor()
    elif args.step == 'tunnel':
        deployer.setup_cloudflare_tunnel()

if __name__ == "__main__":
    main()
