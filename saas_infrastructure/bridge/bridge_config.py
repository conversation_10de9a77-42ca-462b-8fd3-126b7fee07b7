#!/usr/bin/env python3
"""
Bridge Configuration Manager
Handles bridge configuration, token management, and setup validation
"""

import json
import os
import sys
import requests
import uuid
from pathlib import Path
import argparse
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BridgeConfigManager:
    def __init__(self, config_dir=None):
        self.config_dir = Path(config_dir or self.get_default_config_dir())
        self.config_file = self.config_dir / "bridge.json"
        self.config = {}
        
    def get_default_config_dir(self):
        """Get default configuration directory"""
        home = Path.home()
        if sys.platform == "win32":
            return home / "MobileAutomationBridge" / "config"
        else:
            return home / ".mobile-automation-bridge" / "config"
    
    def load_config(self):
        """Load configuration from file"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    self.config = json.load(f)
                logger.info(f"Configuration loaded from {self.config_file}")
            except Exception as e:
                logger.error(f"Failed to load configuration: {e}")
                self.config = {}
        else:
            logger.info("No existing configuration found")
            self.config = {}
    
    def save_config(self):
        """Save configuration to file"""
        try:
            self.config_dir.mkdir(parents=True, exist_ok=True)
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
            logger.info(f"Configuration saved to {self.config_file}")
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            raise
    
    def setup_initial_config(self, cloud_endpoint, email, password, bridge_name=None):
        """Setup initial configuration by registering with cloud"""
        logger.info("Setting up initial bridge configuration...")
        
        # Generate bridge name if not provided
        if not bridge_name:
            bridge_name = f"Bridge-{uuid.uuid4().hex[:8]}"
        
        try:
            # Register bridge with cloud platform
            bridge_token = self.register_bridge_with_cloud(
                cloud_endpoint, email, password, bridge_name
            )
            
            # Create configuration
            self.config = {
                "cloud_endpoint": cloud_endpoint,
                "bridge_token": bridge_token,
                "bridge_name": bridge_name,
                "bridge_id": str(uuid.uuid4()),
                "log_level": "INFO",
                "reconnect_delay": 5,
                "max_reconnect_delay": 60,
                "device_scan_interval": 10,
                "heartbeat_interval": 30,
                "cloudflare_tunnel": {
                    "enabled": False,
                    "tunnel_name": "",
                    "credentials_file": ""
                },
                "device_filters": {
                    "ios_enabled": True,
                    "android_enabled": True,
                    "exclude_simulators": True
                },
                "security": {
                    "verify_ssl": True,
                    "allowed_commands": [
                        "screenshot",
                        "install_app",
                        "run_test",
                        "device_info"
                    ]
                }
            }
            
            self.save_config()
            logger.info("✓ Initial configuration completed successfully")
            
            return bridge_token
            
        except Exception as e:
            logger.error(f"Failed to setup initial configuration: {e}")
            raise
    
    def register_bridge_with_cloud(self, cloud_endpoint, email, password, bridge_name):
        """Register bridge with cloud platform and get token"""
        logger.info(f"Registering bridge '{bridge_name}' with cloud platform...")
        
        # First, authenticate user
        auth_url = f"https://{cloud_endpoint}/api/auth/login"
        auth_data = {
            "email": email,
            "password": password
        }
        
        try:
            auth_response = requests.post(auth_url, json=auth_data, timeout=30)
            auth_response.raise_for_status()
            auth_result = auth_response.json()
            
            access_token = auth_result.get('access_token')
            if not access_token:
                raise Exception("Failed to get access token from authentication")
            
            # Register bridge
            bridge_url = f"https://{cloud_endpoint}/api/bridge/register"
            bridge_data = {
                "bridge_name": bridge_name
            }
            
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
            
            bridge_response = requests.post(
                bridge_url, 
                json=bridge_data, 
                headers=headers, 
                timeout=30
            )
            bridge_response.raise_for_status()
            bridge_result = bridge_response.json()
            
            bridge_token = bridge_result.get('bridge_token')
            if not bridge_token:
                raise Exception("Failed to get bridge token from registration")
            
            logger.info("✓ Bridge registered successfully")
            return bridge_token
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Network error during registration: {e}")
            raise Exception(f"Failed to connect to cloud platform: {e}")
        except Exception as e:
            logger.error(f"Registration failed: {e}")
            raise
    
    def update_config(self, **kwargs):
        """Update configuration values"""
        for key, value in kwargs.items():
            if '.' in key:
                # Handle nested keys like 'cloudflare_tunnel.enabled'
                keys = key.split('.')
                config_section = self.config
                for k in keys[:-1]:
                    if k not in config_section:
                        config_section[k] = {}
                    config_section = config_section[k]
                config_section[keys[-1]] = value
            else:
                self.config[key] = value
        
        self.save_config()
        logger.info(f"Configuration updated: {list(kwargs.keys())}")
    
    def validate_config(self):
        """Validate current configuration"""
        logger.info("Validating configuration...")
        
        required_fields = [
            'cloud_endpoint',
            'bridge_token',
            'bridge_name'
        ]
        
        missing_fields = []
        for field in required_fields:
            if not self.config.get(field):
                missing_fields.append(field)
        
        if missing_fields:
            raise Exception(f"Missing required configuration fields: {missing_fields}")
        
        # Validate cloud endpoint format
        endpoint = self.config['cloud_endpoint']
        if not endpoint or '.' not in endpoint:
            raise Exception("Invalid cloud endpoint format")
        
        # Test connection to cloud platform
        try:
            test_url = f"https://{endpoint}/health"
            response = requests.get(test_url, timeout=10)
            response.raise_for_status()
            logger.info("✓ Cloud platform connectivity verified")
        except Exception as e:
            logger.warning(f"Could not verify cloud platform connectivity: {e}")
        
        logger.info("✓ Configuration validation completed")
    
    def test_bridge_connection(self):
        """Test bridge connection to cloud"""
        logger.info("Testing bridge connection...")
        
        try:
            # This would test the WebSocket connection
            # For now, just validate the configuration
            self.validate_config()
            
            # In a real implementation, this would:
            # 1. Establish WebSocket connection
            # 2. Authenticate with bridge token
            # 3. Send ping message
            # 4. Verify response
            
            logger.info("✓ Bridge connection test completed")
            return True
            
        except Exception as e:
            logger.error(f"Bridge connection test failed: {e}")
            return False
    
    def get_config_summary(self):
        """Get configuration summary for display"""
        if not self.config:
            return "No configuration found"
        
        summary = []
        summary.append(f"Cloud Endpoint: {self.config.get('cloud_endpoint', 'Not set')}")
        summary.append(f"Bridge Name: {self.config.get('bridge_name', 'Not set')}")
        summary.append(f"Bridge Token: {'Set' if self.config.get('bridge_token') else 'Not set'}")
        summary.append(f"Log Level: {self.config.get('log_level', 'INFO')}")
        
        # Cloudflare Tunnel status
        tunnel_config = self.config.get('cloudflare_tunnel', {})
        tunnel_status = "Enabled" if tunnel_config.get('enabled') else "Disabled"
        summary.append(f"Cloudflare Tunnel: {tunnel_status}")
        
        # Device filters
        device_config = self.config.get('device_filters', {})
        ios_enabled = device_config.get('ios_enabled', True)
        android_enabled = device_config.get('android_enabled', True)
        summary.append(f"iOS Support: {'Enabled' if ios_enabled else 'Disabled'}")
        summary.append(f"Android Support: {'Enabled' if android_enabled else 'Disabled'}")
        
        return "\n".join(summary)
    
    def reset_config(self):
        """Reset configuration to defaults"""
        logger.info("Resetting configuration...")
        
        if self.config_file.exists():
            backup_file = self.config_file.with_suffix('.json.backup')
            self.config_file.rename(backup_file)
            logger.info(f"Existing configuration backed up to {backup_file}")
        
        self.config = {}
        logger.info("✓ Configuration reset completed")

def main():
    parser = argparse.ArgumentParser(description='Bridge Configuration Manager')
    parser.add_argument('--config-dir', help='Configuration directory')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Setup command
    setup_parser = subparsers.add_parser('setup', help='Setup initial configuration')
    setup_parser.add_argument('cloud_endpoint', help='Cloud platform endpoint')
    setup_parser.add_argument('email', help='User email')
    setup_parser.add_argument('password', help='User password')
    setup_parser.add_argument('--bridge-name', help='Custom bridge name')
    
    # Update command
    update_parser = subparsers.add_parser('update', help='Update configuration')
    update_parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'])
    update_parser.add_argument('--ios-enabled', type=bool, help='Enable iOS support')
    update_parser.add_argument('--android-enabled', type=bool, help='Enable Android support')
    update_parser.add_argument('--tunnel-enabled', type=bool, help='Enable Cloudflare tunnel')
    
    # Other commands
    subparsers.add_parser('validate', help='Validate configuration')
    subparsers.add_parser('test', help='Test bridge connection')
    subparsers.add_parser('show', help='Show configuration summary')
    subparsers.add_parser('reset', help='Reset configuration')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        config_manager = BridgeConfigManager(args.config_dir)
        
        if args.command == 'setup':
            config_manager.setup_initial_config(
                args.cloud_endpoint,
                args.email,
                args.password,
                args.bridge_name
            )
            
        elif args.command == 'update':
            config_manager.load_config()
            updates = {}
            
            if args.log_level:
                updates['log_level'] = args.log_level
            if args.ios_enabled is not None:
                updates['device_filters.ios_enabled'] = args.ios_enabled
            if args.android_enabled is not None:
                updates['device_filters.android_enabled'] = args.android_enabled
            if args.tunnel_enabled is not None:
                updates['cloudflare_tunnel.enabled'] = args.tunnel_enabled
            
            if updates:
                config_manager.update_config(**updates)
            else:
                logger.info("No updates specified")
                
        elif args.command == 'validate':
            config_manager.load_config()
            config_manager.validate_config()
            
        elif args.command == 'test':
            config_manager.load_config()
            success = config_manager.test_bridge_connection()
            sys.exit(0 if success else 1)
            
        elif args.command == 'show':
            config_manager.load_config()
            print("\nBridge Configuration:")
            print("=" * 40)
            print(config_manager.get_config_summary())
            print()
            
        elif args.command == 'reset':
            config_manager.reset_config()
            
    except Exception as e:
        logger.error(f"Command failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
