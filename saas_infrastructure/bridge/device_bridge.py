#!/usr/bin/env python3
"""
Local Device Bridge for Mobile Automation SaaS Platform
Discovers local iOS/Android devices and connects them to the cloud platform
"""

import asyncio
import websockets
import json
import subprocess
import ssl
import logging
import time
import uuid
import os
import sys
import signal
from pathlib import Path
from typing import Dict, List, Optional
import argparse

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LocalDeviceBridge:
    def __init__(self, bridge_token: str, cloud_endpoint: str, bridge_name: str = None):
        self.bridge_token = bridge_token
        self.cloud_endpoint = cloud_endpoint
        self.bridge_id = str(uuid.uuid4())
        self.bridge_name = bridge_name or f"Bridge-{self.bridge_id[:8]}"
        self.devices = {}
        self.websocket = None
        self.running = False
        self.reconnect_delay = 5
        self.max_reconnect_delay = 60
        
    async def start_bridge(self):
        """Start the local device bridge"""
        logger.info(f"Starting Local Device Bridge: {self.bridge_name}")
        logger.info(f"Cloud endpoint: {self.cloud_endpoint}")
        
        self.running = True
        
        # Start device discovery loop
        discovery_task = asyncio.create_task(self.device_discovery_loop())
        
        # Start connection loop with auto-reconnect
        connection_task = asyncio.create_task(self.connection_loop())
        
        try:
            # Wait for both tasks
            await asyncio.gather(discovery_task, connection_task)
        except KeyboardInterrupt:
            logger.info("Received interrupt signal, shutting down...")
            self.running = False
            discovery_task.cancel()
            connection_task.cancel()
        except Exception as e:
            logger.error(f"Bridge error: {e}")
            self.running = False
    
    async def connection_loop(self):
        """Main connection loop with auto-reconnect"""
        while self.running:
            try:
                await self.connect_to_cloud()
                await self.process_cloud_commands()
            except websockets.exceptions.ConnectionClosed:
                logger.warning("Cloud connection lost, attempting to reconnect...")
                await asyncio.sleep(self.reconnect_delay)
                # Exponential backoff
                self.reconnect_delay = min(self.reconnect_delay * 2, self.max_reconnect_delay)
            except Exception as e:
                logger.error(f"Connection error: {e}")
                await asyncio.sleep(self.reconnect_delay)
    
    async def connect_to_cloud(self):
        """Establish WebSocket connection to cloud"""
        headers = {
            'Authorization': f'Bearer {self.bridge_token}',
            'X-Bridge-ID': self.bridge_id,
            'X-Bridge-Name': self.bridge_name
        }
        
        try:
            # Build WebSocket URL
            ws_url = f"wss://{self.cloud_endpoint}/bridge"
            
            logger.info(f"Connecting to cloud: {ws_url}")
            
            self.websocket = await websockets.connect(
                ws_url,
                extra_headers=headers,
                ssl=ssl.create_default_context(),
                ping_interval=30,
                ping_timeout=10
            )
            
            # Send authentication
            await self.authenticate_bridge()
            
            logger.info("Connected to cloud successfully")
            self.reconnect_delay = 5  # Reset reconnect delay on successful connection
            
        except Exception as e:
            logger.error(f"Failed to connect to cloud: {e}")
            raise
    
    async def authenticate_bridge(self):
        """Authenticate bridge with cloud"""
        auth_message = {
            'type': 'authenticate',
            'bridge_token': self.bridge_token,
            'bridge_id': self.bridge_id,
            'bridge_name': self.bridge_name,
            'timestamp': time.time()
        }
        
        await self.websocket.send(json.dumps(auth_message))
        
        # Wait for authentication response
        response = await self.websocket.recv()
        auth_response = json.loads(response)
        
        if auth_response.get('type') == 'auth_success':
            logger.info("Bridge authenticated successfully")
        else:
            raise Exception(f"Authentication failed: {auth_response.get('message', 'Unknown error')}")
    
    async def device_discovery_loop(self):
        """Continuously discover connected devices"""
        while self.running:
            try:
                # Discover iOS devices
                ios_devices = await self.discover_ios_devices()
                
                # Discover Android devices
                android_devices = await self.discover_android_devices()
                
                # Update device registry
                current_devices = ios_devices + android_devices
                self.devices = {d['udid']: d for d in current_devices}
                
                # Send device update to cloud
                if self.websocket and not self.websocket.closed:
                    await self.send_device_update(current_devices)
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"Device discovery error: {e}")
                await asyncio.sleep(15)
    
    async def discover_ios_devices(self):
        """Discover iOS devices using idevice tools"""
        devices = []
        try:
            # Get connected iOS devices
            result = await asyncio.create_subprocess_exec(
                'idevice_id', '-l',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await result.communicate()
            
            if result.returncode == 0:
                udids = stdout.decode().strip().split('\n')
                for udid in udids:
                    if udid.strip():
                        device_info = await self.get_ios_device_info(udid.strip())
                        if device_info:
                            devices.append(device_info)
            else:
                logger.debug(f"idevice_id failed: {stderr.decode()}")
                        
        except FileNotFoundError:
            logger.debug("idevice tools not found - iOS support disabled")
        except Exception as e:
            logger.error(f"iOS discovery failed: {e}")
            
        return devices
    
    async def discover_android_devices(self):
        """Discover Android devices using ADB"""
        devices = []
        try:
            # Get connected Android devices
            result = await asyncio.create_subprocess_exec(
                'adb', 'devices',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await result.communicate()
            
            if result.returncode == 0:
                lines = stdout.decode().split('\n')[1:]  # Skip header
                for line in lines:
                    if '\tdevice' in line:
                        udid = line.split('\t')[0].strip()
                        device_info = await self.get_android_device_info(udid)
                        if device_info:
                            devices.append(device_info)
            else:
                logger.debug(f"adb devices failed: {stderr.decode()}")
                        
        except FileNotFoundError:
            logger.debug("ADB not found - Android support disabled")
        except Exception as e:
            logger.error(f"Android discovery failed: {e}")
            
        return devices
    
    async def get_ios_device_info(self, udid):
        """Get detailed iOS device information"""
        try:
            # Get device name
            name_result = await asyncio.create_subprocess_exec(
                'ideviceinfo', '-u', udid, '-k', 'DeviceName',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            name_stdout, _ = await name_result.communicate()
            
            # Get device model
            model_result = await asyncio.create_subprocess_exec(
                'ideviceinfo', '-u', udid, '-k', 'ProductType',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            model_stdout, _ = await model_result.communicate()
            
            # Get iOS version
            version_result = await asyncio.create_subprocess_exec(
                'ideviceinfo', '-u', udid, '-k', 'ProductVersion',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            version_stdout, _ = await version_result.communicate()
            
            return {
                'udid': udid,
                'name': name_stdout.decode().strip() or f"iOS Device {udid[:8]}",
                'model': model_stdout.decode().strip() or "Unknown",
                'platform': 'ios',
                'os_version': version_stdout.decode().strip() or "Unknown",
                'status': 'connected',
                'bridge_id': self.bridge_id,
                'capabilities': {
                    'screenshot': True,
                    'app_install': True,
                    'automation': True
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get iOS device info for {udid}: {e}")
            return None
    
    async def get_android_device_info(self, udid):
        """Get detailed Android device information"""
        try:
            # Get device model
            model_result = await asyncio.create_subprocess_exec(
                'adb', '-s', udid, 'shell', 'getprop', 'ro.product.model',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            model_stdout, _ = await model_result.communicate()
            
            # Get Android version
            version_result = await asyncio.create_subprocess_exec(
                'adb', '-s', udid, 'shell', 'getprop', 'ro.build.version.release',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            version_stdout, _ = await version_result.communicate()
            
            # Get device manufacturer
            manufacturer_result = await asyncio.create_subprocess_exec(
                'adb', '-s', udid, 'shell', 'getprop', 'ro.product.manufacturer',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            manufacturer_stdout, _ = await manufacturer_result.communicate()
            
            model = model_stdout.decode().strip()
            manufacturer = manufacturer_stdout.decode().strip()
            device_name = f"{manufacturer} {model}".strip() or f"Android Device {udid[:8]}"
            
            return {
                'udid': udid,
                'name': device_name,
                'model': model or "Unknown",
                'platform': 'android',
                'os_version': version_stdout.decode().strip() or "Unknown",
                'manufacturer': manufacturer or "Unknown",
                'status': 'connected',
                'bridge_id': self.bridge_id,
                'capabilities': {
                    'screenshot': True,
                    'app_install': True,
                    'automation': True
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get Android device info for {udid}: {e}")
            return None
    
    async def send_device_update(self, devices):
        """Send device list update to cloud"""
        message = {
            'type': 'device_update',
            'bridge_id': self.bridge_id,
            'devices': devices,
            'timestamp': time.time()
        }
        
        try:
            await self.websocket.send(json.dumps(message))
            logger.debug(f"Sent device update: {len(devices)} devices")
        except Exception as e:
            logger.error(f"Failed to send device update: {e}")
    
    async def process_cloud_commands(self):
        """Process commands from cloud application"""
        try:
            async for message in self.websocket:
                try:
                    command = json.loads(message)
                    await self.handle_command(command)
                except json.JSONDecodeError as e:
                    logger.error(f"Invalid JSON received: {e}")
                except Exception as e:
                    logger.error(f"Error processing command: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.warning("Cloud connection closed")
            raise
        except Exception as e:
            logger.error(f"Error in command processing loop: {e}")
            raise
    
    async def handle_command(self, command):
        """Handle specific device commands"""
        command_type = command.get('type')
        device_udid = command.get('device_udid')
        command_id = command.get('command_id', str(uuid.uuid4()))
        
        logger.info(f"Received command: {command_type} for device {device_udid}")
        
        try:
            if command_type == 'screenshot':
                screenshot_data = await self.take_screenshot(device_udid)
                await self.send_response({
                    'type': 'screenshot_response',
                    'command_id': command_id,
                    'device_udid': device_udid,
                    'success': screenshot_data is not None,
                    'data': screenshot_data
                })
                
            elif command_type == 'install_app':
                app_path = command.get('app_path')
                success = await self.install_app(device_udid, app_path)
                await self.send_response({
                    'type': 'install_response',
                    'command_id': command_id,
                    'device_udid': device_udid,
                    'success': success
                })
                
            elif command_type == 'run_test':
                test_config = command.get('test_config')
                result = await self.run_test(device_udid, test_config)
                await self.send_response({
                    'type': 'test_response',
                    'command_id': command_id,
                    'device_udid': device_udid,
                    'result': result
                })
                
            elif command_type == 'ping':
                await self.send_response({
                    'type': 'pong',
                    'command_id': command_id,
                    'timestamp': time.time()
                })
                
            else:
                logger.warning(f"Unknown command type: {command_type}")
                await self.send_response({
                    'type': 'error',
                    'command_id': command_id,
                    'error': f'Unknown command type: {command_type}'
                })
                
        except Exception as e:
            logger.error(f"Error handling command {command_type}: {e}")
            await self.send_response({
                'type': 'error',
                'command_id': command_id,
                'error': str(e)
            })
    
    async def send_response(self, response):
        """Send response back to cloud"""
        try:
            await self.websocket.send(json.dumps(response))
        except Exception as e:
            logger.error(f"Failed to send response: {e}")
    
    async def take_screenshot(self, device_udid):
        """Take screenshot of device"""
        device = self.devices.get(device_udid)
        if not device:
            logger.error(f"Device {device_udid} not found")
            return None
        
        try:
            if device['platform'] == 'ios':
                return await self.take_ios_screenshot(device_udid)
            elif device['platform'] == 'android':
                return await self.take_android_screenshot(device_udid)
        except Exception as e:
            logger.error(f"Screenshot failed for {device_udid}: {e}")
            return None
    
    async def take_ios_screenshot(self, device_udid):
        """Take iOS screenshot using idevicescreenshot"""
        try:
            # Create temp file
            temp_file = f"/tmp/screenshot_{device_udid}_{int(time.time())}.png"
            
            result = await asyncio.create_subprocess_exec(
                'idevicescreenshot', '-u', device_udid, temp_file,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            await result.communicate()
            
            if result.returncode == 0 and os.path.exists(temp_file):
                # Read and encode screenshot
                with open(temp_file, 'rb') as f:
                    import base64
                    screenshot_data = base64.b64encode(f.read()).decode('utf-8')
                
                # Clean up temp file
                os.remove(temp_file)
                
                return screenshot_data
            
            return None
            
        except Exception as e:
            logger.error(f"iOS screenshot error: {e}")
            return None
    
    async def take_android_screenshot(self, device_udid):
        """Take Android screenshot using adb"""
        try:
            # Take screenshot on device
            result = await asyncio.create_subprocess_exec(
                'adb', '-s', device_udid, 'shell', 'screencap', '-p', '/sdcard/screenshot.png',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            await result.communicate()
            
            if result.returncode == 0:
                # Pull screenshot from device
                temp_file = f"/tmp/screenshot_{device_udid}_{int(time.time())}.png"
                
                pull_result = await asyncio.create_subprocess_exec(
                    'adb', '-s', device_udid, 'pull', '/sdcard/screenshot.png', temp_file,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                await pull_result.communicate()
                
                if pull_result.returncode == 0 and os.path.exists(temp_file):
                    # Read and encode screenshot
                    with open(temp_file, 'rb') as f:
                        import base64
                        screenshot_data = base64.b64encode(f.read()).decode('utf-8')
                    
                    # Clean up
                    os.remove(temp_file)
                    
                    # Clean up device screenshot
                    await asyncio.create_subprocess_exec(
                        'adb', '-s', device_udid, 'shell', 'rm', '/sdcard/screenshot.png'
                    )
                    
                    return screenshot_data
            
            return None
            
        except Exception as e:
            logger.error(f"Android screenshot error: {e}")
            return None
    
    async def install_app(self, device_udid, app_path):
        """Install app on device"""
        device = self.devices.get(device_udid)
        if not device:
            return False
        
        try:
            if device['platform'] == 'ios':
                return await self.install_ios_app(device_udid, app_path)
            elif device['platform'] == 'android':
                return await self.install_android_app(device_udid, app_path)
        except Exception as e:
            logger.error(f"App installation failed for {device_udid}: {e}")
            return False
    
    async def install_ios_app(self, device_udid, app_path):
        """Install iOS app using ideviceinstaller"""
        try:
            result = await asyncio.create_subprocess_exec(
                'ideviceinstaller', '-u', device_udid, '-i', app_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await result.communicate()
            
            if result.returncode == 0:
                logger.info(f"iOS app installed successfully on {device_udid}")
                return True
            else:
                logger.error(f"iOS app installation failed: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.error(f"iOS app installation error: {e}")
            return False
    
    async def install_android_app(self, device_udid, app_path):
        """Install Android app using adb"""
        try:
            result = await asyncio.create_subprocess_exec(
                'adb', '-s', device_udid, 'install', app_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await result.communicate()
            
            if result.returncode == 0 and 'Success' in stdout.decode():
                logger.info(f"Android app installed successfully on {device_udid}")
                return True
            else:
                logger.error(f"Android app installation failed: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.error(f"Android app installation error: {e}")
            return False
    
    async def run_test(self, device_udid, test_config):
        """Run test on device"""
        # This would integrate with the existing test execution framework
        # For now, return a placeholder result
        logger.info(f"Running test on {device_udid}: {test_config.get('test_name', 'Unknown')}")
        
        # Simulate test execution
        await asyncio.sleep(2)
        
        return {
            'status': 'completed',
            'test_name': test_config.get('test_name', 'Unknown'),
            'duration': 2.0,
            'passed': True,
            'message': 'Test executed successfully (placeholder)'
        }

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    logger.info(f"Received signal {signum}, shutting down gracefully...")
    sys.exit(0)

async def main():
    parser = argparse.ArgumentParser(description='Local Device Bridge for Mobile Automation')
    parser.add_argument('bridge_token', help='Bridge authentication token')
    parser.add_argument('cloud_endpoint', help='Cloud endpoint (e.g., your-domain.com)')
    parser.add_argument('--bridge-name', help='Custom bridge name')
    parser.add_argument('--log-level', default='INFO', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'])
    
    args = parser.parse_args()
    
    # Set log level
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create and start bridge
    bridge = LocalDeviceBridge(
        bridge_token=args.bridge_token,
        cloud_endpoint=args.cloud_endpoint,
        bridge_name=args.bridge_name
    )
    
    try:
        await bridge.start_bridge()
    except KeyboardInterrupt:
        logger.info("Bridge stopped by user")
    except Exception as e:
        logger.error(f"Bridge failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
