#!/usr/bin/env python3
"""
Mobile Automation Bridge Installer
Automated installer for setting up local device bridge with dependencies
"""

import os
import sys
import subprocess
import platform
import shutil
import urllib.request
import json
import tempfile
import zipfile
import tarfile
from pathlib import Path
import argparse
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BridgeInstaller:
    def __init__(self, install_dir=None, cloud_endpoint=None, bridge_token=None):
        self.system = platform.system().lower()
        self.arch = platform.machine().lower()
        self.install_dir = Path(install_dir or self.get_default_install_dir())
        self.cloud_endpoint = cloud_endpoint
        self.bridge_token = bridge_token
        
        # Tool versions
        self.cloudflared_version = "2023.8.2"
        
    def get_default_install_dir(self):
        """Get default installation directory"""
        if self.system == "windows":
            return Path.home() / "MobileAutomationBridge"
        else:
            return Path.home() / ".mobile-automation-bridge"
    
    def check_system_requirements(self):
        """Check if system meets requirements"""
        logger.info("Checking system requirements...")
        
        # Check Python version
        if sys.version_info < (3, 8):
            raise Exception("Python 3.8 or higher is required")
        
        # Check if running as admin on Windows
        if self.system == "windows":
            try:
                import ctypes
                if not ctypes.windll.shell32.IsUserAnAdmin():
                    logger.warning("Administrator privileges recommended on Windows")
            except:
                pass
        
        logger.info("✓ System requirements met")
    
    def install_python_dependencies(self):
        """Install Python dependencies"""
        logger.info("Installing Python dependencies...")
        
        requirements = [
            "websockets>=11.0.3",
            "asyncio-mqtt>=0.13.0",
            "requests>=2.31.0",
            "click>=8.1.7",
            "python-dotenv>=1.0.0",
            "psutil>=5.9.5"
        ]
        
        for requirement in requirements:
            try:
                subprocess.run([
                    sys.executable, "-m", "pip", "install", requirement
                ], check=True, capture_output=True)
                logger.info(f"✓ Installed {requirement}")
            except subprocess.CalledProcessError as e:
                logger.error(f"Failed to install {requirement}: {e}")
                raise
    
    def install_mobile_tools(self):
        """Install mobile development tools"""
        logger.info("Installing mobile development tools...")
        
        if self.system == "darwin":  # macOS
            self.install_macos_tools()
        elif self.system == "linux":
            self.install_linux_tools()
        elif self.system == "windows":
            self.install_windows_tools()
        else:
            logger.warning(f"Unsupported system: {self.system}")
    
    def install_macos_tools(self):
        """Install tools on macOS"""
        # Check if Homebrew is installed
        if not shutil.which("brew"):
            logger.info("Installing Homebrew...")
            subprocess.run([
                "/bin/bash", "-c",
                "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
            ], check=True)
        
        # Install libimobiledevice for iOS support
        try:
            subprocess.run(["brew", "install", "libimobiledevice"], check=True)
            logger.info("✓ Installed libimobiledevice (iOS support)")
        except subprocess.CalledProcessError:
            logger.warning("Failed to install libimobiledevice - iOS support may be limited")
        
        # Install Android platform tools
        try:
            subprocess.run(["brew", "install", "android-platform-tools"], check=True)
            logger.info("✓ Installed Android platform tools")
        except subprocess.CalledProcessError:
            logger.warning("Failed to install Android platform tools - Android support may be limited")
    
    def install_linux_tools(self):
        """Install tools on Linux"""
        # Detect package manager
        if shutil.which("apt-get"):
            self.install_debian_tools()
        elif shutil.which("yum"):
            self.install_redhat_tools()
        elif shutil.which("pacman"):
            self.install_arch_tools()
        else:
            logger.warning("Unknown package manager - manual tool installation required")
    
    def install_debian_tools(self):
        """Install tools on Debian/Ubuntu"""
        try:
            # Update package list
            subprocess.run(["sudo", "apt-get", "update"], check=True)
            
            # Install libimobiledevice
            subprocess.run([
                "sudo", "apt-get", "install", "-y",
                "libimobiledevice-utils", "ideviceinstaller"
            ], check=True)
            logger.info("✓ Installed libimobiledevice tools")
            
            # Install ADB
            subprocess.run([
                "sudo", "apt-get", "install", "-y", "android-tools-adb"
            ], check=True)
            logger.info("✓ Installed Android tools")
            
        except subprocess.CalledProcessError as e:
            logger.warning(f"Failed to install some tools: {e}")
    
    def install_redhat_tools(self):
        """Install tools on RedHat/CentOS/Fedora"""
        try:
            # Install libimobiledevice
            subprocess.run([
                "sudo", "yum", "install", "-y", "libimobiledevice-utils"
            ], check=True)
            logger.info("✓ Installed libimobiledevice tools")
            
            # Install ADB (may need EPEL repository)
            subprocess.run([
                "sudo", "yum", "install", "-y", "android-tools"
            ], check=True)
            logger.info("✓ Installed Android tools")
            
        except subprocess.CalledProcessError as e:
            logger.warning(f"Failed to install some tools: {e}")
    
    def install_arch_tools(self):
        """Install tools on Arch Linux"""
        try:
            # Install libimobiledevice
            subprocess.run([
                "sudo", "pacman", "-S", "--noconfirm", "libimobiledevice"
            ], check=True)
            logger.info("✓ Installed libimobiledevice tools")
            
            # Install ADB
            subprocess.run([
                "sudo", "pacman", "-S", "--noconfirm", "android-tools"
            ], check=True)
            logger.info("✓ Installed Android tools")
            
        except subprocess.CalledProcessError as e:
            logger.warning(f"Failed to install some tools: {e}")
    
    def install_windows_tools(self):
        """Install tools on Windows"""
        logger.info("Windows tool installation requires manual setup:")
        logger.info("1. Install iTunes for iOS device support")
        logger.info("2. Install Android SDK Platform Tools for ADB")
        logger.info("3. Add tools to PATH environment variable")
        
        # Download and install cloudflared
        self.install_cloudflared_windows()
    
    def install_cloudflared(self):
        """Install Cloudflare Tunnel (cloudflared)"""
        logger.info("Installing Cloudflare Tunnel...")
        
        if self.system == "darwin":
            self.install_cloudflared_macos()
        elif self.system == "linux":
            self.install_cloudflared_linux()
        elif self.system == "windows":
            self.install_cloudflared_windows()
    
    def install_cloudflared_macos(self):
        """Install cloudflared on macOS"""
        try:
            subprocess.run(["brew", "install", "cloudflared"], check=True)
            logger.info("✓ Installed cloudflared")
        except subprocess.CalledProcessError:
            logger.warning("Failed to install cloudflared via Homebrew, trying manual installation")
            self.download_cloudflared_binary("darwin", "amd64")
    
    def install_cloudflared_linux(self):
        """Install cloudflared on Linux"""
        arch_map = {
            "x86_64": "amd64",
            "aarch64": "arm64",
            "armv7l": "arm"
        }
        
        arch = arch_map.get(self.arch, "amd64")
        self.download_cloudflared_binary("linux", arch)
    
    def install_cloudflared_windows(self):
        """Install cloudflared on Windows"""
        self.download_cloudflared_binary("windows", "amd64")
    
    def download_cloudflared_binary(self, os_name, arch):
        """Download cloudflared binary"""
        try:
            ext = ".exe" if os_name == "windows" else ""
            filename = f"cloudflared-{os_name}-{arch}{ext}"
            url = f"https://github.com/cloudflare/cloudflared/releases/download/{self.cloudflared_version}/{filename}"
            
            # Create bin directory
            bin_dir = self.install_dir / "bin"
            bin_dir.mkdir(parents=True, exist_ok=True)
            
            # Download binary
            binary_path = bin_dir / f"cloudflared{ext}"
            logger.info(f"Downloading cloudflared from {url}")
            
            urllib.request.urlretrieve(url, binary_path)
            
            # Make executable on Unix systems
            if os_name != "windows":
                os.chmod(binary_path, 0o755)
            
            logger.info(f"✓ Installed cloudflared to {binary_path}")
            
        except Exception as e:
            logger.error(f"Failed to download cloudflared: {e}")
            raise
    
    def setup_bridge_files(self):
        """Setup bridge application files"""
        logger.info("Setting up bridge files...")
        
        # Create directory structure
        self.install_dir.mkdir(parents=True, exist_ok=True)
        (self.install_dir / "logs").mkdir(exist_ok=True)
        (self.install_dir / "config").mkdir(exist_ok=True)
        
        # Copy bridge script (this would be downloaded in production)
        bridge_script = self.install_dir / "device_bridge.py"
        
        # In production, this would download from the cloud platform
        # For now, we'll create a placeholder
        bridge_content = '''#!/usr/bin/env python3
# This would be the actual device_bridge.py content
# Downloaded from the cloud platform during installation
import sys
print("Bridge installer placeholder - download actual bridge from cloud platform")
sys.exit(1)
'''
        
        with open(bridge_script, 'w') as f:
            f.write(bridge_content)
        
        os.chmod(bridge_script, 0o755)
        
        logger.info(f"✓ Bridge files setup in {self.install_dir}")
    
    def create_configuration(self):
        """Create bridge configuration"""
        logger.info("Creating bridge configuration...")
        
        config = {
            "cloud_endpoint": self.cloud_endpoint,
            "bridge_token": self.bridge_token,
            "log_level": "INFO",
            "reconnect_delay": 5,
            "device_scan_interval": 10,
            "cloudflare_tunnel": {
                "enabled": False,
                "tunnel_name": "",
                "credentials_file": ""
            }
        }
        
        config_file = self.install_dir / "config" / "bridge.json"
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        logger.info(f"✓ Configuration created: {config_file}")
    
    def create_service_scripts(self):
        """Create service/startup scripts"""
        logger.info("Creating service scripts...")
        
        if self.system == "darwin":
            self.create_macos_service()
        elif self.system == "linux":
            self.create_linux_service()
        elif self.system == "windows":
            self.create_windows_service()
    
    def create_macos_service(self):
        """Create macOS LaunchAgent"""
        plist_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.mobileautomation.bridge</string>
    <key>ProgramArguments</key>
    <array>
        <string>{sys.executable}</string>
        <string>{self.install_dir}/device_bridge.py</string>
        <string>--config</string>
        <string>{self.install_dir}/config/bridge.json</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>StandardOutPath</key>
    <string>{self.install_dir}/logs/bridge.log</string>
    <key>StandardErrorPath</key>
    <string>{self.install_dir}/logs/bridge.error.log</string>
</dict>
</plist>'''
        
        launch_agents_dir = Path.home() / "Library" / "LaunchAgents"
        launch_agents_dir.mkdir(exist_ok=True)
        
        plist_file = launch_agents_dir / "com.mobileautomation.bridge.plist"
        with open(plist_file, 'w') as f:
            f.write(plist_content)
        
        logger.info(f"✓ macOS service created: {plist_file}")
        logger.info("To start the service: launchctl load ~/Library/LaunchAgents/com.mobileautomation.bridge.plist")
    
    def create_linux_service(self):
        """Create systemd service"""
        service_content = f'''[Unit]
Description=Mobile Automation Device Bridge
After=network.target

[Service]
Type=simple
User={os.getenv('USER')}
WorkingDirectory={self.install_dir}
ExecStart={sys.executable} {self.install_dir}/device_bridge.py --config {self.install_dir}/config/bridge.json
Restart=always
RestartSec=10
StandardOutput=append:{self.install_dir}/logs/bridge.log
StandardError=append:{self.install_dir}/logs/bridge.error.log

[Install]
WantedBy=multi-user.target'''
        
        service_file = self.install_dir / "mobile-automation-bridge.service"
        with open(service_file, 'w') as f:
            f.write(service_content)
        
        logger.info(f"✓ systemd service created: {service_file}")
        logger.info(f"To install: sudo cp {service_file} /etc/systemd/system/")
        logger.info("To enable: sudo systemctl enable mobile-automation-bridge")
        logger.info("To start: sudo systemctl start mobile-automation-bridge")
    
    def create_windows_service(self):
        """Create Windows batch script"""
        batch_content = f'''@echo off
cd /d "{self.install_dir}"
"{sys.executable}" device_bridge.py --config config\\bridge.json
pause'''
        
        batch_file = self.install_dir / "start_bridge.bat"
        with open(batch_file, 'w') as f:
            f.write(batch_content)
        
        logger.info(f"✓ Windows startup script created: {batch_file}")
        logger.info("Double-click the batch file to start the bridge")
    
    def setup_cloudflare_tunnel(self, tunnel_name=None):
        """Setup Cloudflare Tunnel (optional)"""
        if not tunnel_name:
            logger.info("Skipping Cloudflare Tunnel setup (not requested)")
            return
        
        logger.info(f"Setting up Cloudflare Tunnel: {tunnel_name}")
        
        try:
            # This would require Cloudflare API token and domain setup
            # For now, just provide instructions
            logger.info("Cloudflare Tunnel setup requires:")
            logger.info("1. Cloudflare account with domain")
            logger.info("2. API token with Zone:Edit permissions")
            logger.info("3. Run: cloudflared tunnel login")
            logger.info(f"4. Run: cloudflared tunnel create {tunnel_name}")
            logger.info("5. Configure tunnel routing in Cloudflare dashboard")
            
        except Exception as e:
            logger.error(f"Cloudflare Tunnel setup failed: {e}")
    
    def verify_installation(self):
        """Verify installation"""
        logger.info("Verifying installation...")
        
        # Check if files exist
        required_files = [
            self.install_dir / "device_bridge.py",
            self.install_dir / "config" / "bridge.json"
        ]
        
        for file_path in required_files:
            if not file_path.exists():
                raise Exception(f"Required file missing: {file_path}")
        
        # Check if tools are available
        tools = ["python3" if self.system != "windows" else "python"]
        
        if self.system == "darwin" or self.system == "linux":
            tools.extend(["idevice_id", "adb"])
        
        missing_tools = []
        for tool in tools:
            if not shutil.which(tool):
                missing_tools.append(tool)
        
        if missing_tools:
            logger.warning(f"Some tools are not in PATH: {missing_tools}")
            logger.warning("Device support may be limited")
        
        logger.info("✓ Installation verification completed")
    
    def print_next_steps(self):
        """Print next steps for user"""
        logger.info("\n" + "="*50)
        logger.info("INSTALLATION COMPLETED SUCCESSFULLY!")
        logger.info("="*50)
        logger.info(f"Installation directory: {self.install_dir}")
        logger.info(f"Configuration file: {self.install_dir}/config/bridge.json")
        logger.info(f"Log directory: {self.install_dir}/logs/")
        
        logger.info("\nNext steps:")
        logger.info("1. Update bridge configuration with your cloud endpoint and token")
        logger.info("2. Connect your iOS/Android devices")
        logger.info("3. Start the bridge service")
        
        if self.system == "darwin":
            logger.info("   macOS: launchctl load ~/Library/LaunchAgents/com.mobileautomation.bridge.plist")
        elif self.system == "linux":
            logger.info("   Linux: sudo systemctl start mobile-automation-bridge")
        elif self.system == "windows":
            logger.info(f"   Windows: Run {self.install_dir}/start_bridge.bat")
        
        logger.info("\n4. Check logs for connection status")
        logger.info("5. Verify devices appear in your cloud dashboard")

def main():
    parser = argparse.ArgumentParser(description='Mobile Automation Bridge Installer')
    parser.add_argument('--install-dir', help='Installation directory')
    parser.add_argument('--cloud-endpoint', help='Cloud platform endpoint')
    parser.add_argument('--bridge-token', help='Bridge authentication token')
    parser.add_argument('--tunnel-name', help='Cloudflare tunnel name (optional)')
    parser.add_argument('--skip-tools', action='store_true', help='Skip mobile tool installation')
    parser.add_argument('--skip-cloudflared', action='store_true', help='Skip cloudflared installation')
    
    args = parser.parse_args()
    
    try:
        installer = BridgeInstaller(
            install_dir=args.install_dir,
            cloud_endpoint=args.cloud_endpoint,
            bridge_token=args.bridge_token
        )
        
        # Run installation steps
        installer.check_system_requirements()
        installer.install_python_dependencies()
        
        if not args.skip_tools:
            installer.install_mobile_tools()
        
        if not args.skip_cloudflared:
            installer.install_cloudflared()
        
        installer.setup_bridge_files()
        installer.create_configuration()
        installer.create_service_scripts()
        
        if args.tunnel_name:
            installer.setup_cloudflare_tunnel(args.tunnel_name)
        
        installer.verify_installation()
        installer.print_next_steps()
        
    except Exception as e:
        logger.error(f"Installation failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
