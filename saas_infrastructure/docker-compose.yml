version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: saas_postgres
    environment:
      POSTGRES_DB: mobile_automation_saas
      POSTGRES_USER: saas_user
      POSTGRES_PASSWORD: saas_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
      - ./database/init_data.sql:/docker-entrypoint-initdb.d/02-init_data.sql:ro
    ports:
      - "5432:5432"
    networks:
      - saas_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U saas_user -d mobile_automation_saas"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for session storage and caching
  redis:
    image: redis:7-alpine
    container_name: saas_redis
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - saas_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # SaaS Application
  saas_app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: saas_app
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=1
      - DATABASE_URL=**************************************************/mobile_automation_saas
      - REDIS_URL=redis://:redis_password@redis:6379/0
      - FLASK_SECRET_KEY=dev_secret_key_change_in_production
      - JWT_SECRET_KEY=dev_jwt_secret_change_in_production
      - CLOUDFLARE_TUNNEL_TOKEN=${CLOUDFLARE_TUNNEL_TOKEN:-}
    volumes:
      - ./app:/app/app:ro
      - ./bridge:/app/bridge:ro
      - ./database:/app/database:ro
      - app_logs:/app/logs
      - app_data:/app/data
    ports:
      - "5000:5000"
      - "5001:5001"  # WebSocket port
    networks:
      - saas_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: saas_nginx
    volumes:
      - ./deployment/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./deployment/nginx-sites:/etc/nginx/conf.d:ro
      - nginx_logs:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    networks:
      - saas_network
    depends_on:
      - saas_app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Cloudflared Tunnel (optional)
  cloudflared:
    image: cloudflare/cloudflared:latest
    container_name: saas_cloudflared
    command: tunnel --no-autoupdate run --token ${CLOUDFLARE_TUNNEL_TOKEN}
    networks:
      - saas_network
    depends_on:
      - nginx
    restart: unless-stopped
    profiles:
      - tunnel
    environment:
      - TUNNEL_TOKEN=${CLOUDFLARE_TUNNEL_TOKEN}

  # Database backup service
  db_backup:
    image: postgres:15-alpine
    container_name: saas_db_backup
    environment:
      PGPASSWORD: saas_password
    volumes:
      - ./backups:/backups
      - ./deployment/backup.sh:/backup.sh:ro
    command: >
      sh -c "
        while true; do
          sleep 86400;
          pg_dump -h postgres -U saas_user -d mobile_automation_saas > /backups/backup_$$(date +%Y%m%d_%H%M%S).sql;
          find /backups -name '*.sql' -mtime +7 -delete;
        done
      "
    networks:
      - saas_network
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    profiles:
      - backup

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: saas_prometheus
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - saas_network
    restart: unless-stopped
    profiles:
      - monitoring

  # Grafana for dashboards (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: saas_grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin_password_change_me
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning:ro
    ports:
      - "3000:3000"
    networks:
      - saas_network
    depends_on:
      - prometheus
    restart: unless-stopped
    profiles:
      - monitoring

  # Log aggregation with Loki (optional)
  loki:
    image: grafana/loki:latest
    container_name: saas_loki
    volumes:
      - ./monitoring/loki.yml:/etc/loki/local-config.yaml:ro
      - loki_data:/loki
    ports:
      - "3100:3100"
    networks:
      - saas_network
    restart: unless-stopped
    profiles:
      - logging

  # Log shipping with Promtail (optional)
  promtail:
    image: grafana/promtail:latest
    container_name: saas_promtail
    volumes:
      - ./monitoring/promtail.yml:/etc/promtail/config.yml:ro
      - app_logs:/var/log/app:ro
      - nginx_logs:/var/log/nginx:ro
    networks:
      - saas_network
    depends_on:
      - loki
    restart: unless-stopped
    profiles:
      - logging

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_logs:
    driver: local
  app_data:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  loki_data:
    driver: local

networks:
  saas_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
